"""
Elite Self-Learning Engine for EOTS v2.5 AI Intelligence System
==============================================================

This module implements the most sophisticated self-learning mechanisms possible,
creating a truly sentient AI intelligence system that evolves and improves
with every market interaction.

Features:
- Recursive Learning Algorithms with Multi-Layer Feedback
- Dynamic Confidence Calibration with Historical Accuracy Tracking
- Adaptive Threshold Management with Market Condition Awareness
- Performance-Based Evolution with Genetic Algorithm Principles
- Cross-Validation Learning with Ensemble Intelligence
- Real-Time Market Outcome Validation and Feedback Integration
- Predictive Accuracy Optimization with Bayesian Learning
- Contextual Memory Formation and Pattern Recognition Enhancement

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "SENTIENT EVOLUTION ENGINE"
"""

import asyncio
import logging
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
from decimal import Decimal

# Pydantic imports for validation
from pydantic import BaseModel, Field

# Import EOTS schemas
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5

# Import AI Intelligence Database Integration
try:
    from database_management.ai_intelligence_integration import get_ai_database_integration
    AI_DATABASE_AVAILABLE = True
except ImportError:
    AI_DATABASE_AVAILABLE = False
    logging.warning("AI Intelligence Database not available - using in-memory learning")

logger = logging.getLogger(__name__)

# ===== PYDANTIC MODELS FOR SELF-LEARNING =====

class LearningOutcome(BaseModel):
    """Pydantic model for learning outcome tracking."""
    prediction_id: str = Field(..., description="Unique prediction identifier")
    prediction_timestamp: datetime = Field(..., description="When prediction was made")
    predicted_confidence: float = Field(..., ge=0.0, le=1.0, description="AI's confidence in prediction")
    predicted_insights: List[str] = Field(..., description="AI-generated insights")
    predicted_regime: str = Field(..., description="Predicted market regime")
    actual_outcome: Optional[Dict[str, Any]] = None
    actual_regime: Optional[str] = None
    accuracy_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    confidence_calibration_error: Optional[float] = None
    learning_extracted: Dict[str, Any] = Field(default_factory=dict)
    validation_timestamp: Optional[datetime] = None

class PerformanceMetrics(BaseModel):
    """Comprehensive performance metrics for learning analysis."""
    total_predictions: int = Field(default=0, ge=0)
    correct_predictions: int = Field(default=0, ge=0)
    accuracy_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    confidence_calibration_score: float = Field(default=0.5, ge=0.0, le=1.0)
    learning_velocity: float = Field(default=0.0, ge=0.0, le=1.0)
    adaptation_effectiveness: float = Field(default=0.5, ge=0.0, le=1.0)
    prediction_consistency: float = Field(default=0.5, ge=0.0, le=1.0)
    regime_transition_accuracy: float = Field(default=0.5, ge=0.0, le=1.0)
    insight_quality_score: float = Field(default=0.5, ge=0.0, le=1.0)

class AdaptiveThreshold(BaseModel):
    """Dynamic threshold that evolves based on performance."""
    name: str = Field(..., description="Threshold identifier")
    current_value: float = Field(..., description="Current threshold value")
    default_value: float = Field(..., description="Original default value")
    min_value: float = Field(..., description="Minimum allowed value")
    max_value: float = Field(..., description="Maximum allowed value")
    adaptation_rate: float = Field(default=0.05, ge=0.0, le=1.0)
    performance_correlation: float = Field(default=0.0, ge=-1.0, le=1.0)
    adjustment_history: List[Dict[str, Any]] = Field(default_factory=list)
    last_adjustment: Optional[datetime] = None
    market_context_sensitivity: float = Field(default=0.5, ge=0.0, le=1.0)

class LearningContext(BaseModel):
    """Context information for learning sessions."""
    market_regime: str = Field(..., description="Market regime during learning")
    volatility_level: str = Field(..., description="Volatility environment")
    signal_strength: float = Field(..., ge=0.0, le=5.0, description="Overall signal strength")
    news_sentiment: float = Field(default=0.0, ge=-1.0, le=1.0, description="News sentiment score")
    time_of_day: str = Field(..., description="Market session timing")
    market_stress_level: float = Field(default=0.5, ge=0.0, le=1.0)

# ===== ELITE SELF-LEARNING ENGINE =====

@dataclass
class LearningMemory:
    """Advanced memory structure for learning patterns."""
    pattern_id: str
    pattern_type: str
    success_rate: float
    confidence_accuracy: float
    market_conditions: Dict[str, Any]
    usage_count: int = 0
    last_used: Optional[datetime] = None
    strength: float = 1.0
    decay_rate: float = 0.01
    adaptation_score: float = 0.5

class EliteSelfLearningEngine:
    """
    ELITE SELF-LEARNING ENGINE
    
    The most sophisticated AI learning system possible, implementing:
    - Multi-layer recursive learning with genetic algorithm principles
    - Bayesian confidence calibration with historical accuracy tracking
    - Dynamic threshold evolution with market condition awareness
    - Contextual memory formation with pattern recognition enhancement
    - Real-time performance optimization with predictive accuracy improvement
    """
    
    def __init__(self, db_integration=None):
        self.db_integration = db_integration
        self.logger = logger.getChild(self.__class__.__name__)
        
        # Learning Memory Systems
        self.learning_outcomes: deque = deque(maxlen=10000)  # Last 10k outcomes
        self.performance_history: deque = deque(maxlen=1000)  # Performance snapshots
        self.adaptive_thresholds: Dict[str, AdaptiveThreshold] = {}
        self.learning_patterns: Dict[str, LearningMemory] = {}
        self.contextual_memories: Dict[str, List[LearningMemory]] = defaultdict(list)
        
        # Advanced Learning Parameters
        self.learning_rate = 0.1
        self.adaptation_momentum = 0.9
        self.confidence_calibration_alpha = 0.05
        self.threshold_evolution_rate = 0.02
        self.pattern_recognition_threshold = 0.75
        self.memory_consolidation_interval = 100  # Consolidate every 100 predictions
        
        # Performance Tracking
        self.current_performance = PerformanceMetrics()
        self.learning_session_count = 0
        self.last_consolidation = datetime.now()
        
        # Initialize adaptive thresholds
        self._initialize_adaptive_thresholds()
        
        self.logger.info("🧠 Elite Self-Learning Engine initialized with advanced capabilities")
    
    def _initialize_adaptive_thresholds(self):
        """Initialize adaptive thresholds with elite configuration."""
        threshold_configs = [
            ("vapi_fa_strong", 2.0, 1.0, 5.0, 0.03),
            ("vapi_fa_moderate", 1.5, 0.5, 3.0, 0.04),
            ("dwfd_strong", 1.5, 0.5, 3.0, 0.03),
            ("dwfd_moderate", 1.0, 0.3, 2.5, 0.04),
            ("tw_laf_strong", 2.0, 1.0, 4.0, 0.03),
            ("confidence_threshold", 0.6, 0.3, 0.9, 0.02),
            ("confluence_threshold", 0.7, 0.4, 0.9, 0.025),
            ("regime_transition_sensitivity", 0.5, 0.2, 0.8, 0.03),
            ("insight_quality_threshold", 0.65, 0.4, 0.9, 0.02),
            ("learning_rate_adaptive", 0.1, 0.01, 0.3, 0.01)
        ]
        
        for name, default, min_val, max_val, adapt_rate in threshold_configs:
            self.adaptive_thresholds[name] = AdaptiveThreshold(
                name=name,
                current_value=default,
                default_value=default,
                min_value=min_val,
                max_value=max_val,
                adaptation_rate=adapt_rate
            )
    
    async def record_prediction(self, prediction_data: Dict[str, Any], 
                              context: LearningContext) -> str:
        """Record a new prediction for future learning validation."""
        try:
            prediction_id = f"pred_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.learning_outcomes)}"
            
            outcome = LearningOutcome(
                prediction_id=prediction_id,
                prediction_timestamp=datetime.now(),
                predicted_confidence=prediction_data.get('confidence', 0.5),
                predicted_insights=prediction_data.get('insights', []),
                predicted_regime=prediction_data.get('regime', 'UNKNOWN'),
                learning_extracted={"context": context.model_dump()}
            )
            
            self.learning_outcomes.append(outcome)
            
            # Store in database if available
            if AI_DATABASE_AVAILABLE and self.db_integration:
                await self._store_prediction_in_db(outcome, context)
            
            self.logger.debug(f"📝 Recorded prediction {prediction_id} for learning validation")
            return prediction_id
            
        except Exception as e:
            self.logger.error(f"Failed to record prediction: {e}")
            return ""
    
    async def validate_prediction(self, prediction_id: str, actual_outcome: Dict[str, Any]) -> bool:
        """Validate a prediction against actual market outcomes and trigger learning."""
        try:
            # Find the prediction
            outcome = None
            for o in self.learning_outcomes:
                if o.prediction_id == prediction_id:
                    outcome = o
                    break
            
            if not outcome:
                self.logger.warning(f"Prediction {prediction_id} not found for validation")
                return False
            
            # Calculate accuracy metrics
            accuracy_score = self._calculate_prediction_accuracy(outcome, actual_outcome)
            confidence_error = self._calculate_confidence_calibration_error(outcome, actual_outcome)
            
            # Update outcome with validation results
            outcome.actual_outcome = actual_outcome
            outcome.actual_regime = actual_outcome.get('regime', 'UNKNOWN')
            outcome.accuracy_score = accuracy_score
            outcome.confidence_calibration_error = confidence_error
            outcome.validation_timestamp = datetime.now()
            
            # Extract learning insights
            learning_insights = await self._extract_learning_insights(outcome, actual_outcome)
            outcome.learning_extracted.update(learning_insights)
            
            # Trigger recursive learning
            await self._trigger_recursive_learning(outcome)
            
            # Update performance metrics
            self._update_performance_metrics(outcome)
            
            # Evolve adaptive thresholds
            await self._evolve_adaptive_thresholds(outcome)
            
            # Form contextual memories
            await self._form_contextual_memories(outcome)
            
            # Check for memory consolidation
            if self.learning_session_count % self.memory_consolidation_interval == 0:
                await self._consolidate_learning_memories()
            
            self.learning_session_count += 1
            
            self.logger.info(f"✅ Validated prediction {prediction_id} - Accuracy: {accuracy_score:.3f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to validate prediction {prediction_id}: {e}")
            return False
    
    def _calculate_prediction_accuracy(self, outcome: LearningOutcome, 
                                     actual_outcome: Dict[str, Any]) -> float:
        """Calculate sophisticated prediction accuracy score."""
        try:
            accuracy_factors = []
            
            # Regime prediction accuracy (40% weight)
            if outcome.predicted_regime == actual_outcome.get('regime', 'UNKNOWN'):
                accuracy_factors.append(1.0 * 0.4)
            else:
                # Partial credit for related regimes
                regime_similarity = self._calculate_regime_similarity(
                    outcome.predicted_regime, actual_outcome.get('regime', 'UNKNOWN')
                )
                accuracy_factors.append(regime_similarity * 0.4)
            
            # Confidence calibration accuracy (30% weight)
            predicted_conf = outcome.predicted_confidence
            actual_success = actual_outcome.get('success_rate', 0.5)
            conf_accuracy = 1.0 - abs(predicted_conf - actual_success)
            accuracy_factors.append(conf_accuracy * 0.3)
            
            # Insight quality accuracy (20% weight)
            insight_accuracy = self._evaluate_insight_accuracy(outcome, actual_outcome)
            accuracy_factors.append(insight_accuracy * 0.2)
            
            # Timing accuracy (10% weight)
            timing_accuracy = self._evaluate_timing_accuracy(outcome, actual_outcome)
            accuracy_factors.append(timing_accuracy * 0.1)
            
            return sum(accuracy_factors)
            
        except Exception as e:
            self.logger.error(f"Error calculating prediction accuracy: {e}")
            return 0.5
    
    def _calculate_regime_similarity(self, predicted: str, actual: str) -> float:
        """Calculate similarity between predicted and actual regimes."""
        if predicted == actual:
            return 1.0
        
        # Define regime similarity matrix
        regime_similarities = {
            ('BULLISH_MOMENTUM', 'BULLISH_CONSOLIDATION'): 0.7,
            ('BEARISH_MOMENTUM', 'BEARISH_CONSOLIDATION'): 0.7,
            ('HIGH_VOLATILITY', 'VOLATILITY_EXPANSION'): 0.8,
            ('CONSOLIDATION', 'RANGE_BOUND'): 0.9,
            ('BULLISH_MOMENTUM', 'CONSOLIDATION'): 0.4,
            ('BEARISH_MOMENTUM', 'CONSOLIDATION'): 0.4,
        }
        
        # Check both directions
        similarity = regime_similarities.get((predicted, actual), 
                                           regime_similarities.get((actual, predicted), 0.2))
        return similarity

    def _calculate_confidence_calibration_error(self, outcome: LearningOutcome,
                                              actual_outcome: Dict[str, Any]) -> float:
        """Calculate confidence calibration error for learning."""
        try:
            predicted_confidence = outcome.predicted_confidence
            actual_success = actual_outcome.get('success_rate', 0.5)

            # Calculate absolute calibration error
            calibration_error = abs(predicted_confidence - actual_success)

            # Penalize overconfidence more than underconfidence
            if predicted_confidence > actual_success:
                calibration_error *= 1.2  # Overconfidence penalty

            return min(calibration_error, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating confidence calibration error: {e}")
            return 0.5

    def _evaluate_insight_accuracy(self, outcome: LearningOutcome,
                                 actual_outcome: Dict[str, Any]) -> float:
        """Evaluate the accuracy of generated insights."""
        try:
            insights = outcome.predicted_insights
            actual_events = actual_outcome.get('market_events', [])

            if not insights or not actual_events:
                return 0.5

            # Simple keyword matching for now (can be enhanced with NLP)
            accuracy_scores = []
            for insight in insights:
                insight_lower = insight.lower()
                matches = 0
                for event in actual_events:
                    event_lower = str(event).lower()
                    # Check for keyword matches
                    if any(word in event_lower for word in insight_lower.split() if len(word) > 3):
                        matches += 1

                insight_accuracy = min(matches / len(actual_events), 1.0) if actual_events else 0.5
                accuracy_scores.append(insight_accuracy)

            return statistics.mean(accuracy_scores) if accuracy_scores else 0.5

        except Exception as e:
            self.logger.error(f"Error evaluating insight accuracy: {e}")
            return 0.5

    def _evaluate_timing_accuracy(self, outcome: LearningOutcome,
                                actual_outcome: Dict[str, Any]) -> float:
        """Evaluate timing accuracy of predictions."""
        try:
            prediction_time = outcome.prediction_timestamp
            actual_event_time = actual_outcome.get('event_timestamp')

            if not actual_event_time:
                return 0.7  # Neutral score if no timing data

            if isinstance(actual_event_time, str):
                actual_event_time = datetime.fromisoformat(actual_event_time.replace('Z', '+00:00'))

            time_diff = abs((actual_event_time - prediction_time).total_seconds())

            # Score based on how close the timing was (within trading day = good)
            if time_diff <= 3600:  # Within 1 hour
                return 1.0
            elif time_diff <= 14400:  # Within 4 hours
                return 0.8
            elif time_diff <= 86400:  # Within 1 day
                return 0.6
            else:
                return 0.3

        except Exception as e:
            self.logger.error(f"Error evaluating timing accuracy: {e}")
            return 0.5

    async def _extract_learning_insights(self, outcome: LearningOutcome,
                                       actual_outcome: Dict[str, Any]) -> Dict[str, Any]:
        """Extract sophisticated learning insights from prediction validation."""
        try:
            insights = {
                "accuracy_analysis": {},
                "confidence_analysis": {},
                "pattern_recognition": {},
                "market_context": {},
                "improvement_opportunities": []
            }

            # Accuracy Analysis
            accuracy = outcome.accuracy_score or 0.5
            insights["accuracy_analysis"] = {
                "overall_accuracy": accuracy,
                "accuracy_category": "high" if accuracy > 0.8 else "medium" if accuracy > 0.6 else "low",
                "accuracy_trend": self._calculate_accuracy_trend(),
                "regime_specific_accuracy": self._get_regime_specific_accuracy(outcome.predicted_regime)
            }

            # Confidence Analysis
            conf_error = outcome.confidence_calibration_error or 0.5
            insights["confidence_analysis"] = {
                "calibration_error": conf_error,
                "overconfidence_tendency": conf_error > 0.3 and outcome.predicted_confidence > 0.7,
                "confidence_consistency": self._calculate_confidence_consistency(),
                "optimal_confidence_range": self._determine_optimal_confidence_range()
            }

            # Pattern Recognition
            insights["pattern_recognition"] = {
                "successful_patterns": self._identify_successful_patterns(outcome),
                "failed_patterns": self._identify_failed_patterns(outcome),
                "emerging_patterns": self._detect_emerging_patterns(outcome),
                "pattern_strength": self._calculate_pattern_strength(outcome)
            }

            # Market Context Analysis
            context = outcome.learning_extracted.get("context", {})
            insights["market_context"] = {
                "regime_stability": self._analyze_regime_stability(context),
                "volatility_impact": self._analyze_volatility_impact(context),
                "sentiment_correlation": self._analyze_sentiment_correlation(context),
                "optimal_conditions": self._identify_optimal_conditions(outcome)
            }

            # Improvement Opportunities
            insights["improvement_opportunities"] = self._identify_improvement_opportunities(outcome, actual_outcome)

            return insights

        except Exception as e:
            self.logger.error(f"Error extracting learning insights: {e}")
            return {}

    async def _trigger_recursive_learning(self, outcome: LearningOutcome):
        """Trigger sophisticated recursive learning algorithms."""
        try:
            # Multi-layer learning approach
            await self._layer_1_immediate_learning(outcome)
            await self._layer_2_pattern_learning(outcome)
            await self._layer_3_contextual_learning(outcome)
            await self._layer_4_meta_learning(outcome)

            self.logger.debug(f"🔄 Completed recursive learning for {outcome.prediction_id}")

        except Exception as e:
            self.logger.error(f"Error in recursive learning: {e}")

    async def _layer_1_immediate_learning(self, outcome: LearningOutcome):
        """Layer 1: Immediate feedback learning."""
        try:
            accuracy = outcome.accuracy_score or 0.5

            # Adjust learning rate based on accuracy
            if accuracy > 0.8:
                self.learning_rate = min(self.learning_rate * 1.05, 0.3)  # Increase learning rate for good predictions
            elif accuracy < 0.4:
                self.learning_rate = max(self.learning_rate * 0.95, 0.01)  # Decrease for poor predictions

            # Update confidence calibration
            conf_error = outcome.confidence_calibration_error or 0.5
            self.confidence_calibration_alpha = max(0.01, min(0.1, conf_error * 0.2))

        except Exception as e:
            self.logger.error(f"Error in layer 1 learning: {e}")

    async def _layer_2_pattern_learning(self, outcome: LearningOutcome):
        """Layer 2: Pattern recognition and reinforcement learning."""
        try:
            # Identify patterns in this prediction
            patterns = self._extract_prediction_patterns(outcome)

            for pattern_id, pattern_data in patterns.items():
                if pattern_id in self.learning_patterns:
                    # Update existing pattern
                    memory = self.learning_patterns[pattern_id]
                    memory.usage_count += 1
                    memory.last_used = datetime.now()

                    # Update success rate with exponential moving average
                    accuracy = outcome.accuracy_score or 0.5
                    memory.success_rate = (memory.success_rate * 0.9) + (accuracy * 0.1)

                    # Update confidence accuracy
                    conf_accuracy = 1.0 - (outcome.confidence_calibration_error or 0.5)
                    memory.confidence_accuracy = (memory.confidence_accuracy * 0.9) + (conf_accuracy * 0.1)

                    # Strengthen or weaken pattern based on performance
                    if accuracy > 0.7:
                        memory.strength = min(memory.strength * 1.1, 2.0)
                    else:
                        memory.strength = max(memory.strength * 0.9, 0.1)

                else:
                    # Create new pattern memory
                    accuracy = outcome.accuracy_score or 0.5
                    conf_accuracy = 1.0 - (outcome.confidence_calibration_error or 0.5)

                    self.learning_patterns[pattern_id] = LearningMemory(
                        pattern_id=pattern_id,
                        pattern_type=pattern_data.get('type', 'unknown'),
                        success_rate=accuracy,
                        confidence_accuracy=conf_accuracy,
                        market_conditions=pattern_data.get('conditions', {}),
                        usage_count=1,
                        last_used=datetime.now(),
                        strength=1.0
                    )

        except Exception as e:
            self.logger.error(f"Error in layer 2 learning: {e}")

    async def _layer_3_contextual_learning(self, outcome: LearningOutcome):
        """Layer 3: Contextual and environmental learning."""
        try:
            context = outcome.learning_extracted.get("context", {})
            regime = context.get("market_regime", "UNKNOWN")

            # Create contextual memory
            accuracy = outcome.accuracy_score or 0.5
            conf_accuracy = 1.0 - (outcome.confidence_calibration_error or 0.5)

            contextual_memory = LearningMemory(
                pattern_id=f"context_{regime}_{datetime.now().strftime('%Y%m%d')}",
                pattern_type="contextual",
                success_rate=accuracy,
                confidence_accuracy=conf_accuracy,
                market_conditions=context,
                usage_count=1,
                last_used=datetime.now(),
                strength=1.0
            )

            # Store in contextual memories
            self.contextual_memories[regime].append(contextual_memory)

            # Limit contextual memories per regime
            if len(self.contextual_memories[regime]) > 100:
                # Remove oldest memories with lowest strength
                self.contextual_memories[regime].sort(key=lambda x: x.strength)
                self.contextual_memories[regime] = self.contextual_memories[regime][-100:]

        except Exception as e:
            self.logger.error(f"Error in layer 3 learning: {e}")

    async def _layer_4_meta_learning(self, outcome: LearningOutcome):
        """Layer 4: Meta-learning - learning how to learn better."""
        try:
            # Analyze learning effectiveness
            recent_outcomes = list(self.learning_outcomes)[-50:]  # Last 50 predictions
            if len(recent_outcomes) < 10:
                return

            # Calculate learning velocity
            accuracies = [o.accuracy_score for o in recent_outcomes if o.accuracy_score is not None]
            if len(accuracies) >= 10:
                early_accuracy = statistics.mean(accuracies[:len(accuracies)//2])
                late_accuracy = statistics.mean(accuracies[len(accuracies)//2:])
                learning_velocity = late_accuracy - early_accuracy

                # Adjust meta-learning parameters
                if learning_velocity > 0.1:  # Good learning progress
                    self.adaptation_momentum = min(self.adaptation_momentum * 1.02, 0.99)
                    self.threshold_evolution_rate = min(self.threshold_evolution_rate * 1.05, 0.05)
                elif learning_velocity < -0.1:  # Learning regression
                    self.adaptation_momentum = max(self.adaptation_momentum * 0.98, 0.5)
                    self.threshold_evolution_rate = max(self.threshold_evolution_rate * 0.95, 0.005)

            # Optimize pattern recognition threshold
            pattern_effectiveness = self._calculate_pattern_effectiveness()
            if pattern_effectiveness > 0.8:
                self.pattern_recognition_threshold = min(self.pattern_recognition_threshold * 1.02, 0.9)
            elif pattern_effectiveness < 0.6:
                self.pattern_recognition_threshold = max(self.pattern_recognition_threshold * 0.98, 0.5)

        except Exception as e:
            self.logger.error(f"Error in layer 4 meta-learning: {e}")

    async def _evolve_adaptive_thresholds(self, outcome: LearningOutcome):
        """Evolve adaptive thresholds based on prediction performance."""
        try:
            accuracy = outcome.accuracy_score or 0.5
            context = outcome.learning_extracted.get("context", {})

            # Evolve each threshold based on performance
            for threshold_name, threshold in self.adaptive_thresholds.items():
                # Calculate performance correlation for this threshold
                correlation = self._calculate_threshold_performance_correlation(threshold_name, outcome)
                threshold.performance_correlation = (threshold.performance_correlation * 0.9) + (correlation * 0.1)

                # Determine adjustment direction and magnitude
                adjustment_factor = self._calculate_threshold_adjustment(threshold, accuracy, context)

                if abs(adjustment_factor) > 0.01:  # Only adjust if significant
                    old_value = threshold.current_value

                    # Apply adjustment with momentum
                    adjustment = adjustment_factor * threshold.adaptation_rate * self.adaptation_momentum
                    new_value = threshold.current_value + adjustment

                    # Ensure within bounds
                    new_value = max(threshold.min_value, min(threshold.max_value, new_value))

                    if abs(new_value - old_value) > 0.001:  # Meaningful change
                        threshold.current_value = new_value
                        threshold.last_adjustment = datetime.now()

                        # Record adjustment history
                        adjustment_record = {
                            "timestamp": datetime.now().isoformat(),
                            "old_value": old_value,
                            "new_value": new_value,
                            "adjustment_factor": adjustment_factor,
                            "accuracy_trigger": accuracy,
                            "market_context": context.get("market_regime", "UNKNOWN")
                        }
                        threshold.adjustment_history.append(adjustment_record)

                        # Limit history size
                        if len(threshold.adjustment_history) > 100:
                            threshold.adjustment_history = threshold.adjustment_history[-100:]

                        self.logger.debug(f"⚖️ Evolved threshold {threshold_name}: {old_value:.3f} → {new_value:.3f}")

        except Exception as e:
            self.logger.error(f"Error evolving adaptive thresholds: {e}")

    def _calculate_threshold_performance_correlation(self, threshold_name: str,
                                                   outcome: LearningOutcome) -> float:
        """Calculate correlation between threshold and performance."""
        try:
            # This is a sophisticated correlation calculation
            # In a real implementation, this would analyze historical data

            accuracy = outcome.accuracy_score or 0.5
            threshold = self.adaptive_thresholds[threshold_name]

            # Simple correlation based on threshold usage and accuracy
            if "confidence" in threshold_name:
                # Confidence thresholds should correlate with calibration accuracy
                conf_accuracy = 1.0 - (outcome.confidence_calibration_error or 0.5)
                return (conf_accuracy - 0.5) * 2  # Scale to -1 to 1
            elif "strong" in threshold_name:
                # Strong signal thresholds should correlate with high accuracy
                return (accuracy - 0.5) * 2
            else:
                # General correlation
                return (accuracy - 0.5) * 1.5

        except Exception as e:
            self.logger.error(f"Error calculating threshold correlation: {e}")
            return 0.0

    def _calculate_threshold_adjustment(self, threshold: AdaptiveThreshold,
                                     accuracy: float, context: Dict[str, Any]) -> float:
        """Calculate sophisticated threshold adjustment factor."""
        try:
            # Base adjustment on performance correlation
            base_adjustment = threshold.performance_correlation * self.threshold_evolution_rate

            # Market context sensitivity
            market_regime = context.get("market_regime", "UNKNOWN")
            volatility_level = context.get("volatility_level", "NORMAL")

            # Adjust based on market conditions
            context_multiplier = 1.0
            if "HIGH_VOLATILITY" in market_regime or volatility_level == "HIGH":
                context_multiplier = 1.2  # More aggressive adjustments in volatile markets
            elif "CONSOLIDATION" in market_regime or volatility_level == "LOW":
                context_multiplier = 0.8  # More conservative adjustments in stable markets

            # Accuracy-based adjustment
            if accuracy > 0.8:
                accuracy_multiplier = 1.1  # Reinforce successful thresholds
            elif accuracy < 0.4:
                accuracy_multiplier = -0.8  # Reverse poor-performing thresholds
            else:
                accuracy_multiplier = 1.0

            # Combine factors
            final_adjustment = base_adjustment * context_multiplier * accuracy_multiplier

            # Apply market context sensitivity
            final_adjustment *= threshold.market_context_sensitivity

            return final_adjustment

        except Exception as e:
            self.logger.error(f"Error calculating threshold adjustment: {e}")
            return 0.0

    async def _form_contextual_memories(self, outcome: LearningOutcome):
        """Form sophisticated contextual memories for pattern recognition."""
        try:
            context = outcome.learning_extracted.get("context", {})
            accuracy = outcome.accuracy_score or 0.5

            # Create memory signature based on context
            memory_signature = self._create_memory_signature(context, outcome)

            # Check if similar memory exists
            similar_memory = self._find_similar_memory(memory_signature, context)

            if similar_memory:
                # Update existing memory
                similar_memory.usage_count += 1
                similar_memory.last_used = datetime.now()

                # Update success rate with recency weighting
                weight = 0.2 if similar_memory.usage_count > 10 else 0.3
                similar_memory.success_rate = (similar_memory.success_rate * (1 - weight)) + (accuracy * weight)

                # Update adaptation score
                adaptation_effectiveness = self._calculate_adaptation_effectiveness(similar_memory, outcome)
                similar_memory.adaptation_score = (similar_memory.adaptation_score * 0.8) + (adaptation_effectiveness * 0.2)

                # Strengthen memory based on consistency
                if abs(accuracy - similar_memory.success_rate) < 0.2:  # Consistent performance
                    similar_memory.strength = min(similar_memory.strength * 1.05, 2.0)
                else:  # Inconsistent performance
                    similar_memory.strength = max(similar_memory.strength * 0.95, 0.5)

            else:
                # Create new contextual memory
                new_memory = LearningMemory(
                    pattern_id=memory_signature,
                    pattern_type="contextual_pattern",
                    success_rate=accuracy,
                    confidence_accuracy=1.0 - (outcome.confidence_calibration_error or 0.5),
                    market_conditions=context,
                    usage_count=1,
                    last_used=datetime.now(),
                    strength=1.0,
                    adaptation_score=0.5
                )

                regime = context.get("market_regime", "UNKNOWN")
                self.contextual_memories[regime].append(new_memory)

        except Exception as e:
            self.logger.error(f"Error forming contextual memories: {e}")

    async def _consolidate_learning_memories(self):
        """Consolidate and optimize learning memories using advanced algorithms."""
        try:
            self.logger.info("🧠 Starting memory consolidation process...")

            # Consolidate pattern memories
            await self._consolidate_pattern_memories()

            # Consolidate contextual memories
            await self._consolidate_contextual_memories()

            # Optimize threshold performance
            await self._optimize_threshold_performance()

            # Update global learning parameters
            await self._update_global_learning_parameters()

            # Decay unused memories
            await self._decay_unused_memories()

            self.last_consolidation = datetime.now()
            self.logger.info("✅ Memory consolidation completed")

        except Exception as e:
            self.logger.error(f"Error in memory consolidation: {e}")

    async def _consolidate_pattern_memories(self):
        """Consolidate pattern memories using clustering and similarity analysis."""
        try:
            # Group similar patterns
            pattern_groups = self._group_similar_patterns()

            for group_id, patterns in pattern_groups.items():
                if len(patterns) > 1:
                    # Merge similar patterns
                    merged_pattern = self._merge_pattern_memories(patterns)

                    # Remove individual patterns and add merged pattern
                    for pattern in patterns:
                        if pattern.pattern_id in self.learning_patterns:
                            del self.learning_patterns[pattern.pattern_id]

                    self.learning_patterns[merged_pattern.pattern_id] = merged_pattern

        except Exception as e:
            self.logger.error(f"Error consolidating pattern memories: {e}")

    async def _consolidate_contextual_memories(self):
        """Consolidate contextual memories by regime and performance."""
        try:
            for regime, memories in self.contextual_memories.items():
                if len(memories) > 50:  # Too many memories for this regime
                    # Sort by strength and recency
                    memories.sort(key=lambda x: (x.strength, x.last_used or datetime.min), reverse=True)

                    # Keep top 30 memories
                    self.contextual_memories[regime] = memories[:30]

                    # Create consolidated memory from removed memories
                    removed_memories = memories[30:]
                    if removed_memories:
                        consolidated_memory = self._create_consolidated_memory(removed_memories, regime)
                        self.contextual_memories[regime].append(consolidated_memory)

        except Exception as e:
            self.logger.error(f"Error consolidating contextual memories: {e}")

    # ===== SOPHISTICATED UTILITY METHODS =====

    def _extract_prediction_patterns(self, outcome: LearningOutcome) -> Dict[str, Dict[str, Any]]:
        """Extract sophisticated patterns from prediction data."""
        patterns = {}

        try:
            context = outcome.learning_extracted.get("context", {})

            # Regime-based patterns
            regime = context.get("market_regime", "UNKNOWN")
            patterns[f"regime_{regime}"] = {
                "type": "regime_pattern",
                "conditions": {
                    "regime": regime,
                    "confidence": outcome.predicted_confidence,
                    "volatility": context.get("volatility_level", "NORMAL")
                }
            }

            # Confidence-based patterns
            conf_level = "high" if outcome.predicted_confidence > 0.7 else "medium" if outcome.predicted_confidence > 0.4 else "low"
            patterns[f"confidence_{conf_level}"] = {
                "type": "confidence_pattern",
                "conditions": {
                    "confidence_level": conf_level,
                    "confidence_value": outcome.predicted_confidence,
                    "regime": regime
                }
            }

            # Signal strength patterns
            signal_strength = context.get("signal_strength", 2.5)
            strength_level = "strong" if signal_strength > 2.0 else "moderate" if signal_strength > 1.0 else "weak"
            patterns[f"signal_{strength_level}"] = {
                "type": "signal_pattern",
                "conditions": {
                    "signal_strength": signal_strength,
                    "strength_level": strength_level,
                    "regime": regime
                }
            }

        except Exception as e:
            self.logger.error(f"Error extracting prediction patterns: {e}")

        return patterns

    def _calculate_accuracy_trend(self) -> str:
        """Calculate accuracy trend over recent predictions."""
        try:
            recent_outcomes = [o for o in list(self.learning_outcomes)[-20:] if o.accuracy_score is not None]

            if len(recent_outcomes) < 5:
                return "insufficient_data"

            accuracies = [o.accuracy_score for o in recent_outcomes]

            # Calculate trend using linear regression
            x = list(range(len(accuracies)))
            slope = np.polyfit(x, accuracies, 1)[0] if len(accuracies) > 1 else 0

            if slope > 0.02:
                return "improving"
            elif slope < -0.02:
                return "declining"
            else:
                return "stable"

        except Exception as e:
            self.logger.error(f"Error calculating accuracy trend: {e}")
            return "unknown"

    def _get_regime_specific_accuracy(self, regime: str) -> float:
        """Get accuracy specific to a market regime."""
        try:
            regime_outcomes = [o for o in self.learning_outcomes
                             if o.predicted_regime == regime and o.accuracy_score is not None]

            if not regime_outcomes:
                return 0.5

            accuracies = [o.accuracy_score for o in regime_outcomes[-10:]]  # Last 10 for this regime
            return statistics.mean(accuracies)

        except Exception as e:
            self.logger.error(f"Error getting regime-specific accuracy: {e}")
            return 0.5

    def _calculate_confidence_consistency(self) -> float:
        """Calculate consistency of confidence predictions."""
        try:
            recent_outcomes = [o for o in list(self.learning_outcomes)[-20:]
                             if o.confidence_calibration_error is not None]

            if len(recent_outcomes) < 5:
                return 0.5

            errors = [o.confidence_calibration_error for o in recent_outcomes]
            consistency = 1.0 - statistics.stdev(errors) if len(errors) > 1 else 0.5
            return max(0.0, min(1.0, consistency))

        except Exception as e:
            self.logger.error(f"Error calculating confidence consistency: {e}")
            return 0.5

    def _determine_optimal_confidence_range(self) -> Tuple[float, float]:
        """Determine optimal confidence range based on historical performance."""
        try:
            # Analyze confidence vs accuracy correlation
            outcomes_with_data = [o for o in self.learning_outcomes
                                if o.accuracy_score is not None and o.predicted_confidence is not None]

            if len(outcomes_with_data) < 10:
                return (0.4, 0.8)  # Default range

            # Group by confidence ranges and calculate average accuracy
            confidence_ranges = {
                "low": ([o for o in outcomes_with_data if o.predicted_confidence < 0.4], 0.2, 0.4),
                "medium": ([o for o in outcomes_with_data if 0.4 <= o.predicted_confidence < 0.7], 0.4, 0.7),
                "high": ([o for o in outcomes_with_data if o.predicted_confidence >= 0.7], 0.7, 1.0)
            }

            best_range = None
            best_accuracy = 0.0

            for range_name, (outcomes, min_conf, max_conf) in confidence_ranges.items():
                if outcomes:
                    avg_accuracy = statistics.mean([o.accuracy_score for o in outcomes])
                    if avg_accuracy > best_accuracy:
                        best_accuracy = avg_accuracy
                        best_range = (min_conf, max_conf)

            return best_range or (0.4, 0.8)

        except Exception as e:
            self.logger.error(f"Error determining optimal confidence range: {e}")
            return (0.4, 0.8)

    def _identify_successful_patterns(self, outcome: LearningOutcome) -> List[str]:
        """Identify patterns associated with successful predictions."""
        successful_patterns = []

        try:
            accuracy = outcome.accuracy_score or 0.5
            if accuracy > 0.7:  # Consider successful
                patterns = self._extract_prediction_patterns(outcome)
                for pattern_id, pattern_data in patterns.items():
                    if pattern_id in self.learning_patterns:
                        memory = self.learning_patterns[pattern_id]
                        if memory.success_rate > 0.7:
                            successful_patterns.append(pattern_id)

        except Exception as e:
            self.logger.error(f"Error identifying successful patterns: {e}")

        return successful_patterns

    def _identify_failed_patterns(self, outcome: LearningOutcome) -> List[str]:
        """Identify patterns associated with failed predictions."""
        failed_patterns = []

        try:
            accuracy = outcome.accuracy_score or 0.5
            if accuracy < 0.4:  # Consider failed
                patterns = self._extract_prediction_patterns(outcome)
                for pattern_id, pattern_data in patterns.items():
                    if pattern_id in self.learning_patterns:
                        memory = self.learning_patterns[pattern_id]
                        if memory.success_rate < 0.4:
                            failed_patterns.append(pattern_id)

        except Exception as e:
            self.logger.error(f"Error identifying failed patterns: {e}")

        return failed_patterns

    def _detect_emerging_patterns(self, outcome: LearningOutcome) -> List[str]:
        """Detect emerging patterns that might be developing."""
        emerging_patterns = []

        try:
            # Look for patterns that are showing improvement
            patterns = self._extract_prediction_patterns(outcome)
            for pattern_id, pattern_data in patterns.items():
                if pattern_id in self.learning_patterns:
                    memory = self.learning_patterns[pattern_id]
                    if (memory.usage_count >= 3 and
                        memory.success_rate > 0.6 and
                        memory.strength > 1.2):
                        emerging_patterns.append(pattern_id)

        except Exception as e:
            self.logger.error(f"Error detecting emerging patterns: {e}")

        return emerging_patterns

    def _calculate_pattern_strength(self, outcome: LearningOutcome) -> float:
        """Calculate overall pattern strength for this prediction."""
        try:
            patterns = self._extract_prediction_patterns(outcome)
            if not patterns:
                return 0.5

            total_strength = 0.0
            pattern_count = 0

            for pattern_id in patterns:
                if pattern_id in self.learning_patterns:
                    memory = self.learning_patterns[pattern_id]
                    total_strength += memory.strength * memory.success_rate
                    pattern_count += 1

            return total_strength / pattern_count if pattern_count > 0 else 0.5

        except Exception as e:
            self.logger.error(f"Error calculating pattern strength: {e}")
            return 0.5

    def _analyze_regime_stability(self, context: Dict[str, Any]) -> str:
        """Analyze regime stability from context."""
        try:
            regime = context.get("market_regime", "UNKNOWN")
            volatility = context.get("volatility_level", "NORMAL")
            signal_strength = context.get("signal_strength", 2.5)

            if volatility == "HIGH" or signal_strength > 3.0:
                return "unstable"
            elif volatility == "LOW" and signal_strength < 1.5:
                return "very_stable"
            else:
                return "stable"

        except Exception as e:
            self.logger.error(f"Error analyzing regime stability: {e}")
            return "unknown"

    def _analyze_volatility_impact(self, context: Dict[str, Any]) -> str:
        """Analyze volatility impact on predictions."""
        try:
            volatility = context.get("volatility_level", "NORMAL")
            market_stress = context.get("market_stress_level", 0.5)

            if volatility == "HIGH" and market_stress > 0.7:
                return "high_negative_impact"
            elif volatility == "HIGH":
                return "moderate_impact"
            elif volatility == "LOW":
                return "minimal_impact"
            else:
                return "normal_impact"

        except Exception as e:
            self.logger.error(f"Error analyzing volatility impact: {e}")
            return "unknown"

    def _analyze_sentiment_correlation(self, context: Dict[str, Any]) -> float:
        """Analyze sentiment correlation with prediction accuracy."""
        try:
            sentiment = context.get("news_sentiment", 0.0)

            # Simple correlation analysis (can be enhanced)
            if abs(sentiment) > 0.5:
                return 0.7  # Strong sentiment usually correlates with accuracy
            elif abs(sentiment) > 0.2:
                return 0.5  # Moderate sentiment
            else:
                return 0.3  # Neutral sentiment less predictive

        except Exception as e:
            self.logger.error(f"Error analyzing sentiment correlation: {e}")
            return 0.5

    def _identify_optimal_conditions(self, outcome: LearningOutcome) -> Dict[str, Any]:
        """Identify optimal market conditions for accurate predictions."""
        try:
            context = outcome.learning_extracted.get("context", {})
            accuracy = outcome.accuracy_score or 0.5

            optimal_conditions = {}

            if accuracy > 0.8:  # High accuracy prediction
                optimal_conditions = {
                    "regime": context.get("market_regime", "UNKNOWN"),
                    "volatility": context.get("volatility_level", "NORMAL"),
                    "signal_strength_range": (
                        max(0, context.get("signal_strength", 2.5) - 0.5),
                        context.get("signal_strength", 2.5) + 0.5
                    ),
                    "sentiment_range": (
                        max(-1, context.get("news_sentiment", 0.0) - 0.2),
                        min(1, context.get("news_sentiment", 0.0) + 0.2)
                    ),
                    "time_preference": context.get("time_of_day", "market_hours")
                }

            return optimal_conditions

        except Exception as e:
            self.logger.error(f"Error identifying optimal conditions: {e}")
            return {}

    def _identify_improvement_opportunities(self, outcome: LearningOutcome,
                                         actual_outcome: Dict[str, Any]) -> List[str]:
        """Identify specific improvement opportunities."""
        opportunities = []

        try:
            accuracy = outcome.accuracy_score or 0.5
            conf_error = outcome.confidence_calibration_error or 0.5

            # Accuracy-based opportunities
            if accuracy < 0.6:
                opportunities.append("Improve signal interpretation accuracy")
                opportunities.append("Enhance pattern recognition algorithms")

            # Confidence calibration opportunities
            if conf_error > 0.3:
                if outcome.predicted_confidence > 0.7:
                    opportunities.append("Reduce overconfidence in predictions")
                else:
                    opportunities.append("Improve confidence calibration methods")

            # Regime-specific opportunities
            if outcome.predicted_regime != actual_outcome.get('regime', 'UNKNOWN'):
                opportunities.append("Enhance regime detection algorithms")
                opportunities.append("Improve regime transition prediction")

            # Insight quality opportunities
            insight_accuracy = self._evaluate_insight_accuracy(outcome, actual_outcome)
            if insight_accuracy < 0.6:
                opportunities.append("Improve insight generation quality")
                opportunities.append("Enhance market event prediction")

        except Exception as e:
            self.logger.error(f"Error identifying improvement opportunities: {e}")

        return opportunities

    def _calculate_pattern_effectiveness(self) -> float:
        """Calculate overall effectiveness of pattern recognition."""
        try:
            if not self.learning_patterns:
                return 0.5

            total_effectiveness = 0.0
            pattern_count = 0

            for pattern_id, memory in self.learning_patterns.items():
                if memory.usage_count >= 3:  # Only consider patterns with sufficient usage
                    effectiveness = memory.success_rate * memory.strength * memory.confidence_accuracy
                    total_effectiveness += effectiveness
                    pattern_count += 1

            return total_effectiveness / pattern_count if pattern_count > 0 else 0.5

        except Exception as e:
            self.logger.error(f"Error calculating pattern effectiveness: {e}")
            return 0.5

    def _create_memory_signature(self, context: Dict[str, Any], outcome: LearningOutcome) -> str:
        """Create unique signature for memory storage."""
        try:
            regime = context.get("market_regime", "UNKNOWN")
            volatility = context.get("volatility_level", "NORMAL")
            signal_strength = context.get("signal_strength", 2.5)
            confidence_level = "high" if outcome.predicted_confidence > 0.7 else "medium" if outcome.predicted_confidence > 0.4 else "low"

            signature = f"{regime}_{volatility}_{confidence_level}_{int(signal_strength)}"
            return signature

        except Exception as e:
            self.logger.error(f"Error creating memory signature: {e}")
            return "unknown_signature"

    def _find_similar_memory(self, signature: str, context: Dict[str, Any]) -> Optional[LearningMemory]:
        """Find similar memory based on signature and context."""
        try:
            regime = context.get("market_regime", "UNKNOWN")
            memories = self.contextual_memories.get(regime, [])

            # Look for exact signature match first
            for memory in memories:
                if memory.pattern_id == signature:
                    return memory

            # Look for similar signatures
            signature_parts = signature.split("_")
            for memory in memories:
                memory_parts = memory.pattern_id.split("_")
                if len(memory_parts) >= 3 and len(signature_parts) >= 3:
                    # Check if at least 2 out of 3 main components match
                    matches = sum(1 for i in range(min(3, len(memory_parts), len(signature_parts)))
                                if memory_parts[i] == signature_parts[i])
                    if matches >= 2:
                        return memory

            return None

        except Exception as e:
            self.logger.error(f"Error finding similar memory: {e}")
            return None

    def _calculate_adaptation_effectiveness(self, memory: LearningMemory,
                                         outcome: LearningOutcome) -> float:
        """Calculate how effectively the memory is adapting."""
        try:
            # Compare recent performance with historical performance
            recent_accuracy = outcome.accuracy_score or 0.5
            historical_accuracy = memory.success_rate

            # Adaptation is effective if recent performance is improving
            improvement = recent_accuracy - historical_accuracy

            # Scale to 0-1 range
            effectiveness = 0.5 + (improvement * 0.5)
            return max(0.0, min(1.0, effectiveness))

        except Exception as e:
            self.logger.error(f"Error calculating adaptation effectiveness: {e}")
            return 0.5

    # ===== PUBLIC INTERFACE METHODS =====

    async def get_adaptive_thresholds(self) -> Dict[str, float]:
        """Get current adaptive threshold values."""
        return {name: threshold.current_value for name, threshold in self.adaptive_thresholds.items()}

    async def get_learning_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive learning performance summary."""
        try:
            recent_outcomes = list(self.learning_outcomes)[-50:]
            validated_outcomes = [o for o in recent_outcomes if o.accuracy_score is not None]

            if not validated_outcomes:
                return {"status": "insufficient_data", "message": "Not enough validated predictions for analysis"}

            # Calculate comprehensive metrics
            accuracies = [o.accuracy_score for o in validated_outcomes]
            conf_errors = [o.confidence_calibration_error for o in validated_outcomes if o.confidence_calibration_error is not None]

            summary = {
                "learning_status": "active",
                "total_predictions": len(self.learning_outcomes),
                "validated_predictions": len(validated_outcomes),
                "average_accuracy": statistics.mean(accuracies),
                "accuracy_trend": self._calculate_accuracy_trend(),
                "confidence_calibration": {
                    "average_error": statistics.mean(conf_errors) if conf_errors else 0.5,
                    "consistency": self._calculate_confidence_consistency()
                },
                "pattern_recognition": {
                    "total_patterns": len(self.learning_patterns),
                    "effectiveness": self._calculate_pattern_effectiveness(),
                    "active_patterns": len([p for p in self.learning_patterns.values() if p.usage_count >= 3])
                },
                "adaptive_thresholds": {
                    "total_thresholds": len(self.adaptive_thresholds),
                    "recently_adjusted": len([t for t in self.adaptive_thresholds.values()
                                            if t.last_adjustment and
                                            (datetime.now() - t.last_adjustment).days < 7])
                },
                "learning_velocity": self.current_performance.learning_velocity,
                "adaptation_effectiveness": self.current_performance.adaptation_effectiveness,
                "last_consolidation": self.last_consolidation.isoformat(),
                "learning_session_count": self.learning_session_count
            }

            return summary

        except Exception as e:
            self.logger.error(f"Error generating learning performance summary: {e}")
            return {"status": "error", "message": str(e)}

    async def force_memory_consolidation(self):
        """Force immediate memory consolidation (for testing/maintenance)."""
        await self._consolidate_learning_memories()

    async def reset_learning_system(self, keep_thresholds: bool = True):
        """Reset learning system (for testing or major reconfiguration)."""
        try:
            self.learning_outcomes.clear()
            self.performance_history.clear()
            self.learning_patterns.clear()
            self.contextual_memories.clear()

            if not keep_thresholds:
                self._initialize_adaptive_thresholds()

            self.current_performance = PerformanceMetrics()
            self.learning_session_count = 0
            self.last_consolidation = datetime.now()

            self.logger.warning("🔄 Learning system reset completed")

        except Exception as e:
            self.logger.error(f"Error resetting learning system: {e}")

    # ===== MISSING METHODS IMPLEMENTATION =====

    def _update_performance_metrics(self, outcome: LearningOutcome):
        """Update comprehensive performance metrics."""
        try:
            accuracy = outcome.accuracy_score or 0.5

            # Update basic metrics
            self.current_performance.total_predictions += 1
            if accuracy > 0.6:  # Consider successful
                self.current_performance.correct_predictions += 1

            # Update accuracy rate
            self.current_performance.accuracy_rate = (
                self.current_performance.correct_predictions /
                self.current_performance.total_predictions
            )

            # Update confidence calibration score
            conf_error = outcome.confidence_calibration_error or 0.5
            calibration_score = 1.0 - conf_error
            self.current_performance.confidence_calibration_score = (
                (self.current_performance.confidence_calibration_score * 0.9) +
                (calibration_score * 0.1)
            )

            # Update learning velocity (improvement rate)
            recent_outcomes = list(self.learning_outcomes)[-10:]
            if len(recent_outcomes) >= 5:
                early_acc = statistics.mean([o.accuracy_score for o in recent_outcomes[:5]
                                           if o.accuracy_score is not None])
                late_acc = statistics.mean([o.accuracy_score for o in recent_outcomes[-5:]
                                          if o.accuracy_score is not None])
                self.current_performance.learning_velocity = max(0.0, late_acc - early_acc)

            # Update adaptation effectiveness
            if hasattr(self, 'learning_patterns') and self.learning_patterns:
                pattern_effectiveness = self._calculate_pattern_effectiveness()
                self.current_performance.adaptation_effectiveness = pattern_effectiveness

            # Update prediction consistency
            recent_accuracies = [o.accuracy_score for o in list(self.learning_outcomes)[-10:]
                               if o.accuracy_score is not None]
            if len(recent_accuracies) >= 3:
                consistency = 1.0 - (statistics.stdev(recent_accuracies) /
                                   max(statistics.mean(recent_accuracies), 0.1))
                self.current_performance.prediction_consistency = max(0.0, min(1.0, consistency))

        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")

    def _group_similar_patterns(self) -> Dict[str, List[LearningMemory]]:
        """Group similar patterns for consolidation."""
        try:
            pattern_groups = defaultdict(list)

            for pattern_id, memory in self.learning_patterns.items():
                # Simple grouping by pattern type and success rate range
                pattern_type = memory.pattern_type
                success_range = "high" if memory.success_rate > 0.7 else "medium" if memory.success_rate > 0.4 else "low"
                group_key = f"{pattern_type}_{success_range}"
                pattern_groups[group_key].append(memory)

            return dict(pattern_groups)

        except Exception as e:
            self.logger.error(f"Error grouping similar patterns: {e}")
            return {}

    def _merge_pattern_memories(self, patterns: List[LearningMemory]) -> LearningMemory:
        """Merge similar pattern memories."""
        try:
            if not patterns:
                raise ValueError("No patterns to merge")

            if len(patterns) == 1:
                return patterns[0]

            # Create merged pattern
            merged_id = f"merged_{patterns[0].pattern_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Calculate weighted averages
            total_usage = sum(p.usage_count for p in patterns)
            weighted_success = sum(p.success_rate * p.usage_count for p in patterns) / total_usage
            weighted_confidence = sum(p.confidence_accuracy * p.usage_count for p in patterns) / total_usage
            weighted_strength = sum(p.strength * p.usage_count for p in patterns) / total_usage

            # Merge market conditions (take most recent)
            latest_pattern = max(patterns, key=lambda p: p.last_used or datetime.min)

            merged_pattern = LearningMemory(
                pattern_id=merged_id,
                pattern_type=patterns[0].pattern_type,
                success_rate=weighted_success,
                confidence_accuracy=weighted_confidence,
                market_conditions=latest_pattern.market_conditions,
                usage_count=total_usage,
                last_used=latest_pattern.last_used,
                strength=weighted_strength,
                adaptation_score=statistics.mean([p.adaptation_score for p in patterns])
            )

            return merged_pattern

        except Exception as e:
            self.logger.error(f"Error merging pattern memories: {e}")
            return patterns[0] if patterns else None

    def _create_consolidated_memory(self, memories: List[LearningMemory], regime: str) -> LearningMemory:
        """Create consolidated memory from multiple memories."""
        try:
            if not memories:
                raise ValueError("No memories to consolidate")

            # Calculate consolidated metrics
            avg_success = statistics.mean([m.success_rate for m in memories])
            avg_confidence = statistics.mean([m.confidence_accuracy for m in memories])
            total_usage = sum(m.usage_count for m in memories)
            avg_strength = statistics.mean([m.strength for m in memories])

            consolidated_id = f"consolidated_{regime}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            consolidated_memory = LearningMemory(
                pattern_id=consolidated_id,
                pattern_type="consolidated_contextual",
                success_rate=avg_success,
                confidence_accuracy=avg_confidence,
                market_conditions={"regime": regime, "consolidated": True},
                usage_count=total_usage,
                last_used=datetime.now(),
                strength=avg_strength,
                adaptation_score=statistics.mean([m.adaptation_score for m in memories])
            )

            return consolidated_memory

        except Exception as e:
            self.logger.error(f"Error creating consolidated memory: {e}")
            return memories[0] if memories else None

    async def _optimize_threshold_performance(self):
        """Optimize threshold performance based on learning history."""
        try:
            # Analyze threshold performance over recent predictions
            recent_outcomes = list(self.learning_outcomes)[-50:]

            if len(recent_outcomes) < 10:
                return

            # For each threshold, analyze its impact on prediction accuracy
            for threshold_name, threshold in self.adaptive_thresholds.items():
                # Calculate correlation between threshold adjustments and accuracy
                threshold_performance = self._analyze_threshold_performance(threshold_name, recent_outcomes)

                # Update threshold based on performance analysis
                if threshold_performance > 0.7:
                    # Good performance - slightly increase adaptation rate
                    threshold.adaptation_rate = min(threshold.adaptation_rate * 1.02, 0.1)
                elif threshold_performance < 0.4:
                    # Poor performance - decrease adaptation rate
                    threshold.adaptation_rate = max(threshold.adaptation_rate * 0.98, 0.005)

        except Exception as e:
            self.logger.error(f"Error optimizing threshold performance: {e}")

    def _analyze_threshold_performance(self, threshold_name: str, outcomes: List[LearningOutcome]) -> float:
        """Analyze performance of a specific threshold."""
        try:
            # Simple performance analysis based on accuracy when threshold was relevant
            relevant_outcomes = []

            for outcome in outcomes:
                context = outcome.learning_extracted.get("context", {})
                signal_strength = context.get("signal_strength", 2.5)

                # Determine if this threshold was likely relevant
                if ("strong" in threshold_name and signal_strength > 2.0) or \
                   ("moderate" in threshold_name and 1.0 < signal_strength <= 2.0) or \
                   ("confidence" in threshold_name):
                    relevant_outcomes.append(outcome)

            if not relevant_outcomes:
                return 0.5  # Neutral performance

            # Calculate average accuracy for relevant outcomes
            accuracies = [o.accuracy_score for o in relevant_outcomes if o.accuracy_score is not None]
            return statistics.mean(accuracies) if accuracies else 0.5

        except Exception as e:
            self.logger.error(f"Error analyzing threshold performance: {e}")
            return 0.5

    async def _update_global_learning_parameters(self):
        """Update global learning parameters based on overall performance."""
        try:
            # Analyze overall learning effectiveness
            recent_outcomes = list(self.learning_outcomes)[-100:]

            if len(recent_outcomes) < 20:
                return

            # Calculate overall learning effectiveness
            accuracies = [o.accuracy_score for o in recent_outcomes if o.accuracy_score is not None]

            if accuracies:
                avg_accuracy = statistics.mean(accuracies)
                accuracy_trend = self._calculate_accuracy_trend()

                # Adjust global learning rate
                if avg_accuracy > 0.8 and accuracy_trend == "improving":
                    self.learning_rate = min(self.learning_rate * 1.05, 0.3)
                elif avg_accuracy < 0.4 or accuracy_trend == "declining":
                    self.learning_rate = max(self.learning_rate * 0.95, 0.01)

                # Adjust adaptation momentum
                if accuracy_trend == "improving":
                    self.adaptation_momentum = min(self.adaptation_momentum * 1.02, 0.99)
                elif accuracy_trend == "declining":
                    self.adaptation_momentum = max(self.adaptation_momentum * 0.98, 0.5)

        except Exception as e:
            self.logger.error(f"Error updating global learning parameters: {e}")

    async def _decay_unused_memories(self):
        """Apply decay to unused memories."""
        try:
            current_time = datetime.now()

            # Decay pattern memories
            for pattern_id, memory in list(self.learning_patterns.items()):
                if memory.last_used:
                    days_unused = (current_time - memory.last_used).days
                    if days_unused > 30:  # Decay after 30 days
                        decay_factor = 1.0 - (days_unused - 30) * memory.decay_rate
                        memory.strength *= max(decay_factor, 0.1)

                        # Remove very weak memories
                        if memory.strength < 0.2:
                            del self.learning_patterns[pattern_id]

            # Decay contextual memories
            for regime, memories in self.contextual_memories.items():
                for memory in memories[:]:  # Copy list to avoid modification during iteration
                    if memory.last_used:
                        days_unused = (current_time - memory.last_used).days
                        if days_unused > 60:  # Longer retention for contextual memories
                            decay_factor = 1.0 - (days_unused - 60) * memory.decay_rate
                            memory.strength *= max(decay_factor, 0.1)

                            if memory.strength < 0.1:
                                memories.remove(memory)

        except Exception as e:
            self.logger.error(f"Error decaying unused memories: {e}")

    async def _store_prediction_in_db(self, outcome: LearningOutcome, context: LearningContext):
        """Store prediction in database if available."""
        try:
            if self.db_integration:
                # This would store the prediction in the database
                # For now, just log the attempt
                self.logger.debug(f"Would store prediction {outcome.prediction_id} in database")
        except Exception as e:
            self.logger.debug(f"Could not store prediction in database: {e}")


# ===== GLOBAL LEARNING ENGINE INSTANCE =====

_global_learning_engine: Optional[EliteSelfLearningEngine] = None

async def get_self_learning_engine() -> EliteSelfLearningEngine:
    """Get or create the global self-learning engine instance."""
    global _global_learning_engine

    if _global_learning_engine is None:
        # Initialize database integration if available
        db_integration = None
        if AI_DATABASE_AVAILABLE:
            try:
                db_integration = await get_ai_database_integration()
            except Exception as e:
                logger.warning(f"Could not initialize AI database integration: {e}")

        _global_learning_engine = EliteSelfLearningEngine(db_integration)
        logger.info("🧠 Global Elite Self-Learning Engine initialized")

    return _global_learning_engine

async def record_ai_prediction(prediction_data: Dict[str, Any], context_data: Dict[str, Any]) -> str:
    """Record an AI prediction for future learning validation."""
    try:
        engine = await get_self_learning_engine()
        context = LearningContext(**context_data)
        return await engine.record_prediction(prediction_data, context)
    except Exception as e:
        logger.error(f"Error recording AI prediction: {e}")
        return ""

async def validate_ai_prediction(prediction_id: str, actual_outcome: Dict[str, Any]) -> bool:
    """Validate an AI prediction against actual market outcomes."""
    try:
        engine = await get_self_learning_engine()
        return await engine.validate_prediction(prediction_id, actual_outcome)
    except Exception as e:
        logger.error(f"Error validating AI prediction: {e}")
        return False

async def get_adaptive_ai_thresholds() -> Dict[str, float]:
    """Get current adaptive AI thresholds."""
    try:
        engine = await get_self_learning_engine()
        return await engine.get_adaptive_thresholds()
    except Exception as e:
        logger.error(f"Error getting adaptive thresholds: {e}")
        return {}

async def get_ai_learning_summary() -> Dict[str, Any]:
    """Get comprehensive AI learning performance summary."""
    try:
        engine = await get_self_learning_engine()
        return await engine.get_learning_performance_summary()
    except Exception as e:
        logger.error(f"Error getting learning summary: {e}")
        return {"status": "error", "message": str(e)}
