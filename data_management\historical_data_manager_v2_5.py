# data_management/historical_data_manager_v2_5.py
# EOTS v2.5 - SENTRY-APPROVED

import logging
from typing import Dict, Any, Optional
from datetime import date, timedelta
import pandas as pd
import builtins

from utils.config_manager_v2_5 import ConfigManagerV2_5
from data_management.database_manager_v2_5 import DatabaseManagerV2_5

logger = logging.getLogger(__name__)

def _get_db_manager() -> DatabaseManagerV2_5:
    db_manager = getattr(builtins, 'db_manager', None)
    if db_manager is None:
        raise RuntimeError("Global db_manager is not initialized. Make sure the runner script sets it up.")
    return db_manager

class HistoricalDataManagerV2_5:
    """
    Manages the retrieval and storage of historical market data and metrics.
    Provides methods to fetch OHLCV and custom metrics for rolling analytics,
    and to store daily EOTS metrics for archival.
    """
    def __init__(self, config_manager: ConfigManagerV2_5, db_manager: Optional[DatabaseManagerV2_5] = None):
        self.logger = logger.getChild(self.__class__.__name__)
        self.config_manager = config_manager
        self.db_manager = db_manager or _get_db_manager()
        if not isinstance(self.db_manager, DatabaseManagerV2_5):
            self.logger.critical("FATAL: Invalid db_manager object provided.")
            raise TypeError("db_manager must be an instance of DatabaseManagerV2_5")
        self.logger.info("HistoricalDataManagerV2_5 initialized with live database access.")

    def get_historical_metric(self, symbol: str, metric_name: str, lookback_days: int) -> Optional[pd.Series]:
        """
        Fetches a historical metric series for a symbol over the specified lookback window.
        Args:
            symbol (str): The ticker symbol.
            metric_name (str): The metric/column name to fetch.
            lookback_days (int): Number of days to look back.
        Returns:
            Optional[pd.Series]: Series indexed by date, or None if not found.
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=lookback_days)
            series = self.db_manager.query_metric(
                table_name="daily_eots_metrics",
                metric_name=metric_name,
                start_date=start_date,
                end_date=end_date
            )
            if series is not None:
                self.logger.info(f"Fetched {len(series)} rows for {symbol} {metric_name} ({start_date} to {end_date})")
            else:
                self.logger.warning(f"No data found for {symbol} {metric_name} ({start_date} to {end_date})")
            return series
        except Exception as e:
            self.logger.error(f"Error fetching historical metric for {symbol} {metric_name}: {e}")
            return None

    def get_historical_ohlcv(self, symbol: str, lookback_days: int) -> Optional[pd.DataFrame]:
        """
        Fetches historical OHLCV data for a symbol over the specified lookback window.
        Args:
            symbol (str): The ticker symbol.
            lookback_days (int): Number of days to look back.
        Returns:
            Optional[pd.DataFrame]: DataFrame of OHLCV data, or None if not found.
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=lookback_days)
            df = self.db_manager.query_ohlcv(
                table_name="daily_ohlcv",
                start_date=start_date,
                end_date=end_date
            )
            if df is not None:
                df = df[df['symbol'] == symbol]
                self.logger.info(f"Fetched {len(df)} OHLCV rows for {symbol} ({start_date} to {end_date})")
            else:
                self.logger.warning(f"No OHLCV data found for {symbol} ({start_date} to {end_date})")
            return df if df is not None and not df.empty else None
        except Exception as e:
            self.logger.error(f"Error fetching historical OHLCV for {symbol}: {e}")
            return None

    def store_daily_eots_metrics(self, symbol: str, metric_date: date, metrics_data: Dict[str, Any]) -> None:
        """
        Stores daily EOTS metrics for a symbol/date into the database.
        Args:
            symbol (str): The ticker symbol.
            metric_date (date): The date for the metrics.
            metrics_data (Dict[str, Any]): The metrics to store (column:value pairs).
        """
        try:
            record = {"symbol": symbol, "date": metric_date}
            record.update(metrics_data)
            self.db_manager.insert_record("daily_eots_metrics", record)
            self.logger.info(f"Stored daily EOTS metrics for {symbol} on {metric_date}.")
        except Exception as e:
            self.logger.error(f"Error storing daily EOTS metrics for {symbol} on {metric_date}: {e}")