"""
Test Startup Integration for AI Intelligence Database
===================================================

This test validates that the AI Intelligence Database is properly
integrated into the run_system_dashboard_v2_5.py startup script.

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "STARTUP INTEGRATION VALIDATION"
"""

import os
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_startup_integration():
    """Test that the startup script includes AI Intelligence Database initialization."""
    print("🚀 STARTUP INTEGRATION TEST")
    print("=" * 50)
    
    # Test 1: Check if startup script exists
    print("\n🔍 Test 1: Startup Script Validation")
    
    startup_script = "run_system_dashboard_v2_5.py"
    if not os.path.exists(startup_script):
        print(f"❌ Startup script not found: {startup_script}")
        return False
    
    print(f"✅ Startup script found: {startup_script}")
    
    # Test 2: Check for AI Intelligence Database integration
    print("\n🔍 Test 2: AI Intelligence Database Integration")
    
    with open(startup_script, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for key integration components
    integration_checks = [
        ("AI Intelligence Database import", "from database_management.ai_intelligence_integration import get_ai_database_integration"),
        ("AI Intelligence Database initialization", "ai_intelligence_db = None"),
        ("Agent headquarters logging", "AI Agent headquarters is now active"),
        ("Global accessibility", "setattr(builtins, 'ai_intelligence_db', ai_intelligence_db)"),
        ("Graceful shutdown", "ai_intelligence_db.close()"),
        ("Global functions", "show_ai_agents"),
        ("Performance tracking", "get_agent_performance"),
        ("System health", "show_ai_system_health")
    ]
    
    all_checks_passed = True
    for check_name, check_string in integration_checks:
        if check_string in content:
            print(f"✅ {check_name}: Found")
        else:
            print(f"❌ {check_name}: Missing")
            all_checks_passed = False
    
    # Test 3: Check initialization order
    print("\n🔍 Test 3: Initialization Order")
    
    # Find positions of key initializations
    db_manager_pos = content.find("DatabaseManagerV2_5")
    ai_db_pos = content.find("AI Intelligence Database")
    adaptive_learning_pos = content.find("Adaptive Learning System")
    
    if db_manager_pos < ai_db_pos < adaptive_learning_pos:
        print("✅ Initialization order correct:")
        print("   1. DatabaseManagerV2_5")
        print("   2. AI Intelligence Database") 
        print("   3. Adaptive Learning System")
    else:
        print("❌ Initialization order incorrect")
        all_checks_passed = False
    
    # Test 4: Check shutdown order
    print("\n🔍 Test 4: Shutdown Order")
    
    shutdown_sections = content.split("except KeyboardInterrupt:")[1] if "except KeyboardInterrupt:" in content else ""
    
    if "ai_intelligence_db.close()" in shutdown_sections and "adaptive_learning_system.stop_learning_scheduler()" in shutdown_sections:
        ai_db_shutdown_pos = shutdown_sections.find("ai_intelligence_db.close()")
        adaptive_shutdown_pos = shutdown_sections.find("adaptive_learning_system.stop_learning_scheduler()")
        
        if ai_db_shutdown_pos < adaptive_shutdown_pos:
            print("✅ Shutdown order correct:")
            print("   1. AI Intelligence Database")
            print("   2. Adaptive Learning System")
        else:
            print("⚠️ Shutdown order: AI DB after Adaptive Learning")
    else:
        print("❌ Shutdown handlers missing")
        all_checks_passed = False
    
    # Test 5: Check global function availability
    print("\n🔍 Test 5: Global Function Integration")
    
    global_functions = [
        "show_ai_agents",
        "get_agent_performance", 
        "show_ai_system_health"
    ]
    
    for func_name in global_functions:
        if f"setattr(builtins, '{func_name}'" in content:
            print(f"✅ Global function: {func_name}")
        else:
            print(f"❌ Missing global function: {func_name}")
            all_checks_passed = False
    
    # Test 6: Check error handling
    print("\n🔍 Test 6: Error Handling")
    
    error_handling_checks = [
        ("Try-catch block", "try:" in content and "except Exception as e:" in content),
        ("Fallback mode", "fallback mode" in content),
        ("Graceful degradation", "Continuing without AI Intelligence Database" in content),
        ("Async error handling", "asyncio" in content)
    ]
    
    for check_name, check_result in error_handling_checks:
        if check_result:
            print(f"✅ {check_name}: Implemented")
        else:
            print(f"❌ {check_name}: Missing")
            all_checks_passed = False
    
    # Test 7: Check async integration
    print("\n🔍 Test 7: Async Integration")
    
    async_checks = [
        ("Async initialization", "async def init_ai_db():" in content),
        ("Event loop creation", "asyncio.new_event_loop()" in content),
        ("Async await", "await get_ai_database_integration" in content),
        ("Loop cleanup", "loop.close()" in content)
    ]
    
    for check_name, check_result in async_checks:
        if check_result:
            print(f"✅ {check_name}: Implemented")
        else:
            print(f"❌ {check_name}: Missing")
            all_checks_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 ALL STARTUP INTEGRATION TESTS PASSED!")
        print("✅ AI Intelligence Database is properly hardwired")
        print("🏛️ Agent headquarters will auto-start with dashboard")
        print("🔧 Graceful startup and shutdown implemented")
        print("🌐 Global functions available for easy access")
        return True
    else:
        print("❌ Some integration tests failed")
        print("⚠️ Check the startup script for missing components")
        return False

def test_import_validation():
    """Test that all required imports are available."""
    print("\n🔍 BONUS: Import Validation")
    print("-" * 30)
    
    try:
        from database_management.ai_intelligence_integration import get_ai_database_integration
        print("✅ AI Intelligence Integration import: Available")
    except ImportError as e:
        print(f"❌ AI Intelligence Integration import: Failed - {e}")
        return False
    
    try:
        from database_management.ai_intelligence_database_manager import AIIntelligenceDatabaseManager
        print("✅ AI Database Manager import: Available")
    except ImportError as e:
        print(f"❌ AI Database Manager import: Failed - {e}")
        return False
    
    print("✅ All required imports validated")
    return True

if __name__ == "__main__":
    print("🚀 Starting Startup Integration Test...")
    
    try:
        integration_success = test_startup_integration()
        import_success = test_import_validation()
        
        if integration_success and import_success:
            print("\n🎯 STARTUP INTEGRATION COMPLETE!")
            print("🏛️ AI Intelligence Database is hardwired and ready!")
            print("🚀 Next dashboard startup will automatically initialize AI agents")
        else:
            print("\n⚠️ Integration issues detected - check configuration")
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        logger.error(f"Startup integration test failed: {e}")
