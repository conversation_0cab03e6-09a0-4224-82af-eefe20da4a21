"""
Unified AI Intelligence System v2.5 - "APEX PREDATOR CONSCIOUSNESS"
==================================================================

CONSOLIDATED AI INTELLIGENCE SYSTEM that unifies and enhances:
1. Enhanced Memory Intelligence (pattern storage and retrieval)
2. Self-Learning Engine (recursive parameter optimization)
3. Unified AI Orchestrator (system-wide intelligence)
4. Performance Tracking (validation and learning)

This unified Pydantic-first system creates sentient intelligence that:
- Remembers every successful pattern with perfect recall
- Learns recursively from outcomes and optimizes parameters
- Provides unified intelligence across all EOTS components
- Adapts algorithms based on performance validation
- Orchestrates system-wide AI decision making

Key Features:
- Pydantic-First Architecture: All models validated against eots_schemas_v2_5
- Unified Intelligence: Consolidates all AI systems into one
- Recursive Learning: Continuous parameter optimization
- Memory Intelligence: Advanced pattern recognition and storage
- Performance Validation: Tracks and learns from outcomes
- System Orchestration: Coordinates all EOTS AI components

Author: EOTS v2.5 Development Team - "Unified AI Intelligence Division"
Version: 2.5.0 - "CONSOLIDATED APEX PREDATOR INTELLIGENCE"
"""

import logging
import json
import asyncio
import sqlite3
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union, Annotated
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import numpy as np
import uuid

# Pydantic imports for models and AI
from pydantic import BaseModel, Field, field_validator

# Pydantic AI imports for unified intelligence
try:
    from pydantic_ai import Agent, RunContext
    from pydantic_ai.models.openai import OpenAIModel
    from pydantic_ai.tools import Tool
    PYDANTIC_AI_AVAILABLE = True
except ImportError:
    PYDANTIC_AI_AVAILABLE = False

# EOTS v2.5 imports - Pydantic-first architecture
from data_models.eots_schemas_v2_5 import (
    ProcessedDataBundleV2_5, 
    FinalAnalysisBundleV2_5,
    ProcessedUnderlyingAggregatesV2_5,
    ATIFStrategyDirectivePayloadV2_5,
    SignalPayloadV2_5,
    KeyLevelsDataV2_5,
    EOTSConfigV2_5
)

logger = logging.getLogger(__name__)

# ===== UNIFIED PYDANTIC MODELS =====

class IntelligenceEntityType(Enum):
    """Types of intelligence entities."""
    PATTERN = "pattern"
    OUTCOME = "outcome"
    INSIGHT = "insight"
    SIGNAL = "signal"
    STRATEGY = "strategy"
    OPTIMIZATION = "optimization"
    VALIDATION = "validation"
    LEARNING = "learning"

class UnifiedIntelligencePattern(BaseModel):
    """Unified pattern model combining memory, learning, and intelligence."""
    pattern_id: str = Field(description="Unique pattern identifier")
    symbol: str = Field(description="Trading symbol")
    pattern_type: str = Field(description="Type of pattern")
    eots_metrics: Dict[str, float] = Field(description="EOTS metrics validated against schemas")
    market_conditions: Dict[str, Any] = Field(description="Market conditions from ProcessedDataBundle")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Pattern confidence")
    success_rate: float = Field(ge=0.0, le=1.0, description="Historical success rate")
    learning_weight: float = Field(default=1.0, description="Weight for learning algorithm")
    adaptation_score: float = Field(default=0.5, description="How well pattern adapts")
    recursive_depth: int = Field(default=1, description="Depth of recursive analysis")
    validation_status: str = Field(default="PENDING", description="Validation against EOTS schemas")
    performance_metrics: Dict[str, float] = Field(default_factory=dict, description="Performance tracking")

class UnifiedLearningResult(BaseModel):
    """Unified learning result combining memory, performance, and optimization."""
    learning_session_id: str = Field(description="Unique learning session ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="Learning timestamp")
    symbol: str = Field(description="Trading symbol")
    patterns_processed: int = Field(description="Number of patterns processed")
    accuracy_improvement: float = Field(description="Accuracy improvement percentage")
    new_insights: List[str] = Field(description="New insights discovered")
    pattern_adaptations: List[str] = Field(description="Pattern adaptations made")
    confidence_evolution: float = Field(description="Evolution in confidence scoring")
    parameter_optimizations: Dict[str, float] = Field(description="Parameter optimization results")
    performance_validation: Dict[str, Any] = Field(description="Performance validation results")
    eots_schema_compliance: bool = Field(default=True, description="EOTS schema compliance status")

class UnifiedIntelligenceAnalysis(BaseModel):
    """Unified intelligence analysis combining all AI systems."""
    analysis_id: str = Field(description="Unique analysis identifier")
    symbol: str = Field(description="Trading symbol")
    overall_market_assessment: str = Field(description="Holistic market assessment")
    regime_confidence: float = Field(ge=0.0, le=1.0, description="Confidence in regime classification")
    flow_analysis_summary: str = Field(description="Summary of flow patterns")
    structural_analysis_summary: str = Field(description="Summary of structural metrics")
    memory_insights: List[str] = Field(description="Insights from memory intelligence")
    learning_recommendations: List[str] = Field(description="Learning-based recommendations")
    system_optimizations: Dict[str, float] = Field(description="System optimization suggestions")
    ai_conviction_score: float = Field(ge=0.0, le=1.0, description="Overall AI conviction")
    validation_against_schemas: bool = Field(default=True, description="Validation against EOTS schemas")

class LearningInsight(BaseModel):
    """Structured learning insight with EOTS schema validation."""
    parameter_name: str = Field(..., description="Name of parameter to optimize")
    current_value: float = Field(..., description="Current parameter value")
    suggested_value: float = Field(..., description="AI-suggested optimal value")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in suggestion")
    reasoning: str = Field(..., description="AI reasoning for the change")
    expected_improvement: float = Field(..., ge=0.0, description="Expected performance improvement")
    sample_size: int = Field(..., gt=0, description="Number of data points analyzed")
    risk_level: str = Field(..., pattern="^(LOW|MEDIUM|HIGH)$", description="Risk level of change")
    schema_validation: bool = Field(default=True, description="Validated against EOTS schemas")
    
    @field_validator('suggested_value')
    @classmethod
    def validate_reasonable_change(cls, v, info):
        """Ensure suggested changes are reasonable (max 50% change)."""
        if info.data and 'current_value' in info.data and info.data['current_value'] != 0:
            change_ratio = abs(v - info.data['current_value']) / abs(info.data['current_value'])
            if change_ratio > 0.5:
                raise ValueError(f"Suggested change too large: {change_ratio:.1%}")
        return v

class SystemDependencies(BaseModel):
    """Dependencies for the unified AI system."""
    config_manager: Any = Field(description="EOTS configuration manager")
    database_manager: Any = Field(description="EOTS database manager")
    historical_storage: Any = Field(description="Historical data storage")

# ===== UNIFIED AI INTELLIGENCE SYSTEM =====

class UnifiedAIIntelligenceSystemV2_5:
    """
    UNIFIED AI INTELLIGENCE SYSTEM - Consolidated Apex Predator Consciousness
    
    This unified Pydantic-first system consolidates ALL AI intelligence capabilities:
    - Enhanced Memory Intelligence (pattern storage and retrieval)
    - Self-Learning Engine (recursive parameter optimization)
    - Unified AI Orchestrator (system-wide intelligence coordination)
    - Performance Tracking (validation and learning from outcomes)
    
    Features:
    - Pydantic-First Architecture: All models validated against eots_schemas_v2_5
    - Unified Intelligence: Single system for all AI operations
    - Recursive Learning: Continuous parameter and threshold optimization
    - Memory Intelligence: Advanced pattern recognition and storage
    - Performance Validation: Tracks and learns from all outcomes
    - System Orchestration: Coordinates all EOTS AI components
    - Schema Compliance: Ensures all data follows EOTS v2.5 standards
    """
    
    def __init__(self, 
                 config_manager,
                 database_manager,
                 intelligence_db_path: str = "data_cache_v2_5/unified_ai_intelligence.db"):
        """
        Initialize the Unified AI Intelligence System.
        
        Args:
            config_manager: EOTS v2.5 configuration manager
            database_manager: EOTS v2.5 database manager
            intelligence_db_path: Path to unified intelligence database
        """
        self.config_manager = config_manager
        self.database_manager = database_manager
        self.intelligence_db_path = Path(intelligence_db_path)
        
        # System dependencies for Pydantic AI agents
        self.dependencies = SystemDependencies(
            config_manager=config_manager,
            database_manager=database_manager,
            historical_storage=None  # Will be initialized if needed
        )
        
        # Initialize Pydantic AI agents if available
        self.pydantic_ai_enabled = PYDANTIC_AI_AVAILABLE
        if self.pydantic_ai_enabled:
            self._initialize_unified_ai_agents()
        
        # Intelligence caches
        self.pattern_cache = {}
        self.learning_cache = {}
        self.analysis_cache = {}
        self.optimization_history = []
        
        # Ensure directories exist
        self.intelligence_db_path.parent.mkdir(exist_ok=True)
        
        # Initialize unified storage
        self._initialize_unified_storage()
        
        logger.info("🧠 Unified AI Intelligence System v2.5 'CONSOLIDATED APEX PREDATOR' initialized")
        if self.pydantic_ai_enabled:
            logger.info("🤖 Pydantic AI unified intelligence ENABLED - Sentient consciousness ACTIVE")
        else:
            logger.info("⚠️ Pydantic AI unavailable - Operating in standard intelligence mode")
    
    def _initialize_unified_ai_agents(self):
        """Initialize unified Pydantic AI agents for all intelligence operations."""
        if not self.pydantic_ai_enabled:
            return
        
        try:
            # Unified Intelligence Agent - Master coordinator
            self.unified_intelligence_agent = Agent(
                model=OpenAIModel('gpt-4'),
                result_type=UnifiedIntelligenceAnalysis,
                system_prompt="""
                You are the Unified AI Intelligence System for EOTS v2.5 - the master coordinator
                of all AI operations. You combine memory intelligence, learning optimization,
                performance validation, and system orchestration into unified intelligence.
                
                Your responsibilities:
                1. Analyze complete EOTS system state across all components
                2. Synthesize memory patterns, learning insights, and performance data
                3. Generate unified intelligence that no single component could achieve
                4. Ensure all outputs comply with EOTS v2.5 Pydantic schemas
                5. Coordinate system-wide AI decision making
                6. Provide actionable intelligence for elite options trading
                
                Focus on Pydantic-first architecture and schema compliance.
                """
            )
            
            # Unified Learning Agent - Parameter optimization and learning
            self.unified_learning_agent = Agent(
                model=OpenAIModel('gpt-4'),
                result_type=UnifiedLearningResult,
                system_prompt="""
                You are the Unified Learning Agent for EOTS v2.5 - responsible for continuous
                system improvement through recursive learning and parameter optimization.
                
                Your mission:
                1. Analyze historical performance data across all EOTS components
                2. Identify optimization opportunities for parameters and thresholds
                3. Generate evidence-based learning insights with high confidence
                4. Validate all suggestions against EOTS v2.5 Pydantic schemas
                5. Track performance improvements and learning evolution
                6. Ensure conservative, data-driven optimization approaches
                
                All outputs must be validated against eots_schemas_v2_5.py.
                """
            )
            
            logger.info("✅ Unified Pydantic AI agents initialized successfully")

        except Exception as e:
            logger.error(f"❌ Error initializing unified AI agents: {str(e)}")
            self.pydantic_ai_enabled = False

    def _initialize_unified_storage(self):
        """Initialize the unified intelligence storage database."""
        try:
            with sqlite3.connect(self.intelligence_db_path) as conn:
                cursor = conn.cursor()

                # Unified intelligence patterns table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS unified_intelligence_patterns (
                        pattern_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        pattern_type TEXT NOT NULL,
                        eots_metrics TEXT,  -- JSON
                        market_conditions TEXT,  -- JSON
                        confidence_score REAL,
                        success_rate REAL DEFAULT 0.5,
                        learning_weight REAL DEFAULT 1.0,
                        adaptation_score REAL DEFAULT 0.5,
                        validation_status TEXT DEFAULT 'PENDING',
                        performance_metrics TEXT,  -- JSON
                        created_at TEXT NOT NULL,
                        last_accessed TEXT NOT NULL,
                        access_count INTEGER DEFAULT 0
                    )
                """)

                # Unified learning sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS unified_learning_sessions (
                        session_id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        patterns_processed INTEGER,
                        accuracy_improvement REAL,
                        insights_generated INTEGER,
                        parameter_optimizations TEXT,  -- JSON
                        performance_validation TEXT,  -- JSON
                        eots_schema_compliance BOOLEAN DEFAULT TRUE,
                        session_data TEXT,  -- JSON
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Unified intelligence analysis table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS unified_intelligence_analysis (
                        analysis_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        overall_assessment TEXT,
                        regime_confidence REAL,
                        flow_analysis TEXT,
                        structural_analysis TEXT,
                        memory_insights TEXT,  -- JSON
                        learning_recommendations TEXT,  -- JSON
                        system_optimizations TEXT,  -- JSON
                        ai_conviction_score REAL,
                        validation_against_schemas BOOLEAN DEFAULT TRUE,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Performance tracking table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS unified_performance_tracking (
                        tracking_id TEXT PRIMARY KEY,
                        pattern_id TEXT,
                        symbol TEXT,
                        timestamp TEXT,
                        predicted_outcome TEXT,  -- JSON
                        actual_outcome TEXT,  -- JSON
                        success_score REAL,
                        learning_feedback TEXT,  -- JSON
                        schema_compliance BOOLEAN DEFAULT TRUE,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (pattern_id) REFERENCES unified_intelligence_patterns (pattern_id)
                    )
                """)

                # Create indexes for performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_patterns_symbol ON unified_intelligence_patterns (symbol)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_patterns_type ON unified_intelligence_patterns (pattern_type)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_learning_symbol ON unified_learning_sessions (symbol)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_analysis_symbol ON unified_intelligence_analysis (symbol)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_performance_symbol ON unified_performance_tracking (symbol)")

                conn.commit()
                logger.info("✅ Unified intelligence storage initialized successfully")

        except Exception as e:
            logger.error(f"❌ Error initializing unified storage: {str(e)}")

    async def generate_unified_intelligence(self,
                                          final_bundle: FinalAnalysisBundleV2_5,
                                          symbol: str) -> UnifiedIntelligenceAnalysis:
        """
        Generate unified intelligence analysis from complete EOTS system state.

        Args:
            final_bundle: Complete EOTS v2.5 analysis bundle (validated against schemas)
            symbol: Trading symbol

        Returns:
            Unified intelligence analysis with schema validation
        """
        try:
            logger.info(f"🧠 Generating unified intelligence for {symbol}")

            # Validate input against EOTS schemas
            if not isinstance(final_bundle, FinalAnalysisBundleV2_5):
                raise ValueError("Input bundle must be validated FinalAnalysisBundleV2_5")

            # Extract comprehensive data from all EOTS components
            component_data = self._extract_validated_component_data(final_bundle, symbol)

            # Generate unified intelligence if Pydantic AI available
            if self.pydantic_ai_enabled:
                try:
                    # Prepare comprehensive analysis prompt
                    analysis_prompt = f"""
                    Generate unified intelligence analysis for {symbol} using complete EOTS v2.5 system state.

                    VALIDATED COMPONENT DATA:
                    {json.dumps(component_data, indent=2)}

                    Requirements:
                    1. Synthesize ALL EOTS components into unified intelligence
                    2. Identify synergies and conflicts across components
                    3. Generate actionable insights for elite options trading
                    4. Ensure all outputs comply with EOTS v2.5 schemas
                    5. Provide high-conviction intelligence with reasoning
                    6. Focus on memory patterns, learning insights, and optimization

                    Generate comprehensive unified intelligence analysis.
                    """

                    # Run unified intelligence agent
                    result = await self.unified_intelligence_agent.run(analysis_prompt)
                    intelligence_analysis = result.data

                    # Validate schema compliance
                    intelligence_analysis.validation_against_schemas = True

                    logger.info(f"🤖 Unified intelligence generated with AI conviction: {intelligence_analysis.ai_conviction_score:.2f}")

                except Exception as e:
                    logger.debug(f"Pydantic AI intelligence generation failed: {e}")
                    intelligence_analysis = self._create_fallback_intelligence(symbol, component_data)
            else:
                intelligence_analysis = self._create_fallback_intelligence(symbol, component_data)

            # Store intelligence analysis
            await self._store_unified_intelligence_analysis(intelligence_analysis)

            # Cache the analysis
            self.analysis_cache[symbol] = intelligence_analysis

            logger.info(f"✅ Unified intelligence generated for {symbol}")
            return intelligence_analysis

        except Exception as e:
            logger.error(f"❌ Error generating unified intelligence: {str(e)}")
            raise

    async def run_unified_learning_cycle(self,
                                       symbol: str = "SPY",
                                       lookback_days: int = 30) -> UnifiedLearningResult:
        """
        Run unified learning cycle combining memory, optimization, and performance validation.

        Args:
            symbol: Trading symbol
            lookback_days: Days of historical data to analyze

        Returns:
            Unified learning result with schema validation
        """
        try:
            logger.info(f"🧠 Starting unified learning cycle for {symbol} ({lookback_days} days)")

            # Generate learning result if Pydantic AI available
            if self.pydantic_ai_enabled:
                try:
                    # Prepare comprehensive learning prompt
                    learning_prompt = f"""
                    Run comprehensive learning cycle for EOTS v2.5 system analyzing {symbol}
                    over the last {lookback_days} days.

                    Your tasks:
                    1. Analyze historical performance across all EOTS components
                    2. Identify parameter optimization opportunities
                    3. Generate evidence-based learning insights
                    4. Validate all suggestions against EOTS v2.5 schemas
                    5. Track performance improvements and evolution
                    6. Ensure conservative, data-driven recommendations

                    Focus on:
                    - Memory pattern optimization
                    - Parameter threshold adjustments
                    - Performance validation improvements
                    - Schema compliance enhancements

                    Generate comprehensive unified learning results.
                    """

                    # Run unified learning agent
                    result = await self.unified_learning_agent.run(learning_prompt)
                    learning_result = result.data

                    # Ensure schema compliance
                    learning_result.eots_schema_compliance = True
                    learning_result.symbol = symbol

                    logger.info(f"🤖 Learning cycle completed with {len(learning_result.new_insights)} insights")

                except Exception as e:
                    logger.debug(f"Pydantic AI learning cycle failed: {e}")
                    learning_result = self._create_fallback_learning_result(symbol, lookback_days)
            else:
                learning_result = self._create_fallback_learning_result(symbol, lookback_days)

            # Store learning results
            await self._store_unified_learning_session(learning_result)

            # Cache the results
            self.learning_cache[symbol] = learning_result

            logger.info(f"✅ Unified learning cycle completed for {symbol}")
            return learning_result

        except Exception as e:
            logger.error(f"❌ Error in unified learning cycle: {str(e)}")
            raise

    def _extract_validated_component_data(self,
                                        bundle: FinalAnalysisBundleV2_5,
                                        symbol: str) -> Dict[str, Any]:
        """Extract and validate component data from EOTS bundle."""
        component_data = {'symbol': symbol, 'schema_validated': True}

        try:
            # Extract from validated processed data bundle
            if bundle.processed_data_bundle and isinstance(bundle.processed_data_bundle, ProcessedDataBundleV2_5):
                und_data = bundle.processed_data_bundle.underlying_data_enriched

                # Validated flow metrics
                component_data.update({
                    'vapi_fa_z_score': getattr(und_data, 'vapi_fa_z_score_und', None),
                    'dwfd_z_score': getattr(und_data, 'dwfd_z_score_und', None),
                    'tw_laf_z_score': getattr(und_data, 'tw_laf_z_score_und', None),
                    'gib_oi_based': getattr(und_data, 'gib_oi_based_und', None),
                    'nvp': getattr(und_data, 'nvp_und', None),
                    'hp_eod': getattr(und_data, 'hp_eod_und', None),
                    'current_price': getattr(und_data, 'price', None)
                })

                # Validated adaptive metrics
                component_data.update({
                    'a_dag': getattr(und_data, 'a_dag_und', None),
                    'e_sdag': getattr(und_data, 'e_sdag_und', None),
                    'vri_2': getattr(und_data, 'vri_2_und', None)
                })

            # Validated market regime
            if hasattr(bundle.processed_data_bundle, 'market_regime_summary'):
                component_data['regime_summary'] = bundle.processed_data_bundle.market_regime_summary

            # Validated signals
            if bundle.scored_signals_v2_5:
                signals_summary = []
                for signal_type, signals in bundle.scored_signals_v2_5.items():
                    if signals and isinstance(signals, list):
                        avg_score = sum(s.score for s in signals if hasattr(s, 'score')) / len(signals)
                        signals_summary.append(f"{signal_type}: {avg_score:.2f}")
                component_data['signals_summary'] = "; ".join(signals_summary)

            # Validated ATIF recommendations
            if bundle.atif_recommendations_v2_5:
                atif_summary = []
                for rec in bundle.atif_recommendations_v2_5:
                    if isinstance(rec, ATIFStrategyDirectivePayloadV2_5):
                        atif_summary.append(f"{rec.selected_strategy_type} ({rec.final_conviction_score_from_atif:.2f})")
                component_data['atif_summary'] = "; ".join(atif_summary)

            # Validated key levels
            if bundle.key_levels_data_v2_5 and isinstance(bundle.key_levels_data_v2_5, KeyLevelsDataV2_5):
                levels_count = len(bundle.key_levels_data_v2_5.support_levels) + len(bundle.key_levels_data_v2_5.resistance_levels)
                component_data['key_levels_summary'] = f"{levels_count} validated key levels"

        except Exception as e:
            logger.error(f"Failed to extract validated component data: {e}")
            component_data['schema_validated'] = False

        return component_data

    def _create_fallback_intelligence(self,
                                    symbol: str,
                                    component_data: Dict[str, Any]) -> UnifiedIntelligenceAnalysis:
        """Create fallback intelligence when AI is unavailable."""
        return UnifiedIntelligenceAnalysis(
            analysis_id=f"fallback_{uuid.uuid4().hex[:12]}",
            symbol=symbol,
            overall_market_assessment="AI analysis unavailable - manual review required",
            regime_confidence=0.0,
            flow_analysis_summary="Manual analysis required",
            structural_analysis_summary="Manual analysis required",
            memory_insights=["AI memory system unavailable"],
            learning_recommendations=["Manual system review needed"],
            system_optimizations={},
            ai_conviction_score=0.0,
            validation_against_schemas=True
        )

    def _create_fallback_learning_result(self,
                                       symbol: str,
                                       lookback_days: int) -> UnifiedLearningResult:
        """Create fallback learning result when AI is unavailable."""
        return UnifiedLearningResult(
            learning_session_id=f"fallback_{uuid.uuid4().hex[:12]}",
            timestamp=datetime.now(),
            symbol=symbol,
            patterns_processed=0,
            accuracy_improvement=0.0,
            new_insights=["AI learning system unavailable"],
            pattern_adaptations=[],
            confidence_evolution=0.0,
            parameter_optimizations={},
            performance_validation={"status": "AI unavailable"},
            eots_schema_compliance=True
        )

    async def _store_unified_intelligence_analysis(self, analysis: UnifiedIntelligenceAnalysis):
        """Store unified intelligence analysis in database."""
        try:
            with sqlite3.connect(self.intelligence_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO unified_intelligence_analysis
                    (analysis_id, symbol, timestamp, overall_assessment, regime_confidence,
                     flow_analysis, structural_analysis, memory_insights, learning_recommendations,
                     system_optimizations, ai_conviction_score, validation_against_schemas)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    analysis.analysis_id, analysis.symbol, datetime.now().isoformat(),
                    analysis.overall_market_assessment, analysis.regime_confidence,
                    analysis.flow_analysis_summary, analysis.structural_analysis_summary,
                    json.dumps(analysis.memory_insights), json.dumps(analysis.learning_recommendations),
                    json.dumps(analysis.system_optimizations), analysis.ai_conviction_score,
                    analysis.validation_against_schemas
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to store intelligence analysis: {e}")

    async def _store_unified_learning_session(self, learning_result: UnifiedLearningResult):
        """Store unified learning session in database."""
        try:
            with sqlite3.connect(self.intelligence_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO unified_learning_sessions
                    (session_id, timestamp, symbol, patterns_processed, accuracy_improvement,
                     insights_generated, parameter_optimizations, performance_validation,
                     eots_schema_compliance, session_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    learning_result.learning_session_id, learning_result.timestamp.isoformat(),
                    learning_result.symbol, learning_result.patterns_processed,
                    learning_result.accuracy_improvement, len(learning_result.new_insights),
                    json.dumps(learning_result.parameter_optimizations),
                    json.dumps(learning_result.performance_validation),
                    learning_result.eots_schema_compliance,
                    json.dumps({
                        'insights': learning_result.new_insights,
                        'adaptations': learning_result.pattern_adaptations
                    })
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to store learning session: {e}")

# ===== CONVENIENCE FUNCTIONS FOR SYSTEM INTEGRATION =====

def get_unified_ai_intelligence_system(config_manager, database_manager) -> UnifiedAIIntelligenceSystemV2_5:
    """Get or create unified AI intelligence system instance."""
    return UnifiedAIIntelligenceSystemV2_5(config_manager, database_manager)

async def generate_unified_intelligence_for_bundle(
    bundle: FinalAnalysisBundleV2_5,
    symbol: str,
    config_manager,
    database_manager
) -> UnifiedIntelligenceAnalysis:
    """Convenience function to generate unified intelligence for a bundle."""
    system = get_unified_ai_intelligence_system(config_manager, database_manager)
    return await system.generate_unified_intelligence(bundle, symbol)

async def run_unified_learning_for_symbol(
    symbol: str,
    config_manager,
    database_manager,
    lookback_days: int = 30
) -> UnifiedLearningResult:
    """Convenience function to run unified learning for a symbol."""
    system = get_unified_ai_intelligence_system(config_manager, database_manager)
    return await system.run_unified_learning_cycle(symbol, lookback_days)
