{"daily_learning_enabled": true, "weekly_deep_learning_enabled": true, "monthly_comprehensive_review_enabled": true, "real_time_adaptation_enabled": true, "min_confidence_for_auto_update": 0.8, "max_parameter_change_per_cycle": 0.15, "learning_history_retention_days": 90, "learning_schedule": {"daily_time": "02:00", "weekly_day": "sunday", "weekly_time": "03:00", "monthly_day": 1}, "parameter_constraints": {"ai_confidence_threshold": {"min": 0.3, "max": 0.95, "current": 0.7}, "vapi_fa_threshold": {"min": 0.5, "max": 3.0, "current": 1.5}, "dwfd_threshold": {"min": 0.5, "max": 3.0, "current": 1.5}, "tw_laf_threshold": {"min": 0.5, "max": 3.0, "current": 1.5}, "regime_confidence_threshold": {"min": 0.4, "max": 0.9, "current": 0.7}, "flow_intensity_threshold": {"min": 0.2, "max": 0.9, "current": 0.6}, "volatility_threshold": {"min": 15.0, "max": 50.0, "current": 25.0}}, "learning_priorities": {"ai_prediction_accuracy": 1.0, "regime_detection_accuracy": 0.9, "flow_signal_quality": 0.8, "key_level_effectiveness": 0.7, "pattern_recognition": 0.6}, "risk_management": {"enable_rollback": true, "rollback_threshold_days": 7, "performance_degradation_threshold": 0.05, "max_consecutive_failures": 3}, "notification_settings": {"email_notifications": false, "log_level": "INFO", "alert_on_major_changes": true, "alert_on_performance_improvement": true, "alert_on_parameter_updates": false}, "advanced_settings": {"enable_experimental_features": false, "use_ensemble_learning": true, "enable_cross_validation": true, "learning_rate_adaptation": true, "enable_regime_specific_learning": true}}