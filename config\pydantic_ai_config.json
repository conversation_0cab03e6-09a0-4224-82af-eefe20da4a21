{"pydantic_ai_settings": {"enabled": true, "model_config": {"primary_model": "gpt-4", "fallback_model": "gpt-3.5-turbo", "temperature": 0.1, "max_tokens": 2000, "timeout_seconds": 30}, "agent_settings": {"market_analyst": {"system_prompt_enhancement": "Focus on institutional flow patterns and gamma positioning effects", "confidence_threshold": 0.7, "max_retries": 2, "learning_enabled": true, "pattern_recognition_depth": "advanced"}, "regime_analyst": {"system_prompt_enhancement": "Specialize in regime detection and transition prediction with EOTS metrics", "confidence_threshold": 0.75, "max_retries": 2, "transition_sensitivity": 0.8, "regime_validation_enabled": true}, "confidence_calculator": {"system_prompt_enhancement": "Focus on confidence calibration and ensemble validation", "confidence_threshold": 0.8, "max_retries": 1, "self_validation_enabled": true, "historical_accuracy_weight": 0.3}, "strategy_advisor": {"system_prompt_enhancement": "Emphasize risk management and position sizing guidance", "confidence_threshold": 0.75, "max_retries": 2}, "learning_agent": {"system_prompt_enhancement": "Focus on quantifiable pattern recognition and system improvements", "confidence_threshold": 0.8, "learning_retention_days": 90, "recursive_learning_enabled": true, "adaptive_threshold_management": true}}, "learning_settings": {"auto_apply_threshold": 0.85, "learning_batch_size": 10, "pattern_confidence_minimum": 0.8, "feedback_loop_enabled": true, "performance_tracking_enabled": true}, "safety_settings": {"max_daily_ai_calls": 1000, "rate_limit_per_minute": 20, "fallback_to_traditional": true, "error_threshold_before_disable": 5}, "insight_generation": {"ai_insight_weight": 0.4, "traditional_insight_weight": 0.6, "combine_insights": true, "ai_insight_expiry_hours": 4, "minimum_confidence_for_display": 0.6}, "adaptive_thresholds": {"enabled": true, "adjustment_frequency_hours": 24, "max_threshold_change_percent": 0.1, "learning_sample_minimum": 20, "intelligence_engine_thresholds": {"vapi_fa_strong": 2.0, "vapi_fa_moderate": 1.5, "dwfd_strong": 1.5, "dwfd_moderate": 1.0, "tw_laf_strong": 1.5, "tw_laf_moderate": 1.0, "gib_significant": 100000, "vri_high": 5000, "confluence_threshold": 0.7, "confidence_threshold": 0.6, "signal_strength_norm": 3.0}}, "intelligence_engine": {"enabled": true, "aggressive_learning": true, "self_improvement_enabled": true, "performance_tracking": {"track_insight_accuracy": true, "track_confidence_calibration": true, "track_regime_predictions": true, "validation_against_outcomes": true, "learning_history_retention_days": 90}, "ensemble_methods": {"enabled": true, "cross_validation_agents": true, "confidence_ensemble_weight": 0.4, "primary_agent_weight": 0.6}, "adaptive_learning": {"parameter_optimization": true, "threshold_adaptation": true, "pattern_recognition_improvement": true, "recursive_learning_cycles": true}}}, "api_keys": {"primary_provider": "openai", "openai_api_key_env_var": "OPENAI_API_KEY", "anthropic_api_key_env_var": "ANTHROPIC_API_KEY", "use_fallback_provider": false, "backup_provider": "anthropic"}, "monitoring": {"log_ai_interactions": true, "track_performance_metrics": true, "alert_on_ai_failures": true, "dashboard_ai_status_display": true}, "unified_ai_orchestrator": {"enabled": true, "component_integration": {"market_regime_engine": true, "metrics_calculator": true, "signal_generator": true, "atif_framework": true, "ticker_context_analyzer": true, "key_level_identifier": true, "trade_parameter_optimizer": true, "performance_tracker": true, "multi_database_manager": true}, "intelligence_generation": {"auto_generate_on_analysis": true, "store_intelligence_history": true, "intelligence_expiry_hours": 2, "minimum_conviction_threshold": 0.6}, "system_optimization": {"auto_optimize_enabled": true, "optimization_frequency_hours": 24, "minimum_trades_for_optimization": 20, "auto_apply_threshold": 0.85, "backup_settings_before_changes": true}, "component_weighting": {"flow_metrics_weight": 0.25, "adaptive_metrics_weight": 0.2, "structural_metrics_weight": 0.2, "signals_weight": 0.15, "atif_weight": 0.1, "performance_weight": 0.1}}, "multi_database_integration": {"supabase_primary": true, "mcp_ai_learning": true, "sync_frequency_minutes": 15, "cross_database_validation": true, "fallback_to_single_db": true}, "ai_dashboard_settings": {"enabled": true, "layout_config": {"grid_layout": "7_component_optimized", "responsive_breakpoints": {"lg": 1200, "md": 768, "sm": 576}, "component_spacing": "20px", "header_height": "auto"}, "component_settings": {"unified_ai_intelligence_hub": {"title": "🧠 Unified AI Intelligence Hub", "priority": 1, "height": 400, "refresh_interval": 15, "max_insights": 6, "confluence_threshold": 0.7, "signal_strength_threshold": 2.0, "show_confidence_meter": true, "auto_refresh": true}, "ai_recommendations_panel": {"title": "🎯 AI Recommendations", "priority": 2, "height": 350, "max_recommendations": 5, "confidence_threshold": 0.6, "strategy_diversity_weight": 0.3, "conviction_weight": 0.7, "show_rationale": true, "auto_update": true}, "ai_regime_context_panel": {"title": "🌊 AI Regime Context", "priority": 3, "height": 300, "show_transition_probability": true, "regime_confidence_threshold": 0.65, "transition_alert_threshold": 0.7, "historical_regime_days": 30, "show_regime_insights": true}, "ai_performance_tracker": {"title": "📈 AI Performance Tracker", "priority": 4, "height": 250, "lookback_days": 30, "show_learning_curve": true, "performance_metrics": ["success_rate", "avg_confidence", "total_signals", "learning_score"], "chart_type": "line_with_metrics", "real_time_updates": true}, "apex_predator_brain": {"title": "😈 APEX PREDATOR BRAIN", "priority": 5, "height": 350, "mcp_systems_display": 4, "diabolical_insights_max": 3, "alpha_intelligence_enabled": true, "news_sentiment_threshold": 0.3, "intelligence_score_threshold": 0.5, "auto_refresh_mcp": true}, "ai_metrics_dashboard": {"title": "🔢 Raw EOTS Metrics", "priority": 6, "height": 400, "show_tier_separators": true, "color_code_values": true, "decimal_precision": 3, "chart_height": 300, "metrics_per_tier": {"tier_3": 6, "tier_2": 5, "tier_1": 5}, "validation_enabled": true}, "ai_learning_center": {"title": "🎓 AI Learning Center", "priority": 7, "height": 350, "learning_metrics_format": "comprehensive_6_metric", "pattern_display_limit": 10, "adaptation_score_threshold": 7.0, "learning_velocity_threshold": 0.75, "memory_node_display": true, "show_learning_insights": true}, "ai_system_status_bar": {"title": "📊 System Status", "priority": 0, "height": 60, "always_visible": true, "health_check_interval": 10, "component_status_display": true, "alert_threshold": 0.8, "show_latency": true}}, "performance_optimization": {"lazy_loading_enabled": true, "component_caching": {"enabled": true, "cache_duration_minutes": 5, "cache_size_limit_mb": 50}, "data_streaming": {"enabled": false, "stream_interval_seconds": 2, "buffer_size": 100}, "progressive_loading": {"enabled": true, "priority_components_first": true, "loading_animation": true}}, "integration_settings": {"pydantic_validation": {"strict_mode": true, "validation_on_update": true, "error_handling": "graceful_fallback"}, "external_data_sources": {"alpha_vantage": {"enabled": true, "timeout_seconds": 10, "retry_attempts": 2, "fallback_enabled": true}, "mcp_systems": {"enabled": true, "health_check_interval": 30, "auto_reconnect": true}, "database_integration": {"primary_db_required": true, "fallback_to_cache": true, "connection_pool_size": 5}}, "cross_component_communication": {"shared_data_bus": true, "event_driven_updates": true, "component_dependencies": {"ai_recommendations_panel": ["unified_ai_intelligence_hub", "ai_metrics_dashboard"], "ai_regime_context_panel": ["unified_ai_intelligence_hub"], "apex_predator_brain": ["ai_system_status_bar"]}}}, "user_experience": {"theme": {"dark_mode": true, "color_scheme": "ai_optimized", "animation_enabled": true, "transition_duration": "0.3s"}, "accessibility": {"high_contrast_mode": false, "screen_reader_support": true, "keyboard_navigation": true}, "customization": {"user_layout_preferences": true, "component_reordering": false, "custom_thresholds": true}}, "monitoring_and_analytics": {"component_performance_tracking": true, "user_interaction_analytics": true, "error_reporting": {"enabled": true, "detailed_logging": true, "auto_recovery_attempts": 3}, "usage_statistics": {"track_component_access": true, "track_feature_utilization": true, "performance_metrics": true}}}}