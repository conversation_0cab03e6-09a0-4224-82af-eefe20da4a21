"""
Cache Migration Utility v2.5 - Upgrade Legacy Cache to Enhanced System
=======================================================================

Migrates the existing static JSON cache files to the new enhanced cache system
with compression, metadata, and multi-level storage.
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import shutil

from enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5, CacheLevel


class CacheMigrationUtilityV2_5:
    """
    Utility to migrate legacy cache files to enhanced cache system.
    """
    
    def __init__(self, 
                 legacy_cache_dir: str = "cache/intraday_metrics",
                 enhanced_cache_dir: str = "cache/enhanced_v2_5"):
        """
        Initialize the migration utility.
        
        Args:
            legacy_cache_dir: Path to legacy cache directory
            enhanced_cache_dir: Path to enhanced cache directory
        """
        self.logger = logging.getLogger(__name__)
        self.legacy_cache_dir = Path(legacy_cache_dir)
        self.enhanced_cache_dir = Path(enhanced_cache_dir)
        
        # Initialize enhanced cache manager
        self.enhanced_cache = EnhancedCacheManagerV2_5(
            cache_root=str(self.enhanced_cache_dir)
        )
        
        self.migration_stats = {
            "files_processed": 0,
            "files_migrated": 0,
            "files_failed": 0,
            "compression_savings": 0.0,
            "errors": []
        }
    
    def analyze_legacy_cache(self) -> Dict[str, Any]:
        """
        Analyze the legacy cache structure and provide migration recommendations.
        
        Returns:
            Analysis report with recommendations
        """
        if not self.legacy_cache_dir.exists():
            return {"error": "Legacy cache directory not found"}
        
        analysis = {
            "total_files": 0,
            "total_size_mb": 0.0,
            "file_types": {},
            "symbols": set(),
            "metrics": set(),
            "date_range": {"earliest": None, "latest": None},
            "recommendations": []
        }
        
        for cache_file in self.legacy_cache_dir.glob("*.json"):
            try:
                analysis["total_files"] += 1
                file_size = cache_file.stat().st_size
                analysis["total_size_mb"] += file_size / (1024 * 1024)
                
                # Parse filename: SYMBOL_METRIC_DATE.json
                filename_parts = cache_file.stem.split("_")
                if len(filename_parts) >= 3:
                    symbol = filename_parts[0]
                    metric = "_".join(filename_parts[1:-1])  # Handle multi-part metrics
                    date_str = filename_parts[-1]
                    
                    analysis["symbols"].add(symbol)
                    analysis["metrics"].add(metric)
                    
                    # Track date range
                    try:
                        file_date = datetime.strptime(date_str, "%Y-%m-%d")
                        if not analysis["date_range"]["earliest"] or file_date < analysis["date_range"]["earliest"]:
                            analysis["date_range"]["earliest"] = file_date
                        if not analysis["date_range"]["latest"] or file_date > analysis["date_range"]["latest"]:
                            analysis["date_range"]["latest"] = file_date
                    except ValueError:
                        pass
                
                # Analyze file content
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    
                data_type = type(data.get("values", [])).__name__
                analysis["file_types"][data_type] = analysis["file_types"].get(data_type, 0) + 1
                
            except Exception as e:
                self.logger.warning(f"Failed to analyze {cache_file}: {e}")
        
        # Convert sets to lists for JSON serialization
        analysis["symbols"] = sorted(list(analysis["symbols"]))
        analysis["metrics"] = sorted(list(analysis["metrics"]))
        
        # Generate recommendations
        if analysis["total_size_mb"] > 100:
            analysis["recommendations"].append("Use COMPRESSED cache level for large datasets")
        if len(analysis["symbols"]) > 20:
            analysis["recommendations"].append("Consider symbol-based cache partitioning")
        if analysis["total_files"] > 1000:
            analysis["recommendations"].append("Implement aggressive TTL policies")
        
        return analysis
    
    def migrate_legacy_cache(self, 
                           compression_threshold_mb: float = 1.0,
                           backup_legacy: bool = True) -> Dict[str, Any]:
        """
        Migrate legacy cache files to enhanced cache system.
        
        Args:
            compression_threshold_mb: Files larger than this will use compression
            backup_legacy: Whether to backup legacy cache before migration
            
        Returns:
            Migration report
        """
        if not self.legacy_cache_dir.exists():
            return {"error": "Legacy cache directory not found"}
        
        # Backup legacy cache if requested
        if backup_legacy:
            backup_dir = self.legacy_cache_dir.parent / f"legacy_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copytree(self.legacy_cache_dir, backup_dir)
            self.logger.info(f"Legacy cache backed up to: {backup_dir}")
        
        # Process each legacy cache file
        for cache_file in self.legacy_cache_dir.glob("*.json"):
            try:
                self.migration_stats["files_processed"] += 1
                
                # Parse filename
                filename_parts = cache_file.stem.split("_")
                if len(filename_parts) < 3:
                    self.logger.warning(f"Skipping file with invalid format: {cache_file}")
                    continue
                
                symbol = filename_parts[0]
                metric = "_".join(filename_parts[1:-1])
                date_str = filename_parts[-1]
                
                # Load legacy data
                with open(cache_file, 'r') as f:
                    legacy_data = json.load(f)
                
                # Extract data and metadata
                data = legacy_data.get("values", [])
                last_updated = legacy_data.get("last_updated")
                
                # Determine cache level based on file size
                file_size_mb = cache_file.stat().st_size / (1024 * 1024)
                cache_level = CacheLevel.COMPRESSED if file_size_mb > compression_threshold_mb else CacheLevel.DISK
                
                # Calculate TTL based on data freshness
                ttl_seconds = self._calculate_ttl(date_str, last_updated)
                
                # Migrate to enhanced cache
                success = self.enhanced_cache.put(
                    symbol=symbol,
                    metric_name=metric,
                    data=data,
                    ttl_seconds=ttl_seconds,
                    cache_level=cache_level,
                    tags=[f"migrated_from_legacy", f"date_{date_str}"]
                )
                
                if success:
                    self.migration_stats["files_migrated"] += 1
                    
                    # Calculate compression savings
                    if cache_level == CacheLevel.COMPRESSED:
                        # Estimate compression savings (actual savings calculated by cache manager)
                        estimated_savings = file_size_mb * 0.3  # Assume 30% compression
                        self.migration_stats["compression_savings"] += estimated_savings
                else:
                    self.migration_stats["files_failed"] += 1
                    self.migration_stats["errors"].append(f"Failed to migrate {cache_file}")
                
            except Exception as e:
                self.migration_stats["files_failed"] += 1
                error_msg = f"Error migrating {cache_file}: {e}"
                self.migration_stats["errors"].append(error_msg)
                self.logger.error(error_msg)
        
        # Generate migration report
        report = {
            "migration_completed": datetime.now().isoformat(),
            "stats": self.migration_stats,
            "enhanced_cache_info": self.enhanced_cache.get_cache_info(),
            "success_rate": self.migration_stats["files_migrated"] / max(1, self.migration_stats["files_processed"])
        }
        
        self.logger.info(f"Migration completed: {self.migration_stats['files_migrated']}/{self.migration_stats['files_processed']} files migrated")
        
        return report
    
    def _calculate_ttl(self, date_str: str, last_updated: Optional[str]) -> int:
        """
        Calculate appropriate TTL based on data age.
        
        Args:
            date_str: Date string from filename
            last_updated: Last updated timestamp
            
        Returns:
            TTL in seconds
        """
        try:
            # Parse date
            file_date = datetime.strptime(date_str, "%Y-%m-%d")
            now = datetime.now()
            age_days = (now - file_date).days
            
            # Adaptive TTL based on age
            if age_days == 0:  # Today's data
                return 3600  # 1 hour
            elif age_days <= 7:  # This week
                return 86400  # 24 hours
            elif age_days <= 30:  # This month
                return 604800  # 1 week
            else:  # Older data
                return 2592000  # 30 days
                
        except ValueError:
            return 3600  # Default 1 hour
    
    def cleanup_legacy_cache(self, confirm: bool = False) -> bool:
        """
        Remove legacy cache files after successful migration.
        
        Args:
            confirm: Confirmation flag to prevent accidental deletion
            
        Returns:
            Success status
        """
        if not confirm:
            self.logger.warning("Legacy cache cleanup requires explicit confirmation")
            return False
        
        try:
            if self.legacy_cache_dir.exists():
                shutil.rmtree(self.legacy_cache_dir)
                self.logger.info("Legacy cache directory removed")
                return True
        except Exception as e:
            self.logger.error(f"Failed to cleanup legacy cache: {e}")
            return False
    
    def validate_migration(self) -> Dict[str, Any]:
        """
        Validate the migration by comparing legacy and enhanced cache data.
        
        Returns:
            Validation report
        """
        validation_report = {
            "validation_time": datetime.now().isoformat(),
            "samples_checked": 0,
            "matches": 0,
            "mismatches": 0,
            "errors": []
        }
        
        # Sample a few files for validation
        legacy_files = list(self.legacy_cache_dir.glob("*.json"))[:10]  # Check first 10 files
        
        for cache_file in legacy_files:
            try:
                validation_report["samples_checked"] += 1
                
                # Parse filename
                filename_parts = cache_file.stem.split("_")
                if len(filename_parts) < 3:
                    continue
                
                symbol = filename_parts[0]
                metric = "_".join(filename_parts[1:-1])
                
                # Load legacy data
                with open(cache_file, 'r') as f:
                    legacy_data = json.load(f)
                
                # Get enhanced cache data
                enhanced_data = self.enhanced_cache.get(symbol, metric)
                
                # Compare data
                if enhanced_data == legacy_data.get("values"):
                    validation_report["matches"] += 1
                else:
                    validation_report["mismatches"] += 1
                    validation_report["errors"].append(f"Data mismatch for {symbol}:{metric}")
                
            except Exception as e:
                validation_report["errors"].append(f"Validation error for {cache_file}: {e}")
        
        validation_report["success_rate"] = validation_report["matches"] / max(1, validation_report["samples_checked"])
        
        return validation_report
