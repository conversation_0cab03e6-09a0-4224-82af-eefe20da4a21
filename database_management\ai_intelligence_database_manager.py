"""
AI Intelligence Database Manager for EOTS v2.5
==============================================

This module provides comprehensive database management for the AI intelligence
system, including agent lifecycle management, learning persistence, performance
tracking, and adaptive evolution capabilities.

The AI Intelligence Database serves as the "headquarters" where AI agents:
- Live and maintain their state
- Store and retrieve learning experiences
- Track performance and adapt thresholds
- Collaborate and share knowledge
- Evolve and multiply their capabilities

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "SENTIENT DATABASE OPERATIONS"
"""

import asyncio
import logging
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Union, Tuple
from decimal import Decimal
import uuid

# Pydantic imports for validation
from pydantic import BaseModel, Field, field_validator

# Database imports
try:
    import asyncpg
    import psycopg2
    from psycopg2.extras import RealDictCursor
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    logging.warning("Database libraries not available - AI Intelligence Database will use fallback mode")

# Import EOTS schemas
from data_models.eots_schemas_v2_5 import (
    FinalAnalysisBundleV2_5,
    ProcessedDataBundleV2_5
)

logger = logging.getLogger(__name__)

# ===== PYDANTIC MODELS FOR AI DATABASE =====

class AIAgentRecord(BaseModel):
    """Pydantic model for AI agent database records."""
    id: Optional[str] = None
    agent_name: str = Field(..., description="Unique name of the AI agent")
    agent_type: str = Field(..., description="Type of AI agent")
    agent_version: str = Field(default="1.0.0", description="Version of the agent")
    parent_agent_id: Optional[str] = None
    specialization: Optional[str] = None
    capabilities: Dict[str, Any] = Field(default_factory=dict)
    configuration: Dict[str, Any] = Field(default_factory=dict)
    performance_score: float = Field(default=0.5, ge=0.0, le=1.0)
    learning_rate: float = Field(default=0.1, ge=0.0, le=1.0)
    adaptation_score: float = Field(default=0.5, ge=0.0, le=1.0)
    status: str = Field(default="ACTIVE", pattern="^(ACTIVE|LEARNING|EVOLVING|DORMANT)$")
    total_analyses: int = Field(default=0, ge=0)
    successful_predictions: int = Field(default=0, ge=0)

class AILearningSession(BaseModel):
    """Pydantic model for AI learning session records."""
    id: Optional[str] = None
    agent_id: str = Field(..., description="ID of the learning agent")
    session_type: str = Field(..., description="Type of learning session")
    market_context: Dict[str, Any] = Field(..., description="Market conditions during learning")
    input_data: Dict[str, Any] = Field(..., description="Input data for the session")
    output_data: Dict[str, Any] = Field(..., description="Output generated by the agent")
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    actual_outcome: Optional[Dict[str, Any]] = None
    accuracy_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    learning_extracted: Optional[Dict[str, Any]] = None
    session_timestamp: datetime = Field(default_factory=datetime.now)

class AIMemoryRecord(BaseModel):
    """Pydantic model for AI memory bank records."""
    id: Optional[str] = None
    agent_id: str = Field(..., description="ID of the agent owning this memory")
    memory_type: str = Field(..., description="Type of memory")
    memory_category: Optional[str] = None
    pattern_signature: str = Field(..., description="Unique pattern identifier")
    pattern_data: Dict[str, Any] = Field(..., description="The pattern data")
    success_rate: float = Field(default=0.5, ge=0.0, le=1.0)
    confidence_level: float = Field(default=0.5, ge=0.0, le=1.0)
    usage_count: int = Field(default=0, ge=0)
    memory_strength: float = Field(default=1.0, ge=0.0, le=1.0)

class AIAdaptiveThreshold(BaseModel):
    """Pydantic model for adaptive threshold records."""
    id: Optional[str] = None
    agent_id: str = Field(..., description="ID of the agent owning this threshold")
    threshold_name: str = Field(..., description="Name of the threshold")
    threshold_category: Optional[str] = None
    current_value: float = Field(..., description="Current threshold value")
    default_value: float = Field(..., description="Default threshold value")
    min_allowed_value: Optional[float] = None
    max_allowed_value: Optional[float] = None
    adaptation_rate: float = Field(default=0.05, ge=0.0, le=1.0)
    performance_correlation: Optional[float] = Field(None, ge=-1.0, le=1.0)

class AIPerformanceMetrics(BaseModel):
    """Pydantic model for AI performance metrics."""
    id: Optional[str] = None
    agent_id: str = Field(..., description="ID of the agent")
    metric_date: datetime = Field(..., description="Date of the metrics")
    symbol: Optional[str] = None
    total_predictions: int = Field(default=0, ge=0)
    correct_predictions: int = Field(default=0, ge=0)
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    average_confidence: float = Field(default=0.0, ge=0.0, le=1.0)
    confidence_calibration: float = Field(default=0.0, ge=0.0, le=1.0)
    learning_velocity: float = Field(default=0.0, ge=0.0, le=1.0)
    adaptation_effectiveness: float = Field(default=0.0, ge=0.0, le=1.0)

# ===== AI INTELLIGENCE DATABASE MANAGER =====

class AIIntelligenceDatabaseManager:
    """
    COMPREHENSIVE AI INTELLIGENCE DATABASE MANAGER
    
    This class manages all database operations for the AI intelligence system,
    providing a headquarters for AI agents to live, learn, and evolve.
    """
    
    def __init__(self, connection_config: Optional[Dict[str, Any]] = None):
        self.connection_config = connection_config or self._get_default_config()
        self.logger = logger.getChild(self.__class__.__name__)
        self.connection_pool = None
        self.is_connected = False
        
        # Performance tracking
        self.operation_count = 0
        self.error_count = 0
        self.last_health_check = None
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default database configuration."""
        return {
            "host": "localhost",
            "port": 5432,
            "database": "eots_ai_intelligence",
            "user": "postgres",
            "password": "password",
            "min_connections": 2,
            "max_connections": 10,
            "command_timeout": 30
        }
    
    async def initialize_connection(self) -> bool:
        """Initialize database connection pool."""
        if not DATABASE_AVAILABLE:
            self.logger.warning("Database libraries not available - using fallback mode")
            return False
            
        try:
            self.connection_pool = await asyncpg.create_pool(
                host=self.connection_config["host"],
                port=self.connection_config["port"],
                database=self.connection_config["database"],
                user=self.connection_config["user"],
                password=self.connection_config["password"],
                min_size=self.connection_config["min_connections"],
                max_size=self.connection_config["max_connections"],
                command_timeout=self.connection_config["command_timeout"]
            )
            
            self.is_connected = True
            self.logger.info("🏛️ AI Intelligence Database connection established")
            
            # Perform health check
            await self._health_check()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI Intelligence Database: {e}")
            self.is_connected = False
            return False
    
    async def close_connection(self):
        """Close database connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            self.is_connected = False
            self.logger.info("🏛️ AI Intelligence Database connection closed")
    
    async def _health_check(self) -> bool:
        """Perform database health check."""
        try:
            async with self.connection_pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                self.last_health_check = datetime.now()
                return result == 1
        except Exception as e:
            self.logger.error(f"AI Intelligence Database health check failed: {e}")
            return False
    
    # ===== AI AGENT MANAGEMENT =====
    
    async def register_agent(self, agent_record: AIAgentRecord) -> str:
        """Register a new AI agent in the database."""
        try:
            async with self.connection_pool.acquire() as conn:
                agent_id = str(uuid.uuid4())
                
                await conn.execute("""
                    INSERT INTO ai_agents (
                        id, agent_name, agent_type, agent_version, parent_agent_id,
                        specialization, capabilities, configuration, performance_score,
                        learning_rate, adaptation_score, status, total_analyses, successful_predictions
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                """, 
                agent_id, agent_record.agent_name, agent_record.agent_type,
                agent_record.agent_version, agent_record.parent_agent_id,
                agent_record.specialization, json.dumps(agent_record.capabilities),
                json.dumps(agent_record.configuration), agent_record.performance_score,
                agent_record.learning_rate, agent_record.adaptation_score,
                agent_record.status, agent_record.total_analyses, agent_record.successful_predictions)
                
                self.logger.info(f"🤖 Registered AI agent: {agent_record.agent_name} ({agent_id})")
                return agent_id
                
        except Exception as e:
            self.logger.error(f"Failed to register AI agent {agent_record.agent_name}: {e}")
            self.error_count += 1
            raise
    
    async def get_agent(self, agent_id: str) -> Optional[AIAgentRecord]:
        """Retrieve an AI agent by ID."""
        try:
            async with self.connection_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM ai_agents WHERE id = $1
                """, agent_id)
                
                if row:
                    return AIAgentRecord(
                        id=str(row['id']),
                        agent_name=row['agent_name'],
                        agent_type=row['agent_type'],
                        agent_version=row['agent_version'],
                        parent_agent_id=str(row['parent_agent_id']) if row['parent_agent_id'] else None,
                        specialization=row['specialization'],
                        capabilities=json.loads(row['capabilities']) if row['capabilities'] else {},
                        configuration=json.loads(row['configuration']) if row['configuration'] else {},
                        performance_score=float(row['performance_score']),
                        learning_rate=float(row['learning_rate']),
                        adaptation_score=float(row['adaptation_score']),
                        status=row['status'],
                        total_analyses=row['total_analyses'],
                        successful_predictions=row['successful_predictions']
                    )
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to retrieve AI agent {agent_id}: {e}")
            self.error_count += 1
            return None
    
    async def update_agent_performance(self, agent_id: str, performance_score: float, 
                                     adaptation_score: Optional[float] = None) -> bool:
        """Update an AI agent's performance metrics."""
        try:
            async with self.connection_pool.acquire() as conn:
                if adaptation_score is not None:
                    await conn.execute("""
                        UPDATE ai_agents 
                        SET performance_score = $1, adaptation_score = $2, updated_at = NOW()
                        WHERE id = $3
                    """, performance_score, adaptation_score, agent_id)
                else:
                    await conn.execute("""
                        UPDATE ai_agents 
                        SET performance_score = $1, updated_at = NOW()
                        WHERE id = $2
                    """, performance_score, agent_id)
                
                self.logger.debug(f"Updated performance for agent {agent_id}: {performance_score}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to update agent performance {agent_id}: {e}")
            self.error_count += 1
            return False

    # ===== LEARNING SESSION MANAGEMENT =====

    async def record_learning_session(self, session: AILearningSession) -> str:
        """Record a new learning session."""
        try:
            async with self.connection_pool.acquire() as conn:
                session_id = str(uuid.uuid4())

                await conn.execute("""
                    INSERT INTO ai_learning_sessions (
                        id, agent_id, session_type, market_context, input_data,
                        output_data, confidence_score, actual_outcome, accuracy_score,
                        learning_extracted, session_timestamp
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                """,
                session_id, session.agent_id, session.session_type,
                json.dumps(session.market_context), json.dumps(session.input_data),
                json.dumps(session.output_data), session.confidence_score,
                json.dumps(session.actual_outcome) if session.actual_outcome else None,
                session.accuracy_score,
                json.dumps(session.learning_extracted) if session.learning_extracted else None,
                session.session_timestamp)

                self.logger.debug(f"📚 Recorded learning session {session_id} for agent {session.agent_id}")
                return session_id

        except Exception as e:
            self.logger.error(f"Failed to record learning session: {e}")
            self.error_count += 1
            raise

    async def update_learning_outcome(self, session_id: str, actual_outcome: Dict[str, Any],
                                    accuracy_score: float, learning_extracted: Dict[str, Any]) -> bool:
        """Update a learning session with actual outcomes."""
        try:
            async with self.connection_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE ai_learning_sessions
                    SET actual_outcome = $1, accuracy_score = $2, learning_extracted = $3,
                        validation_timestamp = NOW(), feedback_incorporated = TRUE
                    WHERE id = $4
                """, json.dumps(actual_outcome), accuracy_score, json.dumps(learning_extracted), session_id)

                self.logger.debug(f"📊 Updated learning outcome for session {session_id}")
                return True

        except Exception as e:
            self.logger.error(f"Failed to update learning outcome {session_id}: {e}")
            self.error_count += 1
            return False

    async def get_learning_history(self, agent_id: str, limit: int = 100) -> List[AILearningSession]:
        """Get learning history for an agent."""
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT * FROM ai_learning_sessions
                    WHERE agent_id = $1
                    ORDER BY session_timestamp DESC
                    LIMIT $2
                """, agent_id, limit)

                sessions = []
                for row in rows:
                    sessions.append(AILearningSession(
                        id=str(row['id']),
                        agent_id=str(row['agent_id']),
                        session_type=row['session_type'],
                        market_context=json.loads(row['market_context']),
                        input_data=json.loads(row['input_data']),
                        output_data=json.loads(row['output_data']),
                        confidence_score=float(row['confidence_score']),
                        actual_outcome=json.loads(row['actual_outcome']) if row['actual_outcome'] else None,
                        accuracy_score=float(row['accuracy_score']) if row['accuracy_score'] else None,
                        learning_extracted=json.loads(row['learning_extracted']) if row['learning_extracted'] else None,
                        session_timestamp=row['session_timestamp']
                    ))

                return sessions

        except Exception as e:
            self.logger.error(f"Failed to get learning history for agent {agent_id}: {e}")
            self.error_count += 1
            return []

    # ===== MEMORY MANAGEMENT =====

    async def store_memory(self, memory: AIMemoryRecord) -> str:
        """Store a new memory in the AI memory bank."""
        try:
            async with self.connection_pool.acquire() as conn:
                memory_id = str(uuid.uuid4())

                await conn.execute("""
                    INSERT INTO ai_memory_bank (
                        id, agent_id, memory_type, memory_category, pattern_signature,
                        pattern_data, success_rate, confidence_level, usage_count, memory_strength
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """,
                memory_id, memory.agent_id, memory.memory_type, memory.memory_category,
                memory.pattern_signature, json.dumps(memory.pattern_data),
                memory.success_rate, memory.confidence_level, memory.usage_count, memory.memory_strength)

                self.logger.debug(f"🧠 Stored memory {memory_id} for agent {memory.agent_id}")
                return memory_id

        except Exception as e:
            self.logger.error(f"Failed to store memory: {e}")
            self.error_count += 1
            raise

    async def retrieve_memories(self, agent_id: str, memory_type: Optional[str] = None,
                              memory_category: Optional[str] = None, limit: int = 50) -> List[AIMemoryRecord]:
        """Retrieve memories for an agent."""
        try:
            async with self.connection_pool.acquire() as conn:
                query = """
                    SELECT * FROM ai_memory_bank
                    WHERE agent_id = $1
                """
                params = [agent_id]

                if memory_type:
                    query += " AND memory_type = $2"
                    params.append(memory_type)

                if memory_category:
                    query += f" AND memory_category = ${len(params) + 1}"
                    params.append(memory_category)

                query += f" ORDER BY memory_strength DESC, usage_count DESC LIMIT ${len(params) + 1}"
                params.append(limit)

                rows = await conn.fetch(query, *params)

                memories = []
                for row in rows:
                    memories.append(AIMemoryRecord(
                        id=str(row['id']),
                        agent_id=str(row['agent_id']),
                        memory_type=row['memory_type'],
                        memory_category=row['memory_category'],
                        pattern_signature=row['pattern_signature'],
                        pattern_data=json.loads(row['pattern_data']),
                        success_rate=float(row['success_rate']),
                        confidence_level=float(row['confidence_level']),
                        usage_count=row['usage_count'],
                        memory_strength=float(row['memory_strength'])
                    ))

                return memories

        except Exception as e:
            self.logger.error(f"Failed to retrieve memories for agent {agent_id}: {e}")
            self.error_count += 1
            return []

    async def update_memory_usage(self, memory_id: str, success: bool) -> bool:
        """Update memory usage statistics."""
        try:
            async with self.connection_pool.acquire() as conn:
                if success:
                    await conn.execute("""
                        UPDATE ai_memory_bank
                        SET usage_count = usage_count + 1,
                            last_successful_use = NOW(),
                            success_rate = CASE
                                WHEN usage_count = 0 THEN 1.0
                                ELSE (success_rate * usage_count + 1.0) / (usage_count + 1)
                            END,
                            memory_strength = LEAST(memory_strength + 0.1, 1.0),
                            updated_at = NOW()
                        WHERE id = $1
                    """, memory_id)
                else:
                    await conn.execute("""
                        UPDATE ai_memory_bank
                        SET usage_count = usage_count + 1,
                            last_failed_use = NOW(),
                            success_rate = CASE
                                WHEN usage_count = 0 THEN 0.0
                                ELSE (success_rate * usage_count) / (usage_count + 1)
                            END,
                            memory_strength = GREATEST(memory_strength - 0.05, 0.1),
                            updated_at = NOW()
                        WHERE id = $1
                    """, memory_id)

                return True

        except Exception as e:
            self.logger.error(f"Failed to update memory usage {memory_id}: {e}")
            self.error_count += 1
            return False
