#!/usr/bin/env python
# elite_options_system_v2_5/run_system_dashboard_v2_5.py
# EOTS v2.5 - SENTRY-APPROVED, CANONICAL ENTRY POINT

"""
Elite Options Trading System Dashboard Runner (V2.5 - "Apex Predator")

This is the primary and definitive launcher for the EOTS v2.5 Dashboard.
Its sole responsibilities are:
1. Loading environment variables from the .env file.
2. Configuring the system-wide logger for standard output.
3. Ensuring the Python environment is correctly configured for module resolution
   by adding the project root to the system path.
4. Delegating execution to the main application function.

This script is intentionally lean and does not import or configure specific
application components. It only prepares the environment for the application
core to run successfully.
"""

import os
import sys
import logging
import tempfile
from dotenv import load_dotenv
from pydantic import ValidationError
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import pandas as pd # Will be used where DataFrames are unavoidable
from data_models.eots_schemas_v2_5 import EOTSConfigV2_5
import subprocess
from data_management.database_manager_v2_5 import DatabaseManagerV2_5

# Process lock to prevent multiple instances
LOCK_FILE = os.path.join(tempfile.gettempdir(), 'eots_v2_5.lock')

def acquire_lock():
    """Acquire a file lock to prevent multiple instances."""
    try:
        lock_fd = os.open(LOCK_FILE, os.O_CREAT | os.O_EXCL | os.O_RDWR)
        os.write(lock_fd, str(os.getpid()).encode())
        return lock_fd
    except OSError:
        # Lock file exists, try to remove it and retry once
        try:
            os.remove(LOCK_FILE)
            lock_fd = os.open(LOCK_FILE, os.O_CREAT | os.O_EXCL | os.O_RDWR)
            os.write(lock_fd, str(os.getpid()).encode())
            return lock_fd
        except OSError:
            print(f"EOTS v2.5 may already be running. If not, delete: {LOCK_FILE}")
            return None

def release_lock(lock_fd):
    """Release the file lock."""
    if lock_fd is not None:
        try:
            os.close(lock_fd)
            os.remove(LOCK_FILE)
        except OSError:
            pass

# Configure root logger first so we can see the environment loading
logging.basicConfig(
    level=logging.DEBUG,
    format='[%(asctime)s] [%(levelname)s] [%(name)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Ensure package root is treated as a package
package_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if package_root not in sys.path:
    sys.path.insert(0, package_root)

# Log the .env file path we're trying to load
env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
logger.info(f"Attempting to load .env file from: {env_path}")

# Load environment variables from .env file
if not load_dotenv(env_path):
    logger.warning("No .env file found. Please create a .env file with the required environment variables.")
    logger.warning("Required environment variables:")
    logger.warning("- CONVEX_EMAIL")
    logger.warning("- CONVEX_PASSWORD")
    logger.warning("- TRADIER_PRODUCTION_TOKEN")
    logger.warning("- DB_HOST")
    logger.warning("- DB_PORT")
    logger.warning("- DB_NAME")
    logger.warning("- DB_USER")
    logger.warning("- DB_PASSWORD")

# Debug: Print environment variables
logger.info("Environment variables after loading .env:")
logger.info(f"CONVEX_EMAIL exists: {'CONVEX_EMAIL' in os.environ}")
logger.info(f"CONVEX_PASSWORD exists: {'CONVEX_PASSWORD' in os.environ}")
logger.info(f"TRADIER_PRODUCTION_TOKEN exists: {'TRADIER_PRODUCTION_TOKEN' in os.environ}")

# Log path integrity check
if package_root in sys.path:
    logger.debug("Path Integrity Check: Package root is already in sys.path.")
else:
    logger.debug("Path Integrity Check: Package root is not in sys.path.")

# Import root package to ensure all modules are properly initialized
try:
    import elite_options_system_v2_5
    logger.info("EOTS v2.5 root package initialized")
except ImportError as e:
    logger.error(f"Failed to import root package: {e}")
    sys.exit(1)

logger.info("Successfully imported root package")

# Import application core
from dashboard_application.app_main import main

# --- Initialize Database Manager (Supabase/Postgres Connection) ---
db_manager = None
try:
    db_manager = DatabaseManagerV2_5(db_config={})  # Uses env vars for credentials
    logger.info("DatabaseManagerV2_5 successfully connected to Supabase/Postgres backend.")
except Exception as e:
    logger.critical(f"Failed to initialize DatabaseManagerV2_5: {e}")
    sys.exit(1)

# Make db_manager globally accessible for other modules
import builtins
setattr(builtins, 'db_manager', db_manager)

# --- Initialize AI Intelligence Database (Agent Headquarters) ---
ai_intelligence_db = None
try:
    logger.info("🏛️ Initializing AI Intelligence Database (Agent Headquarters)...")
    from database_management.ai_intelligence_integration import get_ai_database_integration

    # Initialize AI Intelligence Database with existing db_manager config
    import asyncio

    async def init_ai_db():
        return await get_ai_database_integration(db_manager.db_config if db_manager else None)

    # Run async initialization
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    ai_intelligence_db = loop.run_until_complete(init_ai_db())
    loop.close()

    if ai_intelligence_db and ai_intelligence_db.is_initialized:
        logger.info("✅ AI Intelligence Database initialized successfully!")
        logger.info("🧠 AI Agent headquarters is now active:")
        logger.info("   - Agent registration: Automatic")
        logger.info("   - Learning persistence: Enabled")
        logger.info("   - Memory storage: Active")
        logger.info("   - Performance tracking: Running")
        logger.info("   - Adaptive thresholds: Learning")

        # Make globally accessible
        setattr(builtins, 'ai_intelligence_db', ai_intelligence_db)

        # Create convenient global functions for AI intelligence
        def show_ai_agents():
            """Display all registered AI agents."""
            return asyncio.run(ai_intelligence_db.get_system_overview())

        def get_agent_performance(agent_name: str = "MarketAnalystAgent", days: int = 7):
            """Get performance insights for an AI agent."""
            return asyncio.run(ai_intelligence_db.get_learning_insights(agent_name, days))

        def show_ai_system_health():
            """Display AI system health and intelligence metrics."""
            return asyncio.run(ai_intelligence_db.get_system_overview())

        # Make these functions globally accessible
        setattr(builtins, 'show_ai_agents', show_ai_agents)
        setattr(builtins, 'get_agent_performance', get_agent_performance)
        setattr(builtins, 'show_ai_system_health', show_ai_system_health)

    else:
        logger.warning("⚠️ AI Intelligence Database using fallback mode")
        ai_intelligence_db = None

except Exception as e:
    logger.error(f"❌ Failed to initialize AI Intelligence Database: {e}")
    logger.warning("⚠️ Continuing without AI Intelligence Database")
    ai_intelligence_db = None

# --- Initialize Adaptive Learning System (Pydantic AI Self-Learning) ---
adaptive_learning_system = None
try:
    logger.info("🧠 Initializing Adaptive Learning System (Pydantic AI)...")
    from core_analytics_engine.adaptive_learning_integration_v2_5 import AdaptiveLearningIntegrationV2_5

    # Initialize with database manager and config manager (placeholder for now)
    adaptive_learning_system = AdaptiveLearningIntegrationV2_5(db_manager, config_manager=None)

    # Initialize the learning system
    learning_success = adaptive_learning_system.initialize_adaptive_learning()

    if learning_success:
        logger.info("✅ Adaptive Learning System initialized successfully!")
        logger.info("🔄 Self-learning capabilities are now active:")
        logger.info("   - Daily learning cycles: Every day at 02:00")
        logger.info("   - Weekly deep learning: Every Sunday at 03:00")
        logger.info("   - Monthly comprehensive review: First day of each month")
        logger.info("   - Real-time parameter optimization: Enabled")

        # Make learning system globally accessible
        setattr(builtins, 'adaptive_learning_system', adaptive_learning_system)

        # Create learning status display for easy access
        from core_analytics_engine.learning_status_display_v2_5 import LearningStatusDisplayV2_5
        learning_status_display = LearningStatusDisplayV2_5(adaptive_learning_system)
        setattr(builtins, 'learning_status_display', learning_status_display)

        # Create convenient global functions for learning status
        def show_learning_status():
            """Quick function to display learning system status."""
            return learning_status_display.display_learning_dashboard()

        def learning_quick_status():
            """Quick one-line learning status."""
            return learning_status_display.get_quick_status()

        def show_parameter_history(param_name: str, days: int = 30):
            """Show history for a specific parameter."""
            return learning_status_display.display_parameter_history(param_name, days)

        def show_radar_performance(symbol: str = "SPY", days: int = 7):
            """Show Market Dynamics radar performance analysis."""
            return adaptive_learning_system.get_radar_performance_analysis(symbol, days)

        def optimize_radar_performance(symbol: str = "SPY", days: int = 30):
            """Optimize Market Dynamics radar thresholds based on historical data."""
            return adaptive_learning_system.optimize_radar_thresholds(symbol, days)

        # Make these functions globally accessible
        setattr(builtins, 'show_learning_status', show_learning_status)
        setattr(builtins, 'learning_quick_status', learning_quick_status)
        setattr(builtins, 'show_parameter_history', show_parameter_history)
        setattr(builtins, 'show_radar_performance', show_radar_performance)
        setattr(builtins, 'optimize_radar_performance', optimize_radar_performance)

        # Run initial learning cycle in background (non-blocking)
        import asyncio
        import threading

        def run_initial_learning():
            """Run initial learning cycle in background thread."""
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                logger.info("🔄 Running initial learning cycle...")
                initial_result = loop.run_until_complete(
                    adaptive_learning_system.run_daily_learning_cycle("SPY")
                )

                logger.info(f"📈 Initial learning completed:")
                logger.info(f"   - Insights: {len(initial_result.insights_generated)}")
                logger.info(f"   - Updates: {len(initial_result.parameters_updated)}")
                logger.info(f"   - Confidence: {initial_result.confidence_score:.2f}")

                loop.close()

            except Exception as e:
                logger.error(f"Initial learning cycle failed: {e}")

        # Start initial learning in background thread
        learning_thread = threading.Thread(target=run_initial_learning, daemon=True)
        learning_thread.start()

    else:
        logger.warning("⚠️ Adaptive Learning System initialization failed - continuing without self-learning")

except Exception as e:
    logger.error(f"❌ Failed to initialize Adaptive Learning System: {e}")
    logger.warning("⚠️ Continuing without self-learning capabilities")
    adaptive_learning_system = None

if __name__ == "__main__":
    # Acquire process lock to prevent multiple instances
    lock_fd = acquire_lock()
    if lock_fd is None:
        print("Another instance of EOTS v2.5 is already running. Exiting.")
        sys.exit(1)
    
    # --- Start Intraday Collector as Background Process ---
    try:
        collector_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'run_intraday_collector.py')
        logger.info(f"Launching Intraday Collector: {collector_script}")
        subprocess.Popen([sys.executable, collector_script])
        logger.info("Intraday Collector started in background.")
    except Exception as e:
        logger.error(f"Failed to launch Intraday Collector: {e}")
    
    logger.info("EOTS v2.5 'Apex Predator' Initialization Sequence Activated.")
    logger.info("Handoff to Application Core...")
    try:
        main()
    except ValidationError as e:
        logger.critical(f"Configuration validation failed: {e}")
        logger.critical("Please check your config_v2_5.json file for errors.")
        release_lock(lock_fd)
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal. Shutting down gracefully...")

        # Gracefully shutdown AI Intelligence Database
        if ai_intelligence_db:
            try:
                logger.info("🏛️ Stopping AI Intelligence Database...")
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(ai_intelligence_db.close())
                loop.close()
                logger.info("✅ AI Intelligence Database stopped gracefully")
            except Exception as e:
                logger.error(f"Error stopping AI Intelligence Database: {e}")

        # Gracefully shutdown adaptive learning system
        if adaptive_learning_system:
            try:
                logger.info("🛑 Stopping Adaptive Learning System...")
                adaptive_learning_system.stop_learning_scheduler()
                logger.info("✅ Adaptive Learning System stopped gracefully")
            except Exception as e:
                logger.error(f"Error stopping learning system: {e}")

        release_lock(lock_fd)
        sys.exit(0)
    except Exception as e:
        logger.critical("CATASTROPHIC FAILURE: An unhandled exception occurred during application startup.", exc_info=True)
        release_lock(lock_fd)
        sys.exit(1)
    finally:
        logger.info("Execution complete. EOTS shutting down.")

        # Ensure AI Intelligence Database is properly shutdown
        if ai_intelligence_db:
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(ai_intelligence_db.close())
                loop.close()
                logger.info("🏛️ AI Intelligence Database shutdown completed")
            except Exception as e:
                logger.error(f"Error during AI Intelligence Database shutdown: {e}")

        # Ensure adaptive learning system is properly shutdown
        if adaptive_learning_system:
            try:
                adaptive_learning_system.stop_learning_scheduler()
                logger.info("🔄 Adaptive Learning System shutdown completed")
            except Exception as e:
                logger.error(f"Error during learning system shutdown: {e}")

        release_lock(lock_fd)
