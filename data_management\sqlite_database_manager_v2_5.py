# data_management/sqlite_database_manager_v2_5.py
# EOTS v2.5 - SQLite Database Manager

import logging
import sqlite3
from typing import Dict, Any, Optional, List
from datetime import date, datetime
import pandas as pd
import os
import json

logger = logging.getLogger(__name__)

class SQLiteDatabaseManagerV2_5:
    """
    SQLite-compatible database manager for the EOTS v2.5 system.
    Handles SQLite-specific syntax and operations.
    """

    def __init__(self, db_config: Dict[str, Any] = None):
        self.logger = logger.getChild(self.__class__.__name__)
        self._db_config = db_config or {}
        self._conn = None
        self.connection_status = "DISCONNECTED"
        self._connect()

    def _connect(self):
        """Connect to SQLite database."""
        try:
            # Default to local SQLite database
            db_path = self._db_config.get('db_path', 'data/elite_options.db')
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            self._conn = sqlite3.connect(db_path, check_same_thread=False)
            self._conn.row_factory = sqlite3.Row  # Enable dict-like access
            self._conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign keys
            self.connection_status = "CONNECTED"
            self.logger.info(f"SQLiteDatabaseManagerV2_5 connected to: {db_path}")
        except Exception as e:
            self.connection_status = "FAILED"
            self.logger.critical(f"Failed to connect to SQLite database: {e}")
            raise

    def get_connection(self) -> sqlite3.Connection:
        """Get the database connection."""
        return self._conn

    def close_connection(self):
        """Close the database connection."""
        if self._conn:
            self._conn.close()
            self.logger.info("SQLite database connection closed.")
            self.connection_status = "CLOSED"

    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a raw SQL query and return results."""
        try:
            cursor = self._conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # Check if this is a SELECT query
            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
            else:
                self._conn.commit()
                return []
        except Exception as e:
            self.logger.error(f"Failed to execute query: {e}")
            raise

    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database."""
        try:
            cursor = self._conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            return cursor.fetchone() is not None
        except Exception as e:
            self.logger.error(f"Failed to check table existence: {e}")
            return False

    def column_exists(self, table_name: str, column_name: str) -> bool:
        """Check if a column exists in a table."""
        try:
            cursor = self._conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in cursor.fetchall()]
            return column_name in columns
        except Exception as e:
            self.logger.error(f"Failed to check column existence: {e}")
            return False

    def initialize_database_schema(self) -> None:
        """Initialize the database schema with SQLite-compatible syntax."""
        
        # SQLite-compatible table creation statements
        sql_create_daily_ohlcv = """
        CREATE TABLE IF NOT EXISTS daily_ohlcv (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            date DATE NOT NULL,
            open REAL NOT NULL,
            high REAL NOT NULL,
            low REAL NOT NULL,
            close REAL NOT NULL,
            volume INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, date)
        );"""

        sql_create_daily_eots_metrics = """
        CREATE TABLE IF NOT EXISTS daily_eots_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            date DATE NOT NULL,
            gib_oi_based_und REAL,
            ivsdh_und_avg REAL,
            vapi_fa_z_score_und REAL,
            dwfd_z_score_und REAL,
            tw_laf_z_score_und REAL,
            market_regime_summary TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, date)
        );"""

        sql_create_ai_predictions = """
        CREATE TABLE IF NOT EXISTS ai_predictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            prediction_type TEXT NOT NULL,
            prediction_value REAL,
            prediction_direction TEXT CHECK (prediction_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            confidence_score REAL DEFAULT 0.0000,
            time_horizon TEXT NOT NULL,
            prediction_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            target_timestamp DATETIME,
            actual_value REAL,
            actual_direction TEXT CHECK (actual_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            prediction_accurate BOOLEAN,
            accuracy_score REAL,
            model_version TEXT DEFAULT 'v2.5',
            market_context TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        sql_create_ai_adaptations = """
        CREATE TABLE IF NOT EXISTS ai_adaptations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            adaptation_type TEXT NOT NULL,
            adaptation_description TEXT,
            success_rate REAL DEFAULT 0.0000,
            adaptation_score REAL DEFAULT 0.0000,
            market_context TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        sql_create_ai_insights_history = """
        CREATE TABLE IF NOT EXISTS ai_insights_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            insight_type TEXT NOT NULL,
            insight_content TEXT NOT NULL,
            confidence_score REAL DEFAULT 0.0000,
            impact_score REAL DEFAULT 0.0000,
            market_context TEXT DEFAULT '{}',
            outcome_verified BOOLEAN DEFAULT FALSE,
            outcome_accuracy REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        sql_create_ai_learning_patterns = """
        CREATE TABLE IF NOT EXISTS ai_learning_patterns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            pattern_name TEXT NOT NULL,
            pattern_description TEXT,
            market_conditions TEXT DEFAULT '{}',
            success_rate REAL DEFAULT 0.0000,
            confidence_score REAL DEFAULT 0.0000,
            sample_size INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        sql_create_memory_entities = """
        CREATE TABLE IF NOT EXISTS memory_entities (
            entity_id TEXT PRIMARY KEY,
            entity_type TEXT NOT NULL,
            entity_name TEXT NOT NULL,
            description TEXT,
            symbol TEXT NOT NULL,
            confidence_score REAL DEFAULT 0.0000,
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
            access_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE
        );"""

        sql_create_memory_relations = """
        CREATE TABLE IF NOT EXISTS memory_relations (
            relation_id TEXT PRIMARY KEY,
            source_entity_id TEXT NOT NULL,
            target_entity_id TEXT NOT NULL,
            relation_type TEXT NOT NULL,
            relation_strength REAL DEFAULT 0.5000,
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_validated DATETIME DEFAULT CURRENT_TIMESTAMP,
            validation_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE
        );"""

        commands = [
            sql_create_daily_ohlcv,
            sql_create_daily_eots_metrics,
            sql_create_ai_predictions,
            sql_create_ai_adaptations,
            sql_create_ai_insights_history,
            sql_create_ai_learning_patterns,
            sql_create_memory_entities,
            sql_create_memory_relations
        ]

        try:
            cursor = self._conn.cursor()
            for cmd in commands:
                cursor.execute(cmd)
            self._conn.commit()
            self.logger.info("SQLite database schema initialized successfully.")
        except Exception as e:
            self.logger.error(f"Failed to initialize SQLite schema: {e}")
            raise

    def insert_record(self, table_name: str, data: Dict[str, Any]) -> None:
        """Insert a record into the specified table."""
        try:
            columns = list(data.keys())
            placeholders = ['?' for _ in columns]
            values = list(data.values())
            
            query = f"INSERT OR IGNORE INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            
            cursor = self._conn.cursor()
            cursor.execute(query, values)
            self._conn.commit()
            self.logger.debug(f"Inserted record into {table_name}.")
        except Exception as e:
            self.logger.error(f"Failed to insert record into {table_name}: {e}")
            raise
