"""
AI Dashboard Callbacks for Collapsible Info Sections
===================================================

This module contains Dash callbacks for handling collapsible information
sections in the AI dashboard modules.
"""

import logging
from dash import Input, Output, State, callback, html
from typing import Dict, Any

logger = logging.getLogger(__name__)

# AI Module Information Blurbs
AI_MODULE_INFO = {
    "unified_intelligence": """🧠 Unified AI Intelligence Hub: This is your COMMAND CENTER for all AI-powered market analysis. The 4-quadrant layout provides: TOP-LEFT: AI Confidence Barometer showing system conviction levels with real-time data quality scoring. TOP-RIGHT: Signal Confluence Barometer measuring agreement between multiple EOTS metrics (VAPI-FA, DWFD, TW-LAF, GIB). BOTTOM-LEFT: Unified Intelligence Analysis combining Alpha Vantage news sentiment, MCP server insights, and ATIF recommendations. BOTTOM-RIGHT: Market Dynamics Radar showing 6-dimensional market forces (Volatility, Flow, Momentum, Structure, Sentiment, Risk). 💡 TRADING INSIGHT: When AI Confidence > 80% AND Signal Confluence > 70% = HIGH CONVICTION setup. Watch for Market Dynamics radar showing EXTREME readings (outer edges) = potential breakout/breakdown. The Unified Intelligence text provides CONTEXTUAL NARRATIVE explaining WHY the system is confident. This updates every 15 minutes with fresh data integration!""",
    
    "regime_analysis": """🌊 AI Regime Analysis: This 4-quadrant system identifies and analyzes the CURRENT MARKET REGIME using advanced EOTS metrics. TOP-LEFT: Regime Confidence Barometer showing conviction in current regime classification with transition risk assessment. TOP-RIGHT: Regime Characteristics Analysis displaying 4 key market properties (Volatility, Flow Direction, Risk Level, Momentum) with DYNAMIC COLOR CODING. BOTTOM-LEFT: Enhanced AI Analysis showing current regime name, key Z-score metrics (VAPI-FA, DWFD, TW-LAF), and AI-generated insights. BOTTOM-RIGHT: Transition Gauge measuring probability of regime change with stability metrics. 💡 TRADING INSIGHT: Regime Confidence > 70% = STABLE regime, trade WITH the characteristics. Transition Risk > 60% = UNSTABLE regime, expect volatility and potential reversals. When characteristics show EXTREME values (Very High/Low) = regime at INFLECTION POINT. Use regime insights to adjust position sizing and strategy selection!""",
    
    "raw_metrics": """🔢 Raw EOTS Metrics Dashboard: This displays the CORE EOTS v2.5 metrics in their purest form, validated against Pydantic schemas. Shows real-time Z-scores for: VAPI-FA (Volume-Adjusted Put/Call Imbalance with Flow Alignment), DWFD (Delta-Weighted Flow Direction), TW-LAF (Time-Weighted Liquidity-Adjusted Flow), GIB (Gamma Imbalance Barometer), and underlying price/volume data. Each metric is STANDARDIZED to Z-scores for easy comparison. 💡 TRADING INSIGHT: Z-scores > +2.0 = EXTREMELY BULLISH signal. Z-scores < -2.0 = EXTREMELY BEARISH signal. Z-scores between -1.0 and +1.0 = NEUTRAL/CONSOLIDATION. When MULTIPLE metrics show same direction (all positive or all negative) = HIGH CONVICTION directional bias. DIVERGENCE between metrics = UNCERTAINTY, potential for volatility. These are the RAW BUILDING BLOCKS that feed into all other AI analysis!""",
    
    "recommendations": """🎯 AI Recommendations Engine: This panel displays ADAPTIVE TRADE IDEA FRAMEWORK (ATIF) generated strategies with AI-enhanced conviction scoring. Each recommendation includes: Strategy Type, Conviction Level (0-100%), AI-generated Rationale, and Risk Assessment. The system combines EOTS metrics, regime analysis, and market structure to generate ACTIONABLE trade ideas. 💡 TRADING INSIGHT: Conviction > 80% = HIGH PROBABILITY setup, consider larger position size. Conviction 60-80% = MODERATE setup, standard position size. Conviction < 60% = LOW PROBABILITY, small position or avoid. When multiple recommendations AGREE on direction = STRONG CONFLUENCE. Pay attention to the AI rationale - it explains the LOGIC behind each recommendation. Recommendations update based on changing market conditions and new data!""",
    
    "learning_center": """📚 AI Learning Center: This tracks the system's ADAPTIVE LEARNING capabilities and pattern recognition evolution. Displays: Learning Velocity (how fast AI is adapting), Pattern Diversity (variety of market conditions learned), Success Rate Evolution, and Recent Insights discovered by the AI. The system uses machine learning to improve recommendations over time. 💡 TRADING INSIGHT: High Learning Velocity = AI is rapidly adapting to NEW market conditions. High Pattern Diversity = AI has experience with VARIOUS market scenarios. Watch for 'Recent Insights' - these are NEW patterns the AI has discovered that could provide EDGE. When Success Rate is trending UP = AI is getting BETTER at predictions. Use this to gauge confidence in AI recommendations and adjust your reliance on system signals!""",
    
    "performance_tracker": """📈 AI Performance Tracker: This monitors the REAL-TIME performance of AI-generated signals and recommendations using Alpha Intelligence™ data. Tracks: Success Rate (% of profitable signals), Average Confidence (system conviction levels), Total Signals Generated, and Learning Score (improvement rate). Includes performance charts showing success rate evolution over time. 💡 TRADING INSIGHT: Success Rate > 70% = AI is performing WELL, trust the signals. Success Rate < 50% = AI struggling, reduce position sizes or switch to manual analysis. Average Confidence trending UP = AI becoming more CERTAIN in its analysis. Learning Score > 0.8 = AI is RAPIDLY IMPROVING. Use this data to calibrate your TRUST in AI recommendations and adjust position sizing accordingly. When performance metrics are ALL positive = HIGH CONFIDENCE in AI system!""",
    
    "apex_predator": """😈 Apex Predator Brain: This is the ULTIMATE INTELLIGENCE HUB combining Alpha Vantage news sentiment, MCP (Model Context Protocol) servers, and Diabolical Intelligence™. Displays: MCP Systems Status (Knowledge Graph, Sequential Thinking, Memory), Consolidated Intelligence Insights, Alpha Intelligence™ sentiment analysis, and Market Attention metrics. This is where ALL intelligence sources converge. 💡 TRADING INSIGHT: When MCP Systems show 'ACTIVE' status = FULL AI POWER engaged. Diabolical Insights provide CONTRARIAN perspectives that others miss. Alpha Intelligence™ sentiment EXTREME readings (>0.8 or <-0.2) = potential REVERSAL signals. High Market Attention = increased VOLATILITY expected. Use this as your FINAL CHECK before executing trades - it provides the MACRO CONTEXT that pure technical analysis misses. This is your EDGE over other traders!"""
}

def create_info_content_div(info_id: str, info_content: str) -> html.Div:
    """Create the info content div that will be toggled."""
    return html.Div([
        html.P([
            info_content
        ], style={
            "fontSize": "0.8rem",
            "lineHeight": "1.6",
            "color": "#ffffff",
            "margin": "0",
            "padding": "12px 16px",
            "background": "rgba(255, 255, 255, 0.05)",
            "borderRadius": "8px",
            "border": "1px solid rgba(255, 255, 255, 0.1)",
            "fontFamily": "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
        })
    ], id=f"info-content-{info_id}", style={
        "marginTop": "8px",
        "display": "none",
        "transition": "all 0.3s ease-in-out"
    })

# Register callbacks for each module
def register_collapsible_callbacks(app):
    """Register all collapsible callbacks with the Dash app."""
    
    module_ids = [
        "unified_intelligence",
        "regime_analysis", 
        "raw_metrics",
        "recommendations",
        "learning_center",
        "performance_tracker",
        "apex_predator"
    ]
    
    for module_id in module_ids:
        # Create callback for each module
        @app.callback(
            Output(f"info-content-{module_id}", "style"),
            Input(f"title-button-{module_id}", "n_clicks"),
            State(f"info-content-{module_id}", "style"),
            prevent_initial_call=True
        )
        def toggle_info_section(n_clicks, current_style, module_id=module_id):
            """Toggle the visibility of the info section."""
            if n_clicks and n_clicks > 0:
                # Toggle display
                if current_style.get("display") == "none":
                    return {
                        "marginTop": "8px",
                        "display": "block",
                        "transition": "all 0.3s ease-in-out",
                        "opacity": "1",
                        "transform": "translateY(0)"
                    }
                else:
                    return {
                        "marginTop": "8px", 
                        "display": "none",
                        "transition": "all 0.3s ease-in-out"
                    }
            
            # Default hidden state
            return {
                "marginTop": "8px",
                "display": "none", 
                "transition": "all 0.3s ease-in-out"
            }
    
    logger.info(f"✅ Registered {len(module_ids)} collapsible callbacks")
