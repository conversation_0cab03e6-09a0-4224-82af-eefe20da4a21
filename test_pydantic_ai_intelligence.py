#!/usr/bin/env python3
"""
Test script for the Pydantic AI Intelligence Engine
==================================================

This script tests the refactored intelligence.py module to ensure
the Pydantic AI integration works correctly.
"""

import asyncio
import sys
import os
from datetime import datetime
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from dashboard_application.modes.ai_dashboard.intelligence import (
        PydanticAIIntelligenceEngine,
        MarketMetrics,
        AIInsight,
        RegimeAnalysis,
        ConfidenceAssessment,
        generate_unified_ai_insights,
        calculate_ai_confidence,
        _get_intelligence_engine
    )
    from data_models.eots_schemas_v2_5 import (
        FinalAnalysisBundleV2_5,
        ProcessedDataBundleV2_5,
        ProcessedUnderlyingAggregatesV2_5
    )
    print("✅ Successfully imported Pydantic AI Intelligence modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_mock_bundle_data():
    """Create mock bundle data for testing."""
    try:
        # Create mock processed data with required fields
        mock_underlying_data = ProcessedUnderlyingAggregatesV2_5(
            symbol="SPY",  # Required field
            timestamp=datetime.now(),  # Required field
            vapi_fa_z_score_und=2.5,
            dwfd_z_score_und=1.8,
            tw_laf_z_score_und=2.1,
            gib_oi_based_und=150000.0,
            vri_2_0_und_aggregate=7500.0,  # Corrected field name
            current_market_regime_v2_5="REGIME_HIGH_VAPI_FA_BULLISH_MOMENTUM_UNIVERSAL"
        )
        
        mock_processed_bundle = ProcessedDataBundleV2_5(
            underlying_data_enriched=mock_underlying_data,
            processing_timestamp=datetime.now()  # Required field
        )
        
        # Create mock key levels data
        from data_models.eots_schemas_v2_5 import KeyLevelsDataV2_5
        mock_key_levels = KeyLevelsDataV2_5(
            timestamp=datetime.now()
        )

        mock_bundle = FinalAnalysisBundleV2_5(
            target_symbol="SPY",
            processed_data_bundle=mock_processed_bundle,
            key_levels_data_v2_5=mock_key_levels,  # Required field
            bundle_timestamp=datetime.now(),  # Required field
            news_intelligence_v2_5={
                "intelligence_score": 0.75,
                "sentiment_regime": "BULLISH",
                "sentiment_score": 0.3
            },
            atif_recommendations_v2_5=[]
        )
        
        return mock_bundle
    except Exception as e:
        print(f"❌ Error creating mock data: {e}")
        return None

async def test_intelligence_engine():
    """Test the Pydantic AI Intelligence Engine."""
    print("\n🧠 Testing Pydantic AI Intelligence Engine...")
    
    try:
        # Get the intelligence engine
        engine = _get_intelligence_engine()
        print(f"✅ Intelligence engine initialized: AI agents available = {engine.ai_agents_available}")
        
        # Test market metrics creation
        test_metrics = MarketMetrics(
            vapi_fa_z_score=2.5,
            dwfd_z_score=1.8,
            tw_laf_z_score=2.1,
            gib_oi_based=150000.0,
            vri_2_0=7500.0,
            current_regime="REGIME_HIGH_VAPI_FA_BULLISH_MOMENTUM_UNIVERSAL",
            symbol="SPY",
            timestamp=datetime.now()
        )
        print(f"✅ Market metrics created: {test_metrics.symbol} - VAPI-FA: {test_metrics.vapi_fa_z_score}")
        
        return True
        
    except Exception as e:
        print(f"❌ Intelligence engine test failed: {e}")
        return False

async def test_unified_insights():
    """Test the unified AI insights generation."""
    print("\n🎯 Testing Unified AI Insights Generation...")
    
    try:
        mock_bundle = create_mock_bundle_data()
        if not mock_bundle:
            return False
        
        # Test insights generation
        insights = await generate_unified_ai_insights(mock_bundle, "SPY")
        print(f"✅ Generated {len(insights)} insights:")
        for i, insight in enumerate(insights[:3], 1):
            print(f"   {i}. {insight}")
        
        return True
        
    except Exception as e:
        print(f"❌ Unified insights test failed: {e}")
        return False

async def test_confidence_calculation():
    """Test the AI confidence calculation."""
    print("\n📊 Testing AI Confidence Calculation...")
    
    try:
        mock_bundle = create_mock_bundle_data()
        if not mock_bundle:
            return False
        
        # Test confidence calculation
        confidence = await calculate_ai_confidence(mock_bundle)
        print(f"✅ AI confidence calculated: {confidence:.2%}")
        
        if 0.0 <= confidence <= 1.0:
            print("✅ Confidence value is within valid range")
            return True
        else:
            print(f"❌ Confidence value {confidence} is outside valid range [0.0, 1.0]")
            return False
        
    except Exception as e:
        print(f"❌ Confidence calculation test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 PYDANTIC AI INTELLIGENCE ENGINE TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Intelligence Engine Initialization", test_intelligence_engine),
        ("Unified AI Insights Generation", test_unified_insights),
        ("AI Confidence Calculation", test_confidence_calculation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   Result: {status}")
        except Exception as e:
            print(f"   Result: ❌ ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Pydantic AI Intelligence Engine is ready!")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
