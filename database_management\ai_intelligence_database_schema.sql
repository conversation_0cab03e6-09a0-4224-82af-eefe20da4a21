-- =====================================================
-- AI INTELLIGENCE HEADQUARTERS - SUPABASE DATABASE SCHEMA
-- =====================================================
-- 
-- This schema creates a comprehensive database for AI intelligence
-- agents to live, learn, grow, multiply, and evolve their capabilities.
--
-- Author: EOTS v2.5 AI Intelligence Division
-- Version: 1.0.0 - "SENTIENT MARKET DOMINATION"
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- CORE AI AGENT MANAGEMENT
-- =====================================================

-- AI Agents Registry - The living directory of all AI agents
CREATE TABLE ai_agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_name VARCHAR(100) NOT NULL UNIQUE,
    agent_type VARCHAR(50) NOT NULL, -- 'market_analyst', 'regime_analyst', 'confidence_calculator', etc.
    agent_version VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    parent_agent_id UUID REFERENCES ai_agents(id), -- For agent multiplication/spawning
    specialization TEXT, -- What this agent specializes in
    capabilities JSONB NOT NULL DEFAULT '{}', -- Agent's current capabilities
    configuration JSONB NOT NULL DEFAULT '{}', -- Agent configuration settings
    performance_score DECIMAL(5,4) DEFAULT 0.5000, -- Overall performance (0-1)
    learning_rate DECIMAL(5,4) DEFAULT 0.1000, -- How fast this agent learns
    adaptation_score DECIMAL(5,4) DEFAULT 0.5000, -- How well it adapts
    status VARCHAR(20) DEFAULT 'ACTIVE', -- 'ACTIVE', 'LEARNING', 'EVOLVING', 'DORMANT'
    birth_timestamp TIMESTAMPTZ DEFAULT NOW(),
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    total_analyses INTEGER DEFAULT 0,
    successful_predictions INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Agent Evolution History - Track how agents evolve over time
CREATE TABLE ai_agent_evolution (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    evolution_type VARCHAR(50) NOT NULL, -- 'capability_upgrade', 'specialization_change', 'performance_boost'
    previous_state JSONB NOT NULL,
    new_state JSONB NOT NULL,
    evolution_trigger VARCHAR(100), -- What caused this evolution
    performance_improvement DECIMAL(5,4), -- How much performance improved
    evolution_timestamp TIMESTAMPTZ DEFAULT NOW(),
    success_validation BOOLEAN, -- Was this evolution successful?
    rollback_data JSONB -- Data needed to rollback if evolution fails
);

-- =====================================================
-- AI LEARNING AND MEMORY SYSTEM
-- =====================================================

-- AI Learning Sessions - Every learning interaction
CREATE TABLE ai_learning_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    session_type VARCHAR(50) NOT NULL, -- 'insight_generation', 'confidence_assessment', 'regime_analysis'
    market_context JSONB NOT NULL, -- Market conditions during learning
    input_data JSONB NOT NULL, -- What data was provided to the agent
    output_data JSONB NOT NULL, -- What the agent produced
    confidence_score DECIMAL(5,4) NOT NULL,
    actual_outcome JSONB, -- What actually happened (for validation)
    accuracy_score DECIMAL(5,4), -- How accurate was the prediction
    learning_extracted JSONB, -- What the agent learned from this session
    session_timestamp TIMESTAMPTZ DEFAULT NOW(),
    validation_timestamp TIMESTAMPTZ, -- When outcome was validated
    feedback_incorporated BOOLEAN DEFAULT FALSE
);

-- AI Memory Bank - Long-term memory for pattern recognition
CREATE TABLE ai_memory_bank (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    memory_type VARCHAR(50) NOT NULL, -- 'pattern', 'strategy', 'market_condition', 'failure_case'
    memory_category VARCHAR(50), -- 'bullish_patterns', 'volatility_expansion', 'regime_transitions'
    pattern_signature TEXT NOT NULL, -- Unique identifier for this pattern
    pattern_data JSONB NOT NULL, -- The actual pattern data
    success_rate DECIMAL(5,4) NOT NULL DEFAULT 0.5000,
    confidence_level DECIMAL(5,4) NOT NULL DEFAULT 0.5000,
    usage_count INTEGER DEFAULT 0,
    last_successful_use TIMESTAMPTZ,
    last_failed_use TIMESTAMPTZ,
    memory_strength DECIMAL(5,4) DEFAULT 1.0000, -- How strong this memory is (decay over time)
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Insights History - Every insight generated by AI agents
CREATE TABLE ai_insights_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    session_id UUID REFERENCES ai_learning_sessions(id),
    symbol VARCHAR(10) NOT NULL,
    insight_text TEXT NOT NULL,
    insight_type VARCHAR(50) NOT NULL, -- 'market_analysis', 'regime_prediction', 'risk_assessment'
    confidence_score DECIMAL(5,4) NOT NULL,
    reasoning TEXT,
    supporting_metrics JSONB,
    risk_level VARCHAR(20), -- 'LOW', 'MODERATE', 'HIGH', 'EXTREME'
    actionability_score DECIMAL(5,4),
    market_regime VARCHAR(100),
    insight_timestamp TIMESTAMPTZ DEFAULT NOW(),
    outcome_validation JSONB, -- How well this insight performed
    validation_timestamp TIMESTAMPTZ,
    user_feedback INTEGER, -- User rating of insight quality (1-5)
    ai_self_assessment DECIMAL(5,4) -- Agent's own assessment of insight quality
);

-- =====================================================
-- ADAPTIVE THRESHOLDS AND PARAMETERS
-- =====================================================

-- AI Adaptive Thresholds - Dynamic thresholds that evolve
CREATE TABLE ai_adaptive_thresholds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    threshold_name VARCHAR(100) NOT NULL,
    threshold_category VARCHAR(50), -- 'signal_strength', 'confidence', 'risk_management'
    current_value DECIMAL(10,6) NOT NULL,
    default_value DECIMAL(10,6) NOT NULL,
    min_allowed_value DECIMAL(10,6),
    max_allowed_value DECIMAL(10,6),
    adaptation_rate DECIMAL(5,4) DEFAULT 0.05, -- How fast this threshold adapts
    performance_correlation DECIMAL(5,4), -- How this threshold correlates with performance
    last_adjustment TIMESTAMPTZ,
    adjustment_reason TEXT,
    market_conditions_context JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(agent_id, threshold_name)
);

-- AI Threshold History - Track how thresholds change over time
CREATE TABLE ai_threshold_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    threshold_id UUID NOT NULL REFERENCES ai_adaptive_thresholds(id) ON DELETE CASCADE,
    old_value DECIMAL(10,6) NOT NULL,
    new_value DECIMAL(10,6) NOT NULL,
    adjustment_magnitude DECIMAL(10,6) NOT NULL,
    adjustment_reason VARCHAR(200),
    performance_before DECIMAL(5,4),
    performance_after DECIMAL(5,4),
    market_context JSONB,
    adjustment_timestamp TIMESTAMPTZ DEFAULT NOW(),
    validation_period_days INTEGER DEFAULT 7,
    adjustment_success BOOLEAN -- Was this adjustment beneficial?
);

-- =====================================================
-- PERFORMANCE TRACKING AND ANALYTICS
-- =====================================================

-- AI Performance Metrics - Comprehensive performance tracking
CREATE TABLE ai_performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    symbol VARCHAR(10),
    total_predictions INTEGER DEFAULT 0,
    correct_predictions INTEGER DEFAULT 0,
    incorrect_predictions INTEGER DEFAULT 0,
    pending_predictions INTEGER DEFAULT 0,
    success_rate DECIMAL(5,4) DEFAULT 0.0000,
    average_confidence DECIMAL(5,4) DEFAULT 0.0000,
    confidence_calibration DECIMAL(5,4) DEFAULT 0.0000, -- How well-calibrated confidence is
    learning_velocity DECIMAL(5,4) DEFAULT 0.0000, -- How fast the agent is improving
    adaptation_effectiveness DECIMAL(5,4) DEFAULT 0.0000,
    insights_generated INTEGER DEFAULT 0,
    insights_validated INTEGER DEFAULT 0,
    user_satisfaction_score DECIMAL(5,4), -- Average user feedback
    computational_efficiency DECIMAL(5,4), -- How efficiently the agent operates
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(agent_id, metric_date, symbol)
);

-- AI Learning Curves - Track learning progress over time
CREATE TABLE ai_learning_curves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    learning_period_start TIMESTAMPTZ NOT NULL,
    learning_period_end TIMESTAMPTZ NOT NULL,
    initial_performance DECIMAL(5,4) NOT NULL,
    final_performance DECIMAL(5,4) NOT NULL,
    performance_improvement DECIMAL(5,4) NOT NULL,
    learning_rate_achieved DECIMAL(5,4) NOT NULL,
    key_learnings JSONB, -- What the agent learned during this period
    breakthrough_moments JSONB, -- Significant learning breakthroughs
    plateau_periods JSONB, -- When learning stagnated
    learning_efficiency DECIMAL(5,4), -- How efficiently learning occurred
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- AI COLLABORATION AND ENSEMBLE SYSTEMS
-- =====================================================

-- AI Agent Collaborations - Track when agents work together
CREATE TABLE ai_agent_collaborations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    primary_agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    collaborating_agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    collaboration_type VARCHAR(50) NOT NULL, -- 'ensemble_analysis', 'cross_validation', 'knowledge_sharing'
    collaboration_context JSONB NOT NULL,
    primary_output JSONB NOT NULL,
    collaborating_output JSONB NOT NULL,
    ensemble_result JSONB NOT NULL,
    collaboration_success BOOLEAN,
    synergy_score DECIMAL(5,4), -- How well agents worked together
    collaboration_timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- AI Knowledge Sharing - When agents share learnings
CREATE TABLE ai_knowledge_sharing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    target_agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    knowledge_type VARCHAR(50) NOT NULL, -- 'pattern', 'threshold', 'strategy', 'failure_case'
    knowledge_data JSONB NOT NULL,
    transfer_success BOOLEAN,
    adaptation_required JSONB, -- How knowledge needed to be adapted
    performance_impact DECIMAL(5,4), -- Impact on target agent performance
    sharing_timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- AI SYSTEM HEALTH AND MONITORING
-- =====================================================

-- AI System Health - Overall system health metrics
CREATE TABLE ai_system_health (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    health_timestamp TIMESTAMPTZ DEFAULT NOW(),
    total_active_agents INTEGER NOT NULL,
    total_learning_sessions INTEGER NOT NULL,
    average_system_performance DECIMAL(5,4) NOT NULL,
    memory_usage_mb INTEGER,
    processing_latency_ms INTEGER,
    error_rate DECIMAL(5,4),
    learning_velocity DECIMAL(5,4), -- How fast the system is learning overall
    adaptation_rate DECIMAL(5,4), -- How fast the system is adapting
    system_intelligence_score DECIMAL(5,4), -- Overall intelligence level
    emergent_behaviors JSONB, -- Any emergent behaviors detected
    system_status VARCHAR(20) DEFAULT 'HEALTHY' -- 'HEALTHY', 'LEARNING', 'ADAPTING', 'EVOLVING'
);

-- AI Error Tracking - Track and learn from errors
CREATE TABLE ai_error_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES ai_agents(id) ON DELETE CASCADE,
    error_type VARCHAR(50) NOT NULL,
    error_category VARCHAR(50), -- 'prediction_error', 'confidence_miscalibration', 'system_error'
    error_context JSONB NOT NULL,
    error_message TEXT,
    market_conditions JSONB,
    recovery_action JSONB, -- What was done to recover
    learning_extracted JSONB, -- What was learned from this error
    error_timestamp TIMESTAMPTZ DEFAULT NOW(),
    resolution_timestamp TIMESTAMPTZ,
    prevention_measures JSONB -- Measures taken to prevent similar errors
);

-- =====================================================
-- AI MARKET PATTERN RECOGNITION
-- =====================================================

-- AI Market Patterns - Discovered market patterns
CREATE TABLE ai_market_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    discoverer_agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    pattern_name VARCHAR(100) NOT NULL,
    pattern_type VARCHAR(50) NOT NULL, -- 'flow_pattern', 'regime_transition', 'volatility_pattern'
    pattern_signature JSONB NOT NULL, -- Mathematical signature of the pattern
    pattern_conditions JSONB NOT NULL, -- Conditions when this pattern occurs
    success_probability DECIMAL(5,4) NOT NULL,
    confidence_level DECIMAL(5,4) NOT NULL,
    discovery_timestamp TIMESTAMPTZ DEFAULT NOW(),
    last_occurrence TIMESTAMPTZ,
    occurrence_count INTEGER DEFAULT 0,
    validation_count INTEGER DEFAULT 0,
    false_positive_count INTEGER DEFAULT 0,
    pattern_strength DECIMAL(5,4) DEFAULT 1.0000, -- How strong/reliable this pattern is
    market_regime_context VARCHAR(100),
    timeframe_applicability JSONB, -- What timeframes this pattern works on
    symbol_applicability JSONB -- What symbols this pattern works on
);

-- AI Pattern Validations - Track pattern performance
CREATE TABLE ai_pattern_validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pattern_id UUID NOT NULL REFERENCES ai_market_patterns(id) ON DELETE CASCADE,
    validating_agent_id UUID NOT NULL REFERENCES ai_agents(id) ON DELETE CASCADE,
    validation_context JSONB NOT NULL,
    predicted_outcome JSONB NOT NULL,
    actual_outcome JSONB,
    validation_success BOOLEAN,
    accuracy_score DECIMAL(5,4),
    validation_timestamp TIMESTAMPTZ DEFAULT NOW(),
    outcome_timestamp TIMESTAMPTZ,
    learning_feedback JSONB -- What was learned from this validation
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Core performance indexes
CREATE INDEX idx_ai_agents_type_status ON ai_agents(agent_type, status);
CREATE INDEX idx_ai_agents_performance ON ai_agents(performance_score DESC);
CREATE INDEX idx_ai_learning_sessions_agent_timestamp ON ai_learning_sessions(agent_id, session_timestamp DESC);
CREATE INDEX idx_ai_memory_bank_agent_type ON ai_memory_bank(agent_id, memory_type);
CREATE INDEX idx_ai_insights_agent_symbol_timestamp ON ai_insights_history(agent_id, symbol, insight_timestamp DESC);
CREATE INDEX idx_ai_performance_agent_date ON ai_performance_metrics(agent_id, metric_date DESC);
CREATE INDEX idx_ai_thresholds_agent_name ON ai_adaptive_thresholds(agent_id, threshold_name);
CREATE INDEX idx_ai_patterns_type_strength ON ai_market_patterns(pattern_type, pattern_strength DESC);

-- Composite indexes for complex queries
CREATE INDEX idx_ai_learning_performance ON ai_learning_sessions(agent_id, accuracy_score DESC, session_timestamp DESC);
CREATE INDEX idx_ai_memory_usage_success ON ai_memory_bank(agent_id, usage_count DESC, success_rate DESC);
CREATE INDEX idx_ai_system_health_timestamp ON ai_system_health(health_timestamp DESC);

-- =====================================================
-- VIEWS FOR ANALYTICS
-- =====================================================

-- AI Agent Performance Summary
CREATE VIEW ai_agent_performance_summary AS
SELECT
    a.id,
    a.agent_name,
    a.agent_type,
    a.performance_score,
    a.total_analyses,
    a.successful_predictions,
    CASE
        WHEN a.total_analyses > 0 THEN (a.successful_predictions::DECIMAL / a.total_analyses)
        ELSE 0
    END as success_rate,
    COUNT(ls.id) as learning_sessions_count,
    AVG(ls.accuracy_score) as avg_accuracy,
    MAX(ls.session_timestamp) as last_learning_session
FROM ai_agents a
LEFT JOIN ai_learning_sessions ls ON a.id = ls.agent_id
WHERE a.status = 'ACTIVE'
GROUP BY a.id, a.agent_name, a.agent_type, a.performance_score, a.total_analyses, a.successful_predictions;

-- AI System Intelligence Overview
CREATE VIEW ai_system_intelligence_overview AS
SELECT
    COUNT(*) as total_agents,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_agents,
    COUNT(CASE WHEN status = 'LEARNING' THEN 1 END) as learning_agents,
    COUNT(CASE WHEN status = 'EVOLVING' THEN 1 END) as evolving_agents,
    AVG(performance_score) as avg_performance,
    AVG(adaptation_score) as avg_adaptation,
    SUM(total_analyses) as total_system_analyses,
    SUM(successful_predictions) as total_successful_predictions
FROM ai_agents;

-- =====================================================
-- TRIGGERS FOR AUTOMATION
-- =====================================================

-- Update agent last_activity on learning sessions
CREATE OR REPLACE FUNCTION update_agent_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE ai_agents
    SET last_activity = NOW(),
        total_analyses = total_analyses + 1,
        updated_at = NOW()
    WHERE id = NEW.agent_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_agent_activity
    AFTER INSERT ON ai_learning_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_activity();

-- Update successful predictions on validation
CREATE OR REPLACE FUNCTION update_successful_predictions()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.accuracy_score IS NOT NULL AND NEW.accuracy_score > 0.7 THEN
        UPDATE ai_agents
        SET successful_predictions = successful_predictions + 1,
            updated_at = NOW()
        WHERE id = NEW.agent_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_successful_predictions
    AFTER UPDATE ON ai_learning_sessions
    FOR EACH ROW
    WHEN (OLD.accuracy_score IS NULL AND NEW.accuracy_score IS NOT NULL)
    EXECUTE FUNCTION update_successful_predictions();

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert initial AI agents
INSERT INTO ai_agents (agent_name, agent_type, specialization, capabilities, configuration) VALUES
('MarketAnalystAgent', 'market_analyst', 'Institutional flow patterns and gamma positioning effects',
 '{"pattern_recognition": true, "flow_analysis": true, "institutional_behavior": true}',
 '{"confidence_threshold": 0.7, "max_retries": 2, "learning_enabled": true}'),

('RegimeAnalystAgent', 'regime_analyst', 'Market regime detection and transition prediction',
 '{"regime_detection": true, "transition_prediction": true, "regime_validation": true}',
 '{"confidence_threshold": 0.75, "transition_sensitivity": 0.8, "regime_validation_enabled": true}'),

('ConfidenceCalculatorAgent', 'confidence_calculator', 'Confidence calibration and ensemble validation',
 '{"confidence_calibration": true, "self_validation": true, "ensemble_methods": true}',
 '{"confidence_threshold": 0.8, "self_validation_enabled": true, "historical_accuracy_weight": 0.3}');

-- Insert initial adaptive thresholds
INSERT INTO ai_adaptive_thresholds (agent_id, threshold_name, threshold_category, current_value, default_value, min_allowed_value, max_allowed_value)
SELECT
    a.id,
    threshold_name,
    threshold_category,
    current_value,
    default_value,
    min_allowed_value,
    max_allowed_value
FROM ai_agents a
CROSS JOIN (VALUES
    ('vapi_fa_strong', 'signal_strength', 2.0, 2.0, 1.0, 5.0),
    ('vapi_fa_moderate', 'signal_strength', 1.5, 1.5, 0.5, 3.0),
    ('dwfd_strong', 'signal_strength', 1.5, 1.5, 0.5, 3.0),
    ('confidence_threshold', 'confidence', 0.6, 0.6, 0.3, 0.9),
    ('confluence_threshold', 'signal_strength', 0.7, 0.7, 0.4, 0.9)
) AS thresholds(threshold_name, threshold_category, current_value, default_value, min_allowed_value, max_allowed_value)
WHERE a.agent_type IN ('market_analyst', 'regime_analyst', 'confidence_calculator');

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE ai_agents IS 'Registry of all AI agents in the system with their capabilities and performance metrics';
COMMENT ON TABLE ai_learning_sessions IS 'Every learning interaction and session for performance tracking and improvement';
COMMENT ON TABLE ai_memory_bank IS 'Long-term memory storage for pattern recognition and strategic learning';
COMMENT ON TABLE ai_adaptive_thresholds IS 'Dynamic thresholds that evolve based on performance and market conditions';
COMMENT ON TABLE ai_performance_metrics IS 'Comprehensive performance tracking for all AI agents';
COMMENT ON TABLE ai_market_patterns IS 'Discovered market patterns with validation and success tracking';

-- Grant permissions (adjust as needed for your Supabase setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;
