import asyncio
import logging
from datetime import datetime, timedelta
import os
import sys
from typing import List, Dict, Any

from utils.config_manager_v2_5 import ConfigManagerV2_5
from data_management.tradier_data_fetcher_v2_5 import TradierDataFetcherV2_5
import httpx

SUPABASE_REST_URL = os.environ.get("SUPABASE_REST_URL")
SUPABASE_ANON_KEY = os.environ.get("SUPABASE_ANON_KEY")
HEADERS = {"apikey": SUPABASE_ANON_KEY, "Authorization": f"Bearer {SUPABASE_ANON_KEY}", "Content-Type": "application/json"}

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger("backfill_ohlcv")

def get_past_n_dates_str(n: int) -> List[str]:
    # Returns a list of date strings for the last n days, most recent first (excluding today)
    today = datetime.now().date()
    return [(today - timedelta(days=offset)).strftime("%Y-%m-%d") for offset in range(1, n+1)]

async def record_exists(client: httpx.AsyncClient, table: str, symbol: str, date_str: str) -> bool:
    url = f"{SUPABASE_REST_URL}/{table}?symbol=eq.{symbol}&date=eq.{date_str}&select=symbol"
    resp = await client.get(url, headers=HEADERS)
    if resp.status_code == 200:
        data = resp.json()
        return len(data) > 0
    return False

async def fetch_and_insert_ohlcv(symbol: str, date_str: str, tradier: TradierDataFetcherV2_5, client: httpx.AsyncClient) -> Dict[str, Any]:
    result = {"symbol": symbol, "success": False, "errors": [], "ohlcv": None}
    try:
        # Fetch OHLCV data from Tradier (get last 5 days, then pick yesterday)
        ohlcv_result = await tradier.fetch_historical_data(symbol, days=5)
        if not ohlcv_result or "data" not in ohlcv_result:
            result["errors"].append("No OHLCV data from Tradier")
            return result
        # Find the entry for the target date
        ohlcv = next((row for row in ohlcv_result["data"] if row["date"] == date_str), None)
        if not ohlcv:
            result["errors"].append(f"No OHLCV for {date_str}")
            return result
        # Check if already exists
        exists = await record_exists(client, "daily_ohlcv", symbol, date_str)
        if exists:
            result["errors"].append("Already exists in daily_ohlcv")
            return result
        # Prepare payload
        payload = {
            "symbol": symbol,
            "date": date_str,
            "open": ohlcv["open"],
            "high": ohlcv["high"],
            "low": ohlcv["low"],
            "close": ohlcv["close"],
            "volume": ohlcv["volume"]
        }
        # Insert into Supabase
        url = f"{SUPABASE_REST_URL}/daily_ohlcv"
        resp = await client.post(url, headers=HEADERS, json=payload)
        if resp.status_code in (200, 201):
            result["success"] = True
            result["ohlcv"] = payload
        else:
            result["errors"].append(f"Supabase insert failed: {resp.status_code} {resp.text}")
    except Exception as e:
        result["errors"].append(str(e))
    return result

async def main():
    config = ConfigManagerV2_5()
    tickers = config.config.intraday_collector_settings.watched_tickers
    date_strs = get_past_n_dates_str(60)  # Last 60 days, yesterday first
    async with TradierDataFetcherV2_5(config) as tradier:
        async with httpx.AsyncClient(timeout=30) as client:
            for symbol in tickers:
                logger.info(f"Backfilling {symbol} for last 60 days...")
                # Fetch all available OHLCV for the last 65 days to ensure coverage
                ohlcv_result = await tradier.fetch_historical_data(symbol, days=65)
                if not ohlcv_result or "data" not in ohlcv_result:
                    logger.warning(f"No OHLCV data from Tradier for {symbol}")
                    continue
                ohlcv_data = {row["date"]: row for row in ohlcv_result["data"]}
                for date_str in date_strs:
                    if date_str not in ohlcv_data:
                        logger.warning(f"No OHLCV for {symbol} on {date_str}")
                        continue
                    exists = await record_exists(client, "daily_ohlcv", symbol, date_str)
                    if exists:
                        logger.info(f"Already exists: {symbol} {date_str}")
                        continue
                    ohlcv = ohlcv_data[date_str]
                    payload = {
                        "symbol": symbol,
                        "date": date_str,
                        "open": ohlcv["open"],
                        "high": ohlcv["high"],
                        "low": ohlcv["low"],
                        "close": ohlcv["close"],
                        "volume": ohlcv["volume"]
                    }
                    url = f"{SUPABASE_REST_URL}/daily_ohlcv"
                    resp = await client.post(url, headers=HEADERS, json=payload)
                    if resp.status_code in (200, 201):
                        logger.info(f"Inserted: {symbol} {date_str} | {payload}")
                    else:
                        logger.warning(f"Supabase insert failed for {symbol} {date_str}: {resp.status_code} {resp.text}")

if __name__ == "__main__":
    if not SUPABASE_REST_URL or not SUPABASE_ANON_KEY:
        logger.error("SUPABASE_REST_URL and SUPABASE_ANON_KEY must be set in the environment.")
        sys.exit(1)
    asyncio.run(main()) 