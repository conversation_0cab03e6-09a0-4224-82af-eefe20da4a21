"""
Adaptive Threshold Management System for EOTS v2.5
==================================================

This module implements the most sophisticated adaptive threshold management system
possible, automatically optimizing all AI system thresholds based on performance
data, market conditions, and historical effectiveness.

Features:
- AI-Managed Adaptive Thresholds with Real-Time Optimization
- Performance-Based Threshold Evolution with Machine Learning
- Market Condition-Aware Threshold Adjustment
- Multi-Dimensional Threshold Optimization with Genetic Algorithms
- Historical Performance Analysis for Threshold Calibration
- Cross-System Threshold Synchronization and Coordination
- Automated Threshold Testing and Validation
- Database-Driven Threshold Persistence with Supabase Integration

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "INTELLIGENT THRESHOLD EVOLUTION"
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import statistics
import numpy as np
from collections import defaultdict, deque
import json

# Pydantic imports
from pydantic import BaseModel, Field

# Import EOTS schemas
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5

# Import performance tracking system
try:
    from .performance_tracking_system import get_performance_tracker, PerformanceMetricType
    PERFORMANCE_TRACKING_AVAILABLE = True
except ImportError:
    PERFORMANCE_TRACKING_AVAILABLE = False

# Import unified AI ecosystem
try:
    from .unified_ai_ecosystem import get_unified_ai_ecosystem
    ECOSYSTEM_AVAILABLE = True
except ImportError:
    ECOSYSTEM_AVAILABLE = False

# Import self-learning engine
try:
    from .self_learning_engine import get_self_learning_engine
    SELF_LEARNING_AVAILABLE = True
except ImportError:
    SELF_LEARNING_AVAILABLE = False

# Import database integration
try:
    from database_management.ai_intelligence_integration import get_ai_database_integration
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

logger = logging.getLogger(__name__)

# ===== PYDANTIC MODELS FOR ADAPTIVE THRESHOLDS =====

class ThresholdType(str, Enum):
    """Types of adaptive thresholds."""
    SIGNAL_STRENGTH = "signal_strength"
    CONFIDENCE_LEVEL = "confidence_level"
    ACCURACY_THRESHOLD = "accuracy_threshold"
    VOLATILITY_THRESHOLD = "volatility_threshold"
    VOLUME_THRESHOLD = "volume_threshold"
    REGIME_SENSITIVITY = "regime_sensitivity"
    LEARNING_RATE = "learning_rate"
    ADAPTATION_RATE = "adaptation_rate"
    BREEDING_POTENTIAL = "breeding_potential"
    ALERT_SENSITIVITY = "alert_sensitivity"

class MarketCondition(str, Enum):
    """Market condition types for threshold adaptation."""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS_MARKET = "sideways_market"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    HIGH_VOLUME = "high_volume"
    LOW_VOLUME = "low_volume"
    TRENDING = "trending"
    CONSOLIDATING = "consolidating"

class AdaptiveThreshold(BaseModel):
    """Comprehensive adaptive threshold model."""
    threshold_id: str = Field(..., description="Unique threshold identifier")
    threshold_type: ThresholdType = Field(..., description="Type of threshold")
    current_value: float = Field(..., description="Current threshold value")
    default_value: float = Field(..., description="Original default value")
    min_value: float = Field(..., description="Minimum allowed value")
    max_value: float = Field(..., description="Maximum allowed value")
    
    # Adaptation parameters
    adaptation_rate: float = Field(default=0.05, ge=0.0, le=1.0, description="Rate of adaptation")
    momentum: float = Field(default=0.9, ge=0.0, le=1.0, description="Momentum factor")
    volatility_sensitivity: float = Field(default=0.5, ge=0.0, le=1.0, description="Sensitivity to volatility")
    
    # Performance tracking
    performance_correlation: float = Field(default=0.0, ge=-1.0, le=1.0, description="Correlation with performance")
    effectiveness_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Overall effectiveness")
    usage_frequency: int = Field(default=0, ge=0, description="How often threshold is used")
    
    # Market condition adaptation
    market_condition_modifiers: Dict[MarketCondition, float] = Field(default_factory=dict)
    condition_sensitivity: float = Field(default=0.3, ge=0.0, le=1.0, description="Sensitivity to market conditions")
    
    # Historical tracking
    adjustment_history: List[Dict[str, Any]] = Field(default_factory=list)
    performance_history: List[float] = Field(default_factory=list)
    last_adjustment: Optional[datetime] = None
    last_performance_update: Optional[datetime] = None
    
    # Optimization parameters
    optimization_target: str = Field(default="accuracy", description="What to optimize for")
    learning_window: int = Field(default=100, ge=1, description="Number of samples for learning")
    stability_factor: float = Field(default=0.8, ge=0.0, le=1.0, description="Stability vs. adaptability")

class ThresholdOptimizationResult(BaseModel):
    """Result of threshold optimization."""
    threshold_id: str = Field(..., description="Threshold that was optimized")
    old_value: float = Field(..., description="Previous threshold value")
    new_value: float = Field(..., description="New optimized threshold value")
    improvement_score: float = Field(..., ge=0.0, le=1.0, description="Expected improvement")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in optimization")
    optimization_method: str = Field(..., description="Method used for optimization")
    market_context: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)

class ThresholdManagerConfig(BaseModel):
    """Configuration for the adaptive threshold manager."""
    enable_real_time_adaptation: bool = Field(default=True)
    enable_market_condition_adaptation: bool = Field(default=True)
    enable_performance_based_optimization: bool = Field(default=True)
    enable_genetic_algorithm_optimization: bool = Field(default=True)
    enable_cross_system_synchronization: bool = Field(default=True)
    
    optimization_interval_minutes: int = Field(default=60, ge=1)
    performance_analysis_window_hours: int = Field(default=24, ge=1)
    market_condition_update_interval_minutes: int = Field(default=15, ge=1)
    
    min_samples_for_optimization: int = Field(default=10, ge=1)
    max_adjustment_per_cycle: float = Field(default=0.1, ge=0.0, le=1.0)
    stability_threshold: float = Field(default=0.05, ge=0.0, le=1.0)

# ===== ADAPTIVE THRESHOLD MANAGER =====

class AdaptiveThresholdManager:
    """
    ADAPTIVE THRESHOLD MANAGER
    
    The most sophisticated threshold management system possible, automatically
    optimizing all AI system thresholds based on performance data, market
    conditions, and historical effectiveness using advanced machine learning.
    """
    
    def __init__(self, config: Optional[ThresholdManagerConfig] = None, db_integration=None):
        self.config = config or ThresholdManagerConfig()
        self.db_integration = db_integration
        self.logger = logger.getChild(self.__class__.__name__)
        
        # Threshold storage
        self.adaptive_thresholds: Dict[str, AdaptiveThreshold] = {}
        self.threshold_groups: Dict[str, List[str]] = {}  # Grouped thresholds for coordination
        
        # Performance tracking integration
        self.performance_tracker = None
        self.ecosystem = None
        self.self_learning_engine = None
        
        # Market condition tracking
        self.current_market_conditions: Dict[MarketCondition, float] = {}
        self.market_condition_history: deque = deque(maxlen=1000)
        
        # Optimization tracking
        self.optimization_history: List[ThresholdOptimizationResult] = []
        self.last_optimization: Optional[datetime] = None
        self.optimization_in_progress = False
        
        # Performance correlation tracking
        self.threshold_performance_correlations: Dict[str, List[Tuple[float, float]]] = defaultdict(list)
        
        self.logger.info("⚖️ Adaptive Threshold Manager initialized with intelligent optimization")
    
    async def initialize_manager(self) -> bool:
        """Initialize the adaptive threshold manager with all integrations."""
        try:
            self.logger.info("🚀 Initializing Adaptive Threshold Manager...")
            
            # Initialize performance tracker integration
            if PERFORMANCE_TRACKING_AVAILABLE:
                self.performance_tracker = await get_performance_tracker()
                self.logger.info("📊 Performance tracker integration established")
            
            # Initialize ecosystem integration
            if ECOSYSTEM_AVAILABLE:
                self.ecosystem = await get_unified_ai_ecosystem()
                self.logger.info("🧠 AI ecosystem integration established")
            
            # Initialize self-learning engine integration
            if SELF_LEARNING_AVAILABLE:
                self.self_learning_engine = await get_self_learning_engine()
                self.logger.info("🧠 Self-learning engine integration established")
            
            # Initialize default thresholds
            await self._initialize_default_thresholds()
            
            # Load existing thresholds from database
            if DATABASE_AVAILABLE and self.db_integration:
                await self._load_thresholds_from_database()
            
            # Initialize market condition tracking
            await self._initialize_market_condition_tracking()
            
            # Start optimization scheduler if enabled
            if self.config.enable_real_time_adaptation:
                asyncio.create_task(self._optimization_scheduler())
            
            self.logger.info("✅ Adaptive Threshold Manager initialization complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Adaptive Threshold Manager: {e}")
            return False
    
    async def _initialize_default_thresholds(self):
        """Initialize default adaptive thresholds for all AI systems."""
        try:
            # Define comprehensive threshold configurations
            threshold_configs = [
                # Signal strength thresholds
                ("vapi_fa_strong", ThresholdType.SIGNAL_STRENGTH, 2.0, 1.0, 5.0, "accuracy"),
                ("vapi_fa_moderate", ThresholdType.SIGNAL_STRENGTH, 1.5, 0.5, 3.0, "accuracy"),
                ("dwfd_strong", ThresholdType.SIGNAL_STRENGTH, 1.5, 0.5, 3.0, "accuracy"),
                ("dwfd_moderate", ThresholdType.SIGNAL_STRENGTH, 1.0, 0.3, 2.5, "accuracy"),
                ("tw_laf_strong", ThresholdType.SIGNAL_STRENGTH, 2.0, 1.0, 4.0, "accuracy"),
                ("tw_laf_moderate", ThresholdType.SIGNAL_STRENGTH, 1.0, 0.5, 2.5, "accuracy"),
                
                # Confidence thresholds
                ("confidence_threshold", ThresholdType.CONFIDENCE_LEVEL, 0.6, 0.3, 0.9, "calibration"),
                ("high_confidence_threshold", ThresholdType.CONFIDENCE_LEVEL, 0.8, 0.6, 0.95, "calibration"),
                ("low_confidence_threshold", ThresholdType.CONFIDENCE_LEVEL, 0.4, 0.1, 0.6, "calibration"),
                
                # Accuracy thresholds
                ("accuracy_alert_threshold", ThresholdType.ACCURACY_THRESHOLD, 0.7, 0.5, 0.9, "performance"),
                ("accuracy_optimization_threshold", ThresholdType.ACCURACY_THRESHOLD, 0.8, 0.6, 0.95, "performance"),
                
                # Volatility thresholds
                ("volatility_high_threshold", ThresholdType.VOLATILITY_THRESHOLD, 0.3, 0.1, 0.8, "regime_detection"),
                ("volatility_low_threshold", ThresholdType.VOLATILITY_THRESHOLD, 0.1, 0.01, 0.3, "regime_detection"),
                
                # Volume thresholds
                ("volume_surge_threshold", ThresholdType.VOLUME_THRESHOLD, 2.0, 1.2, 5.0, "signal_confirmation"),
                ("volume_low_threshold", ThresholdType.VOLUME_THRESHOLD, 0.5, 0.1, 1.0, "signal_confirmation"),
                
                # Regime sensitivity
                ("regime_transition_sensitivity", ThresholdType.REGIME_SENSITIVITY, 0.5, 0.2, 0.8, "regime_detection"),
                ("regime_stability_threshold", ThresholdType.REGIME_SENSITIVITY, 0.7, 0.4, 0.9, "regime_detection"),
                
                # Learning parameters
                ("learning_rate_adaptive", ThresholdType.LEARNING_RATE, 0.1, 0.01, 0.3, "learning_efficiency"),
                ("adaptation_rate_threshold", ThresholdType.ADAPTATION_RATE, 0.05, 0.01, 0.2, "adaptation_speed"),
                
                # Breeding thresholds
                ("breeding_potential_threshold", ThresholdType.BREEDING_POTENTIAL, 0.7, 0.5, 0.95, "breeding_success"),
                ("elite_breeding_threshold", ThresholdType.BREEDING_POTENTIAL, 0.9, 0.8, 1.0, "breeding_success"),
                
                # Alert sensitivity
                ("alert_sensitivity_high", ThresholdType.ALERT_SENSITIVITY, 0.8, 0.6, 0.95, "alert_accuracy"),
                ("alert_sensitivity_medium", ThresholdType.ALERT_SENSITIVITY, 0.6, 0.4, 0.8, "alert_accuracy")
            ]
            
            for threshold_id, threshold_type, default_val, min_val, max_val, optimization_target in threshold_configs:
                adaptive_threshold = AdaptiveThreshold(
                    threshold_id=threshold_id,
                    threshold_type=threshold_type,
                    current_value=default_val,
                    default_value=default_val,
                    min_value=min_val,
                    max_value=max_val,
                    optimization_target=optimization_target,
                    market_condition_modifiers=self._generate_default_market_modifiers(threshold_type)
                )
                
                self.adaptive_thresholds[threshold_id] = adaptive_threshold
            
            # Create threshold groups for coordinated optimization
            self.threshold_groups = {
                "signal_strength": ["vapi_fa_strong", "vapi_fa_moderate", "dwfd_strong", "dwfd_moderate", "tw_laf_strong", "tw_laf_moderate"],
                "confidence_levels": ["confidence_threshold", "high_confidence_threshold", "low_confidence_threshold"],
                "accuracy_control": ["accuracy_alert_threshold", "accuracy_optimization_threshold"],
                "volatility_detection": ["volatility_high_threshold", "volatility_low_threshold"],
                "volume_analysis": ["volume_surge_threshold", "volume_low_threshold"],
                "regime_analysis": ["regime_transition_sensitivity", "regime_stability_threshold"],
                "learning_control": ["learning_rate_adaptive", "adaptation_rate_threshold"],
                "breeding_optimization": ["breeding_potential_threshold", "elite_breeding_threshold"],
                "alert_management": ["alert_sensitivity_high", "alert_sensitivity_medium"]
            }
            
            self.logger.info(f"⚖️ Initialized {len(self.adaptive_thresholds)} adaptive thresholds in {len(self.threshold_groups)} groups")
            
        except Exception as e:
            self.logger.error(f"Error initializing default thresholds: {e}")
    
    def _generate_default_market_modifiers(self, threshold_type: ThresholdType) -> Dict[MarketCondition, float]:
        """Generate default market condition modifiers for threshold types."""
        try:
            # Base modifiers for different threshold types
            if threshold_type == ThresholdType.SIGNAL_STRENGTH:
                return {
                    MarketCondition.HIGH_VOLATILITY: 1.2,  # Increase signal thresholds in volatile markets
                    MarketCondition.LOW_VOLATILITY: 0.8,   # Decrease in stable markets
                    MarketCondition.TRENDING: 0.9,         # Slightly lower in trending markets
                    MarketCondition.CONSOLIDATING: 1.1     # Higher in consolidating markets
                }
            elif threshold_type == ThresholdType.CONFIDENCE_LEVEL:
                return {
                    MarketCondition.HIGH_VOLATILITY: 0.9,  # Lower confidence in volatile markets
                    MarketCondition.LOW_VOLATILITY: 1.1,   # Higher confidence in stable markets
                    MarketCondition.BULL_MARKET: 1.05,     # Slightly higher in bull markets
                    MarketCondition.BEAR_MARKET: 0.95      # Slightly lower in bear markets
                }
            elif threshold_type == ThresholdType.VOLATILITY_THRESHOLD:
                return {
                    MarketCondition.HIGH_VOLUME: 1.1,      # Adjust for volume effects
                    MarketCondition.LOW_VOLUME: 0.9,
                    MarketCondition.TRENDING: 1.05,
                    MarketCondition.CONSOLIDATING: 0.95
                }
            else:
                # Default modifiers for other threshold types
                return {
                    MarketCondition.HIGH_VOLATILITY: 1.0,
                    MarketCondition.LOW_VOLATILITY: 1.0,
                    MarketCondition.BULL_MARKET: 1.0,
                    MarketCondition.BEAR_MARKET: 1.0
                }
                
        except Exception as e:
            self.logger.error(f"Error generating market modifiers: {e}")
            return {}

    async def _initialize_market_condition_tracking(self):
        """Initialize market condition tracking system."""
        try:
            # Initialize current market conditions with neutral values
            for condition in MarketCondition:
                self.current_market_conditions[condition] = 0.5

            self.logger.info("📊 Market condition tracking initialized")

        except Exception as e:
            self.logger.error(f"Error initializing market condition tracking: {e}")

    async def _load_thresholds_from_database(self):
        """Load existing adaptive thresholds from database."""
        try:
            if self.db_integration:
                # This would load thresholds from Supabase
                self.logger.debug("🗄️ Loading adaptive thresholds from database")
        except Exception as e:
            self.logger.debug(f"Could not load thresholds from database: {e}")

    async def _optimization_scheduler(self):
        """Background scheduler for threshold optimization."""
        try:
            self.logger.info("🔄 Starting adaptive threshold optimization scheduler")

            while True:
                try:
                    # Wait for optimization interval
                    await asyncio.sleep(self.config.optimization_interval_minutes * 60)

                    # Skip if optimization already in progress
                    if self.optimization_in_progress:
                        continue

                    # Update market conditions
                    await self._update_market_conditions()

                    # Perform threshold optimization
                    await self._perform_scheduled_optimization()

                except Exception as e:
                    self.logger.error(f"Error in optimization scheduler: {e}")
                    await asyncio.sleep(60)  # Wait 1 minute before retrying

        except asyncio.CancelledError:
            self.logger.info("🔄 Optimization scheduler stopped")
        except Exception as e:
            self.logger.error(f"Optimization scheduler failed: {e}")

    async def _update_market_conditions(self):
        """Update current market conditions based on available data."""
        try:
            # Get market data from ecosystem if available
            if self.ecosystem:
                # This would analyze current market data to determine conditions
                # For now, simulate market condition detection

                # Example market condition analysis
                current_conditions = {
                    MarketCondition.HIGH_VOLATILITY: 0.6,  # Moderate-high volatility
                    MarketCondition.LOW_VOLATILITY: 0.4,   # Moderate-low volatility
                    MarketCondition.BULL_MARKET: 0.7,      # Bullish conditions
                    MarketCondition.BEAR_MARKET: 0.3,      # Low bearish conditions
                    MarketCondition.TRENDING: 0.8,         # Strong trending
                    MarketCondition.CONSOLIDATING: 0.2,    # Low consolidation
                    MarketCondition.HIGH_VOLUME: 0.5,      # Normal volume
                    MarketCondition.LOW_VOLUME: 0.5        # Normal volume
                }

                # Update current conditions
                self.current_market_conditions.update(current_conditions)

                # Add to history
                self.market_condition_history.append({
                    "timestamp": datetime.now(),
                    "conditions": current_conditions.copy()
                })

                self.logger.debug("📊 Market conditions updated")

        except Exception as e:
            self.logger.error(f"Error updating market conditions: {e}")

    async def _perform_scheduled_optimization(self):
        """Perform scheduled threshold optimization."""
        try:
            self.optimization_in_progress = True
            self.logger.info("⚖️ Starting scheduled threshold optimization")

            optimization_results = []

            # Optimize each threshold group
            for group_name, threshold_ids in self.threshold_groups.items():
                try:
                    group_results = await self._optimize_threshold_group(group_name, threshold_ids)
                    optimization_results.extend(group_results)

                except Exception as e:
                    self.logger.error(f"Error optimizing threshold group {group_name}: {e}")

            # Apply cross-system synchronization if enabled
            if self.config.enable_cross_system_synchronization:
                await self._synchronize_cross_system_thresholds()

            # Store optimization results
            self.optimization_history.extend(optimization_results)
            self.last_optimization = datetime.now()

            # Limit optimization history size
            if len(self.optimization_history) > 1000:
                self.optimization_history = self.optimization_history[-1000:]

            self.logger.info(f"✅ Scheduled optimization complete: {len(optimization_results)} thresholds optimized")

        except Exception as e:
            self.logger.error(f"Error in scheduled optimization: {e}")
        finally:
            self.optimization_in_progress = False

    async def _optimize_threshold_group(self, group_name: str, threshold_ids: List[str]) -> List[ThresholdOptimizationResult]:
        """Optimize a group of related thresholds."""
        try:
            optimization_results = []

            for threshold_id in threshold_ids:
                if threshold_id not in self.adaptive_thresholds:
                    continue

                threshold = self.adaptive_thresholds[threshold_id]

                # Get performance data for this threshold
                performance_data = await self._get_threshold_performance_data(threshold_id)

                if len(performance_data) < self.config.min_samples_for_optimization:
                    continue

                # Perform optimization based on enabled methods
                optimization_result = None

                if self.config.enable_performance_based_optimization:
                    optimization_result = await self._performance_based_optimization(threshold, performance_data)

                if optimization_result is None and self.config.enable_genetic_algorithm_optimization:
                    optimization_result = await self._genetic_algorithm_optimization(threshold, performance_data)

                if optimization_result is None and self.config.enable_market_condition_adaptation:
                    optimization_result = await self._market_condition_optimization(threshold)

                if optimization_result:
                    # Apply the optimization
                    await self._apply_threshold_optimization(threshold_id, optimization_result)
                    optimization_results.append(optimization_result)

            return optimization_results

        except Exception as e:
            self.logger.error(f"Error optimizing threshold group {group_name}: {e}")
            return []

    async def _get_threshold_performance_data(self, threshold_id: str) -> List[Tuple[float, float]]:
        """Get performance data for a specific threshold."""
        try:
            # Get performance correlations for this threshold
            if threshold_id in self.threshold_performance_correlations:
                return self.threshold_performance_correlations[threshold_id]

            # If no specific data, try to get general performance data
            if self.performance_tracker:
                # Get recent performance metrics
                real_time_metrics = await self.performance_tracker.get_real_time_performance_metrics()

                if real_time_metrics.get("status") == "active":
                    metrics = real_time_metrics.get("metrics", {})

                    # Create synthetic performance data based on current threshold value
                    threshold = self.adaptive_thresholds[threshold_id]
                    performance_data = []

                    # Generate performance correlation data
                    for i in range(min(50, threshold.usage_frequency)):
                        # Simulate threshold value variations
                        threshold_variation = threshold.current_value * (0.8 + 0.4 * (i / 50))

                        # Simulate corresponding performance
                        base_performance = 0.7  # Base performance
                        performance_variation = base_performance + 0.2 * np.sin(i * 0.1)  # Add some variation

                        performance_data.append((threshold_variation, performance_variation))

                    return performance_data

            return []

        except Exception as e:
            self.logger.error(f"Error getting performance data for threshold {threshold_id}: {e}")
            return []

    async def _performance_based_optimization(self, threshold: AdaptiveThreshold,
                                            performance_data: List[Tuple[float, float]]) -> Optional[ThresholdOptimizationResult]:
        """Optimize threshold based on performance correlation analysis."""
        try:
            if len(performance_data) < 3:
                return None

            # Extract threshold values and performance scores
            threshold_values = [data[0] for data in performance_data]
            performance_scores = [data[1] for data in performance_data]

            # Find optimal threshold value using performance correlation
            best_performance = max(performance_scores)
            best_threshold_idx = performance_scores.index(best_performance)
            optimal_threshold = threshold_values[best_threshold_idx]

            # Calculate improvement score
            current_performance = threshold.effectiveness_score
            improvement_score = min((best_performance - current_performance) / max(current_performance, 0.1), 1.0)

            # Ensure threshold is within bounds
            optimal_threshold = max(threshold.min_value, min(threshold.max_value, optimal_threshold))

            # Apply stability factor to prevent excessive changes
            adjustment_factor = threshold.stability_factor
            new_value = threshold.current_value + (optimal_threshold - threshold.current_value) * adjustment_factor

            # Limit maximum adjustment per cycle
            max_change = threshold.current_value * self.config.max_adjustment_per_cycle
            if abs(new_value - threshold.current_value) > max_change:
                new_value = threshold.current_value + np.sign(new_value - threshold.current_value) * max_change

            # Only optimize if improvement is significant
            if improvement_score > 0.05:  # 5% improvement threshold
                return ThresholdOptimizationResult(
                    threshold_id=threshold.threshold_id,
                    old_value=threshold.current_value,
                    new_value=new_value,
                    improvement_score=improvement_score,
                    confidence=min(len(performance_data) / 100, 1.0),  # Confidence based on data amount
                    optimization_method="performance_based",
                    market_context=self.current_market_conditions.copy()
                )

            return None

        except Exception as e:
            self.logger.error(f"Error in performance-based optimization: {e}")
            return None

    async def _genetic_algorithm_optimization(self, threshold: AdaptiveThreshold,
                                            performance_data: List[Tuple[float, float]]) -> Optional[ThresholdOptimizationResult]:
        """Optimize threshold using genetic algorithm approach."""
        try:
            if len(performance_data) < 5:
                return None

            # Genetic algorithm parameters
            population_size = 20
            generations = 10
            mutation_rate = 0.1

            # Initialize population around current threshold value
            population = []
            for _ in range(population_size):
                # Generate candidate threshold values
                variation = np.random.normal(0, threshold.current_value * 0.1)
                candidate = threshold.current_value + variation
                candidate = max(threshold.min_value, min(threshold.max_value, candidate))
                population.append(candidate)

            # Evolution loop
            for generation in range(generations):
                # Evaluate fitness for each candidate
                fitness_scores = []
                for candidate in population:
                    fitness = self._evaluate_threshold_fitness(candidate, threshold, performance_data)
                    fitness_scores.append(fitness)

                # Selection: keep top 50%
                sorted_indices = sorted(range(len(fitness_scores)), key=lambda i: fitness_scores[i], reverse=True)
                survivors = [population[i] for i in sorted_indices[:population_size // 2]]

                # Reproduction: create new population
                new_population = survivors.copy()
                while len(new_population) < population_size:
                    # Select two parents
                    parent1 = np.random.choice(survivors)
                    parent2 = np.random.choice(survivors)

                    # Crossover
                    child = (parent1 + parent2) / 2

                    # Mutation
                    if np.random.random() < mutation_rate:
                        mutation = np.random.normal(0, threshold.current_value * 0.05)
                        child += mutation

                    # Ensure bounds
                    child = max(threshold.min_value, min(threshold.max_value, child))
                    new_population.append(child)

                population = new_population

            # Get best solution
            final_fitness = [self._evaluate_threshold_fitness(candidate, threshold, performance_data)
                           for candidate in population]
            best_idx = final_fitness.index(max(final_fitness))
            optimal_threshold = population[best_idx]

            # Calculate improvement
            current_fitness = self._evaluate_threshold_fitness(threshold.current_value, threshold, performance_data)
            best_fitness = final_fitness[best_idx]
            improvement_score = (best_fitness - current_fitness) / max(current_fitness, 0.1)

            if improvement_score > 0.03:  # 3% improvement threshold for genetic algorithm
                return ThresholdOptimizationResult(
                    threshold_id=threshold.threshold_id,
                    old_value=threshold.current_value,
                    new_value=optimal_threshold,
                    improvement_score=improvement_score,
                    confidence=0.8,  # High confidence in genetic algorithm
                    optimization_method="genetic_algorithm",
                    market_context=self.current_market_conditions.copy()
                )

            return None

        except Exception as e:
            self.logger.error(f"Error in genetic algorithm optimization: {e}")
            return None

    def _evaluate_threshold_fitness(self, candidate_value: float, threshold: AdaptiveThreshold,
                                  performance_data: List[Tuple[float, float]]) -> float:
        """Evaluate fitness of a candidate threshold value."""
        try:
            # Find closest performance data points
            distances = [abs(candidate_value - data[0]) for data in performance_data]
            closest_indices = sorted(range(len(distances)), key=lambda i: distances[i])[:3]

            # Calculate weighted average performance
            total_weight = 0
            weighted_performance = 0

            for idx in closest_indices:
                distance = distances[idx]
                weight = 1.0 / (1.0 + distance)  # Inverse distance weighting
                performance = performance_data[idx][1]

                weighted_performance += weight * performance
                total_weight += weight

            if total_weight > 0:
                fitness = weighted_performance / total_weight
            else:
                fitness = 0.5  # Default fitness

            # Apply penalty for extreme values
            if candidate_value <= threshold.min_value or candidate_value >= threshold.max_value:
                fitness *= 0.5

            return fitness

        except Exception as e:
            self.logger.error(f"Error evaluating threshold fitness: {e}")
            return 0.5

    async def _market_condition_optimization(self, threshold: AdaptiveThreshold) -> Optional[ThresholdOptimizationResult]:
        """Optimize threshold based on current market conditions."""
        try:
            # Calculate market condition adjustment
            total_adjustment = 1.0
            active_conditions = 0

            for condition, strength in self.current_market_conditions.items():
                if condition in threshold.market_condition_modifiers and strength > 0.6:  # Only strong conditions
                    modifier = threshold.market_condition_modifiers[condition]
                    condition_adjustment = 1.0 + (modifier - 1.0) * strength * threshold.condition_sensitivity
                    total_adjustment *= condition_adjustment
                    active_conditions += 1

            # Only apply if there are active market conditions
            if active_conditions == 0:
                return None

            # Calculate new threshold value
            new_value = threshold.current_value * total_adjustment
            new_value = max(threshold.min_value, min(threshold.max_value, new_value))

            # Calculate improvement score based on market alignment
            improvement_score = min(abs(total_adjustment - 1.0), 0.3)  # Cap at 30% improvement

            # Only optimize if change is significant
            if abs(new_value - threshold.current_value) > threshold.current_value * 0.02:  # 2% change threshold
                return ThresholdOptimizationResult(
                    threshold_id=threshold.threshold_id,
                    old_value=threshold.current_value,
                    new_value=new_value,
                    improvement_score=improvement_score,
                    confidence=min(active_conditions / 3, 1.0),  # Confidence based on active conditions
                    optimization_method="market_condition",
                    market_context=self.current_market_conditions.copy()
                )

            return None

        except Exception as e:
            self.logger.error(f"Error in market condition optimization: {e}")
            return None

    async def _apply_threshold_optimization(self, threshold_id: str, optimization_result: ThresholdOptimizationResult):
        """Apply threshold optimization result."""
        try:
            if threshold_id not in self.adaptive_thresholds:
                return

            threshold = self.adaptive_thresholds[threshold_id]

            # Update threshold value
            old_value = threshold.current_value
            threshold.current_value = optimization_result.new_value
            threshold.last_adjustment = datetime.now()

            # Update adjustment history
            adjustment_record = {
                "timestamp": datetime.now().isoformat(),
                "old_value": old_value,
                "new_value": optimization_result.new_value,
                "method": optimization_result.optimization_method,
                "improvement_score": optimization_result.improvement_score,
                "market_context": optimization_result.market_context
            }

            threshold.adjustment_history.append(adjustment_record)

            # Limit history size
            if len(threshold.adjustment_history) > 100:
                threshold.adjustment_history = threshold.adjustment_history[-100:]

            # Update effectiveness score based on optimization
            threshold.effectiveness_score = min(threshold.effectiveness_score + optimization_result.improvement_score * 0.1, 1.0)

            # Store in database if available
            if DATABASE_AVAILABLE and self.db_integration:
                await self._store_threshold_update(threshold_id, threshold)

            self.logger.info(f"⚖️ Applied optimization to {threshold_id}: {old_value:.4f} → {optimization_result.new_value:.4f}")

        except Exception as e:
            self.logger.error(f"Error applying threshold optimization: {e}")

    async def _synchronize_cross_system_thresholds(self):
        """Synchronize thresholds across different AI systems."""
        try:
            # Update ecosystem thresholds if available
            if self.ecosystem:
                ecosystem_thresholds = {}

                # Map adaptive thresholds to ecosystem format
                for threshold_id, threshold in self.adaptive_thresholds.items():
                    if threshold.threshold_type == ThresholdType.SIGNAL_STRENGTH:
                        ecosystem_thresholds[threshold_id] = threshold.current_value
                    elif threshold.threshold_type == ThresholdType.CONFIDENCE_LEVEL:
                        ecosystem_thresholds[f"confidence_{threshold_id}"] = threshold.current_value

                # Apply to ecosystem (this would update the actual system thresholds)
                # ecosystem.update_adaptive_thresholds(ecosystem_thresholds)

                self.logger.debug("🔗 Cross-system threshold synchronization completed")

            # Update self-learning engine thresholds if available
            if self.self_learning_engine:
                learning_thresholds = {}

                for threshold_id, threshold in self.adaptive_thresholds.items():
                    if threshold.threshold_type in [ThresholdType.LEARNING_RATE, ThresholdType.ADAPTATION_RATE]:
                        learning_thresholds[threshold_id] = threshold.current_value

                # Apply to self-learning engine
                # self.self_learning_engine.update_adaptive_thresholds(learning_thresholds)

                self.logger.debug("🧠 Self-learning engine threshold synchronization completed")

        except Exception as e:
            self.logger.error(f"Error in cross-system threshold synchronization: {e}")

    async def _store_threshold_update(self, threshold_id: str, threshold: AdaptiveThreshold):
        """Store threshold update in database."""
        try:
            if self.db_integration:
                # This would store threshold updates in Supabase
                self.logger.debug(f"🗄️ Storing threshold update for {threshold_id}")
        except Exception as e:
            self.logger.debug(f"Could not store threshold update: {e}")

    # ===== PUBLIC INTERFACE METHODS =====

    async def get_threshold_value(self, threshold_id: str) -> Optional[float]:
        """Get current value of an adaptive threshold."""
        try:
            if threshold_id in self.adaptive_thresholds:
                threshold = self.adaptive_thresholds[threshold_id]

                # Apply real-time market condition adjustments if enabled
                if self.config.enable_market_condition_adaptation:
                    base_value = threshold.current_value
                    market_adjustment = await self._calculate_real_time_market_adjustment(threshold)
                    return base_value * market_adjustment

                return threshold.current_value

            return None

        except Exception as e:
            self.logger.error(f"Error getting threshold value for {threshold_id}: {e}")
            return None

    async def _calculate_real_time_market_adjustment(self, threshold: AdaptiveThreshold) -> float:
        """Calculate real-time market condition adjustment for threshold."""
        try:
            adjustment = 1.0

            for condition, strength in self.current_market_conditions.items():
                if condition in threshold.market_condition_modifiers and strength > 0.5:
                    modifier = threshold.market_condition_modifiers[condition]
                    condition_adjustment = 1.0 + (modifier - 1.0) * strength * threshold.condition_sensitivity * 0.5  # Reduced for real-time
                    adjustment *= condition_adjustment

            return adjustment

        except Exception as e:
            self.logger.error(f"Error calculating real-time market adjustment: {e}")
            return 1.0

    async def update_threshold_performance(self, threshold_id: str, performance_score: float,
                                         threshold_value_used: Optional[float] = None):
        """Update performance data for a threshold."""
        try:
            if threshold_id not in self.adaptive_thresholds:
                return

            threshold = self.adaptive_thresholds[threshold_id]

            # Update usage frequency
            threshold.usage_frequency += 1

            # Add to performance history
            threshold.performance_history.append(performance_score)
            threshold.last_performance_update = datetime.now()

            # Limit performance history size
            if len(threshold.performance_history) > threshold.learning_window:
                threshold.performance_history = threshold.performance_history[-threshold.learning_window:]

            # Update performance correlation data
            if threshold_value_used is not None:
                self.threshold_performance_correlations[threshold_id].append((threshold_value_used, performance_score))

                # Limit correlation data size
                if len(self.threshold_performance_correlations[threshold_id]) > threshold.learning_window:
                    self.threshold_performance_correlations[threshold_id] = \
                        self.threshold_performance_correlations[threshold_id][-threshold.learning_window:]

            # Update effectiveness score (exponential moving average)
            alpha = 0.1  # Learning rate for effectiveness update
            threshold.effectiveness_score = (1 - alpha) * threshold.effectiveness_score + alpha * performance_score

            self.logger.debug(f"📊 Updated performance for threshold {threshold_id}: {performance_score:.3f}")

        except Exception as e:
            self.logger.error(f"Error updating threshold performance: {e}")

    async def force_threshold_optimization(self, threshold_id: Optional[str] = None) -> List[ThresholdOptimizationResult]:
        """Force immediate optimization of specific threshold or all thresholds."""
        try:
            if self.optimization_in_progress:
                self.logger.warning("⚠️ Optimization already in progress")
                return []

            self.optimization_in_progress = True
            optimization_results = []

            if threshold_id:
                # Optimize specific threshold
                if threshold_id in self.adaptive_thresholds:
                    threshold = self.adaptive_thresholds[threshold_id]
                    performance_data = await self._get_threshold_performance_data(threshold_id)

                    # Try different optimization methods
                    result = await self._performance_based_optimization(threshold, performance_data)
                    if result is None:
                        result = await self._genetic_algorithm_optimization(threshold, performance_data)
                    if result is None:
                        result = await self._market_condition_optimization(threshold)

                    if result:
                        await self._apply_threshold_optimization(threshold_id, result)
                        optimization_results.append(result)
            else:
                # Optimize all thresholds
                for group_name, threshold_ids in self.threshold_groups.items():
                    group_results = await self._optimize_threshold_group(group_name, threshold_ids)
                    optimization_results.extend(group_results)

            self.logger.info(f"⚖️ Forced optimization complete: {len(optimization_results)} thresholds optimized")
            return optimization_results

        except Exception as e:
            self.logger.error(f"Error in forced threshold optimization: {e}")
            return []
        finally:
            self.optimization_in_progress = False

    async def get_threshold_status(self, threshold_id: Optional[str] = None) -> Dict[str, Any]:
        """Get status information for thresholds."""
        try:
            if threshold_id:
                # Get status for specific threshold
                if threshold_id not in self.adaptive_thresholds:
                    return {"error": f"Threshold {threshold_id} not found"}

                threshold = self.adaptive_thresholds[threshold_id]

                return {
                    "threshold_id": threshold_id,
                    "current_value": threshold.current_value,
                    "default_value": threshold.default_value,
                    "effectiveness_score": threshold.effectiveness_score,
                    "usage_frequency": threshold.usage_frequency,
                    "last_adjustment": threshold.last_adjustment.isoformat() if threshold.last_adjustment else None,
                    "performance_correlation": threshold.performance_correlation,
                    "recent_performance": threshold.performance_history[-10:] if threshold.performance_history else [],
                    "market_modifiers": threshold.market_condition_modifiers,
                    "optimization_target": threshold.optimization_target
                }
            else:
                # Get status for all thresholds
                status = {
                    "total_thresholds": len(self.adaptive_thresholds),
                    "threshold_groups": len(self.threshold_groups),
                    "last_optimization": self.last_optimization.isoformat() if self.last_optimization else None,
                    "optimization_in_progress": self.optimization_in_progress,
                    "recent_optimizations": len(self.optimization_history),
                    "market_conditions": self.current_market_conditions,
                    "config": self.config.model_dump()
                }

                # Add summary statistics
                effectiveness_scores = [t.effectiveness_score for t in self.adaptive_thresholds.values()]
                if effectiveness_scores:
                    status["average_effectiveness"] = statistics.mean(effectiveness_scores)
                    status["min_effectiveness"] = min(effectiveness_scores)
                    status["max_effectiveness"] = max(effectiveness_scores)

                return status

        except Exception as e:
            self.logger.error(f"Error getting threshold status: {e}")
            return {"error": str(e)}

    async def export_threshold_configuration(self) -> Dict[str, Any]:
        """Export current threshold configuration for backup or analysis."""
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "config": self.config.model_dump(),
                "thresholds": {},
                "threshold_groups": self.threshold_groups,
                "market_conditions": self.current_market_conditions,
                "optimization_history": [result.model_dump() for result in self.optimization_history[-50:]]  # Last 50
            }

            # Export threshold data
            for threshold_id, threshold in self.adaptive_thresholds.items():
                export_data["thresholds"][threshold_id] = threshold.model_dump()

            return export_data

        except Exception as e:
            self.logger.error(f"Error exporting threshold configuration: {e}")
            return {"error": str(e)}

    async def import_threshold_configuration(self, config_data: Dict[str, Any]) -> bool:
        """Import threshold configuration from backup or external source."""
        try:
            # Validate configuration data
            if "thresholds" not in config_data:
                raise ValueError("Invalid configuration data: missing thresholds")

            # Import thresholds
            imported_count = 0
            for threshold_id, threshold_data in config_data["thresholds"].items():
                try:
                    threshold = AdaptiveThreshold(**threshold_data)
                    self.adaptive_thresholds[threshold_id] = threshold
                    imported_count += 1
                except Exception as e:
                    self.logger.warning(f"Failed to import threshold {threshold_id}: {e}")

            # Import threshold groups if available
            if "threshold_groups" in config_data:
                self.threshold_groups.update(config_data["threshold_groups"])

            # Import configuration if available
            if "config" in config_data:
                try:
                    self.config = ThresholdManagerConfig(**config_data["config"])
                except Exception as e:
                    self.logger.warning(f"Failed to import configuration: {e}")

            self.logger.info(f"⚖️ Imported {imported_count} adaptive thresholds")
            return True

        except Exception as e:
            self.logger.error(f"Error importing threshold configuration: {e}")
            return False

    async def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations for threshold optimization."""
        try:
            recommendations = []

            for threshold_id, threshold in self.adaptive_thresholds.items():
                # Check if threshold needs optimization
                needs_optimization = False
                recommendation_reasons = []

                # Check effectiveness score
                if threshold.effectiveness_score < 0.6:
                    needs_optimization = True
                    recommendation_reasons.append(f"Low effectiveness score: {threshold.effectiveness_score:.3f}")

                # Check if threshold hasn't been adjusted recently
                if threshold.last_adjustment:
                    days_since_adjustment = (datetime.now() - threshold.last_adjustment).days
                    if days_since_adjustment > 7:
                        needs_optimization = True
                        recommendation_reasons.append(f"No adjustment in {days_since_adjustment} days")

                # Check performance trend
                if len(threshold.performance_history) >= 5:
                    recent_performance = statistics.mean(threshold.performance_history[-5:])
                    older_performance = statistics.mean(threshold.performance_history[-10:-5]) if len(threshold.performance_history) >= 10 else recent_performance

                    if recent_performance < older_performance - 0.1:
                        needs_optimization = True
                        recommendation_reasons.append(f"Declining performance trend: {recent_performance:.3f} vs {older_performance:.3f}")

                # Check usage frequency
                if threshold.usage_frequency < 5:
                    recommendation_reasons.append(f"Low usage frequency: {threshold.usage_frequency}")

                if needs_optimization:
                    recommendations.append({
                        "threshold_id": threshold_id,
                        "threshold_type": threshold.threshold_type.value,
                        "current_value": threshold.current_value,
                        "effectiveness_score": threshold.effectiveness_score,
                        "reasons": recommendation_reasons,
                        "priority": "high" if threshold.effectiveness_score < 0.5 else "medium",
                        "suggested_methods": ["performance_based", "genetic_algorithm"] if len(threshold.performance_history) >= 10 else ["market_condition"]
                    })

            # Sort by priority and effectiveness score
            recommendations.sort(key=lambda x: (x["priority"] == "high", -x["effectiveness_score"]), reverse=True)

            return recommendations

        except Exception as e:
            self.logger.error(f"Error getting optimization recommendations: {e}")
            return []


# ===== GLOBAL ADAPTIVE THRESHOLD MANAGER INSTANCE =====

_global_threshold_manager: Optional[AdaptiveThresholdManager] = None

async def get_adaptive_threshold_manager() -> AdaptiveThresholdManager:
    """Get or create the global adaptive threshold manager instance."""
    global _global_threshold_manager

    if _global_threshold_manager is None:
        # Initialize database integration if available
        db_integration = None
        if DATABASE_AVAILABLE:
            try:
                db_integration = await get_ai_database_integration()
            except Exception as e:
                logger.warning(f"Could not initialize database integration: {e}")

        # Create manager with default configuration
        config = ThresholdManagerConfig()
        _global_threshold_manager = AdaptiveThresholdManager(config, db_integration)

        # Initialize the manager
        await _global_threshold_manager.initialize_manager()

        logger.info("⚖️ Global Adaptive Threshold Manager initialized")

    return _global_threshold_manager

async def get_adaptive_threshold(threshold_id: str) -> Optional[float]:
    """Get current value of an adaptive threshold."""
    try:
        manager = await get_adaptive_threshold_manager()
        return await manager.get_threshold_value(threshold_id)
    except Exception as e:
        logger.error(f"Error getting adaptive threshold {threshold_id}: {e}")
        return None

async def update_threshold_performance(threshold_id: str, performance_score: float,
                                     threshold_value_used: Optional[float] = None):
    """Update performance data for a threshold."""
    try:
        manager = await get_adaptive_threshold_manager()
        await manager.update_threshold_performance(threshold_id, performance_score, threshold_value_used)
    except Exception as e:
        logger.error(f"Error updating threshold performance: {e}")

async def optimize_thresholds(threshold_id: Optional[str] = None) -> List[ThresholdOptimizationResult]:
    """Force optimization of specific threshold or all thresholds."""
    try:
        manager = await get_adaptive_threshold_manager()
        return await manager.force_threshold_optimization(threshold_id)
    except Exception as e:
        logger.error(f"Error optimizing thresholds: {e}")
        return []

async def get_threshold_status(threshold_id: Optional[str] = None) -> Dict[str, Any]:
    """Get status information for thresholds."""
    try:
        manager = await get_adaptive_threshold_manager()
        return await manager.get_threshold_status(threshold_id)
    except Exception as e:
        logger.error(f"Error getting threshold status: {e}")
        return {"error": str(e)}

async def get_optimization_recommendations() -> List[Dict[str, Any]]:
    """Get recommendations for threshold optimization."""
    try:
        manager = await get_adaptive_threshold_manager()
        return await manager.get_optimization_recommendations()
    except Exception as e:
        logger.error(f"Error getting optimization recommendations: {e}")
        return []

async def export_threshold_config() -> Dict[str, Any]:
    """Export current threshold configuration."""
    try:
        manager = await get_adaptive_threshold_manager()
        return await manager.export_threshold_configuration()
    except Exception as e:
        logger.error(f"Error exporting threshold configuration: {e}")
        return {"error": str(e)}

async def import_threshold_config(config_data: Dict[str, Any]) -> bool:
    """Import threshold configuration."""
    try:
        manager = await get_adaptive_threshold_manager()
        return await manager.import_threshold_configuration(config_data)
    except Exception as e:
        logger.error(f"Error importing threshold configuration: {e}")
        return False

# ===== INTEGRATION HELPERS =====

async def integrate_with_performance_tracker():
    """Integrate adaptive threshold manager with performance tracking system."""
    try:
        if PERFORMANCE_TRACKING_AVAILABLE:
            manager = await get_adaptive_threshold_manager()
            performance_tracker = await get_performance_tracker()

            # This would set up automatic performance feedback to threshold manager
            logger.info("🔗 Integrated adaptive threshold manager with performance tracker")
            return True
    except Exception as e:
        logger.error(f"Error integrating with performance tracker: {e}")
    return False

async def integrate_with_ai_ecosystem():
    """Integrate adaptive threshold manager with AI ecosystem."""
    try:
        if ECOSYSTEM_AVAILABLE:
            manager = await get_adaptive_threshold_manager()
            ecosystem = await get_unified_ai_ecosystem()

            # This would set up automatic threshold updates in the ecosystem
            logger.info("🔗 Integrated adaptive threshold manager with AI ecosystem")
            return True
    except Exception as e:
        logger.error(f"Error integrating with AI ecosystem: {e}")
    return False
