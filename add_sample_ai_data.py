#!/usr/bin/env python3
"""
Add Sample AI Performance Data
==============================

This script adds sample AI prediction data to the database so the AI Performance Tracker
has data to display. This is needed because the database is empty after initial setup.

Author: EOTS v2.5 Development Team
"""

import sqlite3
import random
from datetime import datetime, timed<PERSON>ta

def add_sample_ai_predictions():
    """Add sample AI prediction data to the database."""
    try:
        # Connect to the database
        conn = sqlite3.connect('data/elite_options.db')
        cursor = conn.cursor()
        
        print("🔄 Adding sample AI prediction data...")
        
        # Create ai_predictions table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                prediction_type TEXT NOT NULL,
                prediction_value REAL,
                prediction_direction TEXT NOT NULL,
                confidence_score REAL NOT NULL,
                time_horizon TEXT NOT NULL,
                prediction_timestamp DATETIME NOT NULL,
                target_timestamp DATETIME,
                market_context TEXT,
                model_version TEXT DEFAULT 'v2.5',
                prediction_accurate INTEGER,
                actual_direction TEXT,
                accuracy_score REAL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL
            )
        ''')
        
        # Add sample predictions for the last 7 days
        symbols = ['SPY', 'QQQ', 'IWM']
        prediction_types = ['eots_direction', 'volatility', 'momentum']
        directions = ['UP', 'DOWN', 'NEUTRAL']
        
        predictions_added = 0
        
        for days_ago in range(7, 0, -1):
            prediction_date = datetime.now() - timedelta(days=days_ago)
            
            for symbol in symbols:
                # Add 2-4 predictions per symbol per day
                for i in range(random.randint(2, 4)):
                    prediction_type = random.choice(prediction_types)
                    direction = random.choice(directions)
                    confidence = random.uniform(0.6, 0.9)
                    prediction_value = random.uniform(1.0, 5.0)
                    
                    # Simulate some predictions being accurate (70% accuracy)
                    is_accurate = random.choice([1, 1, 1, 0])  # 75% accuracy
                    actual_direction = direction if is_accurate else random.choice(directions)
                    accuracy_score = confidence if is_accurate else confidence * 0.5
                    
                    target_time = prediction_date + timedelta(hours=4)
                    
                    cursor.execute('''
                        INSERT INTO ai_predictions (
                            symbol, prediction_type, prediction_value, prediction_direction,
                            confidence_score, time_horizon, prediction_timestamp, target_timestamp,
                            market_context, prediction_accurate, actual_direction, accuracy_score,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        symbol, prediction_type, prediction_value, direction,
                        confidence, '4H', prediction_date, target_time,
                        '{"source": "sample_data", "regime": "normal"}',
                        is_accurate, actual_direction, accuracy_score,
                        prediction_date, prediction_date
                    ))
                    
                    predictions_added += 1
        
        # Create ai_learning_patterns table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_learning_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_name TEXT NOT NULL,
                confidence_score REAL NOT NULL,
                success_rate REAL NOT NULL,
                created_at DATETIME NOT NULL
            )
        ''')
        
        # Add sample learning patterns
        patterns = [
            'bullish_momentum', 'bearish_reversal', 'volatility_expansion',
            'support_bounce', 'resistance_break', 'consolidation_pattern'
        ]
        
        for pattern in patterns:
            for days_ago in range(7, 0, -1):
                pattern_date = datetime.now() - timedelta(days=days_ago)
                confidence = random.uniform(0.6, 0.9)
                success_rate = random.uniform(0.65, 0.85)
                
                cursor.execute('''
                    INSERT INTO ai_learning_patterns (
                        pattern_name, confidence_score, success_rate, created_at
                    ) VALUES (?, ?, ?, ?)
                ''', (pattern, confidence, success_rate, pattern_date))
        
        # Create ai_adaptations table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_adaptations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT DEFAULT 'SPY',
                adaptation_type TEXT NOT NULL,
                success_rate REAL NOT NULL,
                adaptation_score REAL,
                created_at DATETIME NOT NULL
            )
        ''')
        
        # Add sample adaptations
        adaptation_types = ['threshold_adjustment', 'weight_optimization', 'pattern_refinement']
        
        for adaptation_type in adaptation_types:
            for days_ago in range(7, 0, -1):
                adaptation_date = datetime.now() - timedelta(days=days_ago)
                success_rate = random.uniform(0.7, 0.9)
                adaptation_score = random.uniform(7.0, 9.0)
                
                cursor.execute('''
                    INSERT INTO ai_adaptations (
                        symbol, adaptation_type, success_rate, adaptation_score, created_at
                    ) VALUES (?, ?, ?, ?, ?)
                ''', ('SPY', adaptation_type, success_rate, adaptation_score, adaptation_date))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Successfully added {predictions_added} AI predictions")
        print(f"✅ Added {len(patterns) * 7} learning patterns")
        print(f"✅ Added {len(adaptation_types) * 7} adaptations")
        print("🎉 Sample AI data setup complete!")
        print("\n📊 The AI Performance Tracker should now display data!")
        
    except Exception as e:
        print(f"❌ Error adding sample data: {e}")

if __name__ == "__main__":
    add_sample_ai_predictions()
