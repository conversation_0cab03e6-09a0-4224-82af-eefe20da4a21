"""
Elite Performance Tracking & Validation System for EOTS v2.5
============================================================

This module implements the most sophisticated performance tracking and validation
system possible for the AI ecosystem, providing comprehensive monitoring,
real-time validation, and predictive analytics for all AI agents and systems.

Features:
- Real-Time Performance Monitoring with Multi-Dimensional Analytics
- Predictive Accuracy Validation with Market Outcome Correlation
- AI Agent Performance Scoring with Breeding Potential Optimization
- Cross-System Performance Comparison and Ensemble Effectiveness
- Historical Performance Trend Analysis with Predictive Forecasting
- Automated Performance Alerts and Optimization Recommendations
- Comprehensive Performance Dashboards with Interactive Visualizations
- Database-Driven Analytics with Supabase Integration

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "ELITE PERFORMANCE ANALYTICS"
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import statistics
import numpy as np
from collections import defaultdict, deque

# Pydantic imports
from pydantic import BaseModel, Field

# Import EOTS schemas
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5

# Import AI ecosystem components
try:
    from .unified_ai_ecosystem import get_unified_ai_ecosystem, UnifiedAIEcosystem
    ECOSYSTEM_AVAILABLE = True
except ImportError:
    ECOSYSTEM_AVAILABLE = False

try:
    from .self_learning_engine import get_self_learning_engine, EliteSelfLearningEngine
    SELF_LEARNING_AVAILABLE = True
except ImportError:
    SELF_LEARNING_AVAILABLE = False

# Import database integration
try:
    from database_management.ai_intelligence_integration import get_ai_database_integration
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

logger = logging.getLogger(__name__)

# ===== PYDANTIC MODELS FOR PERFORMANCE TRACKING =====

class PerformanceMetricType(str, Enum):
    """Types of performance metrics."""
    ACCURACY = "accuracy"
    CONFIDENCE_CALIBRATION = "confidence_calibration"
    PREDICTION_TIMING = "prediction_timing"
    INSIGHT_QUALITY = "insight_quality"
    REGIME_DETECTION = "regime_detection"
    BREEDING_POTENTIAL = "breeding_potential"
    LEARNING_VELOCITY = "learning_velocity"
    ADAPTATION_EFFECTIVENESS = "adaptation_effectiveness"

class PerformanceScore(BaseModel):
    """Individual performance score record."""
    metric_type: PerformanceMetricType = Field(..., description="Type of performance metric")
    score: float = Field(..., ge=0.0, le=1.0, description="Performance score (0-1)")
    timestamp: datetime = Field(default_factory=datetime.now)
    agent_id: Optional[str] = None
    system_name: Optional[str] = None
    market_context: Dict[str, Any] = Field(default_factory=dict)
    validation_data: Dict[str, Any] = Field(default_factory=dict)

class ValidationResult(BaseModel):
    """Result of prediction validation against market outcomes."""
    prediction_id: str = Field(..., description="Unique prediction identifier")
    predicted_outcome: Dict[str, Any] = Field(..., description="Original AI prediction")
    actual_outcome: Dict[str, Any] = Field(..., description="Actual market outcome")
    accuracy_score: float = Field(..., ge=0.0, le=1.0, description="Prediction accuracy")
    confidence_error: float = Field(..., ge=0.0, le=1.0, description="Confidence calibration error")
    timing_accuracy: float = Field(..., ge=0.0, le=1.0, description="Timing prediction accuracy")
    insight_relevance: float = Field(..., ge=0.0, le=1.0, description="Insight relevance score")
    validation_timestamp: datetime = Field(default_factory=datetime.now)
    market_conditions: Dict[str, Any] = Field(default_factory=dict)

class PerformanceTrend(BaseModel):
    """Performance trend analysis."""
    metric_type: PerformanceMetricType = Field(..., description="Metric being analyzed")
    trend_direction: str = Field(..., description="improving, declining, stable")
    trend_strength: float = Field(..., ge=0.0, le=1.0, description="Strength of trend")
    current_score: float = Field(..., ge=0.0, le=1.0, description="Current performance score")
    projected_score: float = Field(..., ge=0.0, le=1.0, description="Projected future score")
    confidence_interval: Tuple[float, float] = Field(..., description="95% confidence interval")
    analysis_period_days: int = Field(..., gt=0, description="Days of data analyzed")

class SystemPerformanceReport(BaseModel):
    """Comprehensive system performance report."""
    report_timestamp: datetime = Field(default_factory=datetime.now)
    overall_performance_score: float = Field(..., ge=0.0, le=1.0)
    individual_scores: Dict[PerformanceMetricType, float] = Field(default_factory=dict)
    performance_trends: List[PerformanceTrend] = Field(default_factory=list)
    top_performing_agents: List[Dict[str, Any]] = Field(default_factory=list)
    improvement_recommendations: List[str] = Field(default_factory=list)
    alert_conditions: List[str] = Field(default_factory=list)
    validation_summary: Dict[str, Any] = Field(default_factory=dict)

# ===== ELITE PERFORMANCE TRACKING ENGINE =====

class ElitePerformanceTracker:
    """
    ELITE PERFORMANCE TRACKING ENGINE
    
    The most sophisticated performance tracking and validation system possible,
    providing comprehensive monitoring, real-time validation, and predictive
    analytics for the entire AI ecosystem.
    """
    
    def __init__(self, db_integration=None):
        self.db_integration = db_integration
        self.logger = logger.getChild(self.__class__.__name__)
        
        # Performance data storage
        self.performance_history: deque = deque(maxlen=10000)  # Last 10k performance records
        self.validation_results: deque = deque(maxlen=5000)    # Last 5k validation results
        self.trend_analysis_cache: Dict[str, PerformanceTrend] = {}
        
        # Real-time monitoring
        self.active_predictions: Dict[str, Dict[str, Any]] = {}
        self.performance_alerts: List[Dict[str, Any]] = []
        self.monitoring_enabled = True
        
        # Analytics configuration
        self.trend_analysis_window = 30  # Days
        self.alert_thresholds = {
            PerformanceMetricType.ACCURACY: 0.6,
            PerformanceMetricType.CONFIDENCE_CALIBRATION: 0.7,
            PerformanceMetricType.BREEDING_POTENTIAL: 0.8,
            PerformanceMetricType.LEARNING_VELOCITY: 0.3
        }
        
        # Performance benchmarks
        self.performance_benchmarks = {
            "excellent": 0.9,
            "good": 0.75,
            "acceptable": 0.6,
            "poor": 0.4
        }
        
        self.logger.info("🎯 Elite Performance Tracker initialized with comprehensive analytics")
    
    async def track_prediction_performance(self, prediction_id: str, prediction_data: Dict[str, Any],
                                         agent_id: Optional[str] = None, system_name: Optional[str] = None) -> bool:
        """Track a new prediction for future performance validation."""
        try:
            # Store prediction for validation
            self.active_predictions[prediction_id] = {
                "prediction_data": prediction_data,
                "agent_id": agent_id,
                "system_name": system_name,
                "timestamp": datetime.now(),
                "market_context": prediction_data.get("market_context", {}),
                "confidence": prediction_data.get("confidence", 0.5),
                "insights": prediction_data.get("insights", []),
                "regime": prediction_data.get("regime", "UNKNOWN")
            }
            
            # Store in database if available
            if DATABASE_AVAILABLE and self.db_integration:
                await self._store_prediction_tracking(prediction_id, prediction_data, agent_id, system_name)
            
            self.logger.debug(f"📊 Tracking prediction {prediction_id} for performance validation")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to track prediction performance: {e}")
            return False
    
    async def validate_prediction_performance(self, prediction_id: str, actual_outcome: Dict[str, Any]) -> ValidationResult:
        """Validate a prediction against actual market outcomes."""
        try:
            if prediction_id not in self.active_predictions:
                raise ValueError(f"Prediction {prediction_id} not found in active tracking")
            
            prediction_data = self.active_predictions[prediction_id]
            
            # Calculate comprehensive validation metrics
            validation_result = await self._calculate_validation_metrics(prediction_data, actual_outcome)
            validation_result.prediction_id = prediction_id
            
            # Store validation result
            self.validation_results.append(validation_result)
            
            # Update performance scores
            await self._update_performance_scores(validation_result)
            
            # Check for performance alerts
            await self._check_performance_alerts(validation_result)
            
            # Store in database
            if DATABASE_AVAILABLE and self.db_integration:
                await self._store_validation_result(validation_result)
            
            # Remove from active predictions
            del self.active_predictions[prediction_id]
            
            self.logger.info(f"✅ Validated prediction {prediction_id} - Accuracy: {validation_result.accuracy_score:.3f}")
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Failed to validate prediction performance: {e}")
            # Return default validation result
            return ValidationResult(
                prediction_id=prediction_id,
                predicted_outcome={},
                actual_outcome=actual_outcome,
                accuracy_score=0.5,
                confidence_error=0.5,
                timing_accuracy=0.5,
                insight_relevance=0.5
            )
    
    async def _calculate_validation_metrics(self, prediction_data: Dict[str, Any], 
                                          actual_outcome: Dict[str, Any]) -> ValidationResult:
        """Calculate comprehensive validation metrics."""
        try:
            # Accuracy Score (40% weight)
            accuracy_score = self._calculate_prediction_accuracy(prediction_data, actual_outcome)
            
            # Confidence Calibration Error (25% weight)
            confidence_error = self._calculate_confidence_error(prediction_data, actual_outcome)
            
            # Timing Accuracy (20% weight)
            timing_accuracy = self._calculate_timing_accuracy(prediction_data, actual_outcome)
            
            # Insight Relevance (15% weight)
            insight_relevance = self._calculate_insight_relevance(prediction_data, actual_outcome)
            
            return ValidationResult(
                prediction_id="",  # Will be set by caller
                predicted_outcome=prediction_data,
                actual_outcome=actual_outcome,
                accuracy_score=accuracy_score,
                confidence_error=confidence_error,
                timing_accuracy=timing_accuracy,
                insight_relevance=insight_relevance,
                market_conditions=actual_outcome.get("market_conditions", {})
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating validation metrics: {e}")
            return ValidationResult(
                prediction_id="",
                predicted_outcome=prediction_data,
                actual_outcome=actual_outcome,
                accuracy_score=0.5,
                confidence_error=0.5,
                timing_accuracy=0.5,
                insight_relevance=0.5
            )
    
    def _calculate_prediction_accuracy(self, prediction_data: Dict[str, Any], 
                                     actual_outcome: Dict[str, Any]) -> float:
        """Calculate prediction accuracy score."""
        try:
            accuracy_factors = []
            
            # Regime prediction accuracy
            predicted_regime = prediction_data.get("regime", "UNKNOWN")
            actual_regime = actual_outcome.get("regime", "UNKNOWN")
            
            if predicted_regime == actual_regime:
                accuracy_factors.append(1.0)
            else:
                # Partial credit for similar regimes
                similarity = self._calculate_regime_similarity(predicted_regime, actual_regime)
                accuracy_factors.append(similarity)
            
            # Direction accuracy (if available)
            predicted_direction = prediction_data.get("direction")
            actual_direction = actual_outcome.get("direction")
            
            if predicted_direction and actual_direction:
                if predicted_direction == actual_direction:
                    accuracy_factors.append(1.0)
                else:
                    accuracy_factors.append(0.0)
            
            # Magnitude accuracy (if available)
            predicted_magnitude = prediction_data.get("magnitude")
            actual_magnitude = actual_outcome.get("magnitude")
            
            if predicted_magnitude and actual_magnitude:
                magnitude_error = abs(predicted_magnitude - actual_magnitude) / max(abs(actual_magnitude), 1.0)
                magnitude_accuracy = max(0.0, 1.0 - magnitude_error)
                accuracy_factors.append(magnitude_accuracy)
            
            return statistics.mean(accuracy_factors) if accuracy_factors else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating prediction accuracy: {e}")
            return 0.5
    
    def _calculate_confidence_error(self, prediction_data: Dict[str, Any], 
                                  actual_outcome: Dict[str, Any]) -> float:
        """Calculate confidence calibration error."""
        try:
            predicted_confidence = prediction_data.get("confidence", 0.5)
            actual_success = actual_outcome.get("success_rate", 0.5)
            
            # Calculate absolute calibration error
            calibration_error = abs(predicted_confidence - actual_success)
            
            # Penalize overconfidence more than underconfidence
            if predicted_confidence > actual_success:
                calibration_error *= 1.2
            
            return min(calibration_error, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence error: {e}")
            return 0.5
    
    def _calculate_timing_accuracy(self, prediction_data: Dict[str, Any], 
                                 actual_outcome: Dict[str, Any]) -> float:
        """Calculate timing accuracy of predictions."""
        try:
            prediction_time = prediction_data.get("timestamp", datetime.now())
            actual_event_time = actual_outcome.get("event_timestamp")
            
            if not actual_event_time:
                return 0.7  # Neutral score if no timing data
            
            if isinstance(actual_event_time, str):
                actual_event_time = datetime.fromisoformat(actual_event_time.replace('Z', '+00:00'))
            
            time_diff = abs((actual_event_time - prediction_time).total_seconds())
            
            # Score based on timing accuracy
            if time_diff <= 3600:  # Within 1 hour
                return 1.0
            elif time_diff <= 14400:  # Within 4 hours
                return 0.8
            elif time_diff <= 86400:  # Within 1 day
                return 0.6
            else:
                return 0.3
                
        except Exception as e:
            self.logger.error(f"Error calculating timing accuracy: {e}")
            return 0.5
    
    def _calculate_insight_relevance(self, prediction_data: Dict[str, Any], 
                                   actual_outcome: Dict[str, Any]) -> float:
        """Calculate relevance of generated insights."""
        try:
            insights = prediction_data.get("insights", [])
            actual_events = actual_outcome.get("market_events", [])
            
            if not insights or not actual_events:
                return 0.5
            
            # Simple keyword matching (can be enhanced with NLP)
            relevance_scores = []
            for insight in insights:
                insight_lower = str(insight).lower()
                matches = 0
                for event in actual_events:
                    event_lower = str(event).lower()
                    # Check for keyword matches
                    if any(word in event_lower for word in insight_lower.split() if len(word) > 3):
                        matches += 1
                
                insight_relevance = min(matches / len(actual_events), 1.0) if actual_events else 0.5
                relevance_scores.append(insight_relevance)
            
            return statistics.mean(relevance_scores) if relevance_scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating insight relevance: {e}")
            return 0.5
    
    def _calculate_regime_similarity(self, predicted: str, actual: str) -> float:
        """Calculate similarity between predicted and actual regimes."""
        if predicted == actual:
            return 1.0
        
        # Define regime similarity matrix
        regime_similarities = {
            ('BULLISH_MOMENTUM', 'BULLISH_CONSOLIDATION'): 0.7,
            ('BEARISH_MOMENTUM', 'BEARISH_CONSOLIDATION'): 0.7,
            ('HIGH_VOLATILITY', 'VOLATILITY_EXPANSION'): 0.8,
            ('CONSOLIDATION', 'RANGE_BOUND'): 0.9,
            ('BULLISH_MOMENTUM', 'CONSOLIDATION'): 0.4,
            ('BEARISH_MOMENTUM', 'CONSOLIDATION'): 0.4,
        }
        
        # Check both directions
        similarity = regime_similarities.get((predicted, actual), 
                                           regime_similarities.get((actual, predicted), 0.2))
        return similarity

    async def _update_performance_scores(self, validation_result: ValidationResult):
        """Update performance scores based on validation results."""
        try:
            timestamp = datetime.now()

            # Create performance scores for different metrics
            performance_scores = [
                PerformanceScore(
                    metric_type=PerformanceMetricType.ACCURACY,
                    score=validation_result.accuracy_score,
                    timestamp=timestamp,
                    validation_data=validation_result.model_dump()
                ),
                PerformanceScore(
                    metric_type=PerformanceMetricType.CONFIDENCE_CALIBRATION,
                    score=1.0 - validation_result.confidence_error,
                    timestamp=timestamp,
                    validation_data=validation_result.model_dump()
                ),
                PerformanceScore(
                    metric_type=PerformanceMetricType.PREDICTION_TIMING,
                    score=validation_result.timing_accuracy,
                    timestamp=timestamp,
                    validation_data=validation_result.model_dump()
                ),
                PerformanceScore(
                    metric_type=PerformanceMetricType.INSIGHT_QUALITY,
                    score=validation_result.insight_relevance,
                    timestamp=timestamp,
                    validation_data=validation_result.model_dump()
                )
            ]

            # Add to performance history
            self.performance_history.extend(performance_scores)

            self.logger.debug(f"📊 Updated performance scores for prediction {validation_result.prediction_id}")

        except Exception as e:
            self.logger.error(f"Error updating performance scores: {e}")

    async def _check_performance_alerts(self, validation_result: ValidationResult):
        """Check for performance alerts based on validation results."""
        try:
            alerts = []

            # Check accuracy alert
            if validation_result.accuracy_score < self.alert_thresholds[PerformanceMetricType.ACCURACY]:
                alerts.append({
                    "type": "low_accuracy",
                    "message": f"Low prediction accuracy: {validation_result.accuracy_score:.3f}",
                    "severity": "warning",
                    "timestamp": datetime.now(),
                    "prediction_id": validation_result.prediction_id
                })

            # Check confidence calibration alert
            confidence_score = 1.0 - validation_result.confidence_error
            if confidence_score < self.alert_thresholds[PerformanceMetricType.CONFIDENCE_CALIBRATION]:
                alerts.append({
                    "type": "poor_confidence_calibration",
                    "message": f"Poor confidence calibration: {confidence_score:.3f}",
                    "severity": "warning",
                    "timestamp": datetime.now(),
                    "prediction_id": validation_result.prediction_id
                })

            # Add alerts to queue
            self.performance_alerts.extend(alerts)

            # Limit alert queue size
            if len(self.performance_alerts) > 100:
                self.performance_alerts = self.performance_alerts[-100:]

            if alerts:
                self.logger.warning(f"⚠️ Generated {len(alerts)} performance alerts for prediction {validation_result.prediction_id}")

        except Exception as e:
            self.logger.error(f"Error checking performance alerts: {e}")

    async def generate_performance_report(self, days_back: int = 30) -> SystemPerformanceReport:
        """Generate comprehensive performance report."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)

            # Filter recent performance data
            recent_scores = [score for score in self.performance_history
                           if score.timestamp >= cutoff_date]
            recent_validations = [val for val in self.validation_results
                                if val.validation_timestamp >= cutoff_date]

            if not recent_scores:
                return SystemPerformanceReport(
                    overall_performance_score=0.5,
                    improvement_recommendations=["Insufficient data for analysis"]
                )

            # Calculate individual metric scores
            individual_scores = {}
            for metric_type in PerformanceMetricType:
                metric_scores = [score.score for score in recent_scores
                               if score.metric_type == metric_type]
                if metric_scores:
                    individual_scores[metric_type] = statistics.mean(metric_scores)

            # Calculate overall performance score
            overall_score = statistics.mean(individual_scores.values()) if individual_scores else 0.5

            # Generate performance trends
            performance_trends = await self._analyze_performance_trends(recent_scores, days_back)

            # Identify top performing agents
            top_agents = await self._identify_top_performing_agents(recent_validations)

            # Generate improvement recommendations
            recommendations = await self._generate_improvement_recommendations(
                individual_scores, performance_trends, recent_validations
            )

            # Check alert conditions
            alert_conditions = await self._analyze_alert_conditions(individual_scores)

            # Create validation summary
            validation_summary = await self._create_validation_summary(recent_validations)

            return SystemPerformanceReport(
                overall_performance_score=overall_score,
                individual_scores=individual_scores,
                performance_trends=performance_trends,
                top_performing_agents=top_agents,
                improvement_recommendations=recommendations,
                alert_conditions=alert_conditions,
                validation_summary=validation_summary
            )

        except Exception as e:
            self.logger.error(f"Error generating performance report: {e}")
            return SystemPerformanceReport(
                overall_performance_score=0.5,
                improvement_recommendations=[f"Report generation failed: {str(e)}"]
            )

    async def _analyze_performance_trends(self, recent_scores: List[PerformanceScore],
                                        days_back: int) -> List[PerformanceTrend]:
        """Analyze performance trends for each metric type."""
        try:
            trends = []

            for metric_type in PerformanceMetricType:
                metric_scores = [(score.timestamp, score.score) for score in recent_scores
                               if score.metric_type == metric_type]

                if len(metric_scores) < 3:
                    continue

                # Sort by timestamp
                metric_scores.sort(key=lambda x: x[0])

                # Calculate trend
                scores = [score for _, score in metric_scores]
                current_score = scores[-1]

                # Simple linear trend calculation
                x = list(range(len(scores)))
                if len(scores) > 1:
                    slope = np.polyfit(x, scores, 1)[0] if len(scores) > 1 else 0

                    # Determine trend direction
                    if slope > 0.01:
                        trend_direction = "improving"
                        trend_strength = min(abs(slope) * 10, 1.0)
                    elif slope < -0.01:
                        trend_direction = "declining"
                        trend_strength = min(abs(slope) * 10, 1.0)
                    else:
                        trend_direction = "stable"
                        trend_strength = 0.5

                    # Project future score
                    projected_score = max(0.0, min(1.0, current_score + slope * 7))  # 7 days ahead

                    # Calculate confidence interval (simplified)
                    score_std = statistics.stdev(scores) if len(scores) > 1 else 0.1
                    confidence_interval = (
                        max(0.0, projected_score - 1.96 * score_std),
                        min(1.0, projected_score + 1.96 * score_std)
                    )

                    trend = PerformanceTrend(
                        metric_type=metric_type,
                        trend_direction=trend_direction,
                        trend_strength=trend_strength,
                        current_score=current_score,
                        projected_score=projected_score,
                        confidence_interval=confidence_interval,
                        analysis_period_days=days_back
                    )

                    trends.append(trend)

            return trends

        except Exception as e:
            self.logger.error(f"Error analyzing performance trends: {e}")
            return []

    async def _identify_top_performing_agents(self, recent_validations: List[ValidationResult]) -> List[Dict[str, Any]]:
        """Identify top performing agents based on recent validations."""
        try:
            # Group validations by agent (if available)
            agent_performance = defaultdict(list)

            for validation in recent_validations:
                agent_id = validation.predicted_outcome.get("agent_id", "unknown")
                agent_performance[agent_id].append(validation.accuracy_score)

            # Calculate average performance for each agent
            agent_scores = {}
            for agent_id, scores in agent_performance.items():
                if len(scores) >= 3:  # Minimum 3 predictions for ranking
                    agent_scores[agent_id] = {
                        "average_accuracy": statistics.mean(scores),
                        "prediction_count": len(scores),
                        "consistency": 1.0 - statistics.stdev(scores) if len(scores) > 1 else 1.0
                    }

            # Sort by performance
            top_agents = sorted(agent_scores.items(),
                              key=lambda x: x[1]["average_accuracy"],
                              reverse=True)[:5]

            return [{"agent_id": agent_id, **metrics} for agent_id, metrics in top_agents]

        except Exception as e:
            self.logger.error(f"Error identifying top performing agents: {e}")
            return []

    async def _generate_improvement_recommendations(self, individual_scores: Dict[PerformanceMetricType, float],
                                                  performance_trends: List[PerformanceTrend],
                                                  recent_validations: List[ValidationResult]) -> List[str]:
        """Generate improvement recommendations based on performance analysis."""
        try:
            recommendations = []

            # Check individual metric scores
            for metric_type, score in individual_scores.items():
                if score < self.performance_benchmarks["acceptable"]:
                    if metric_type == PerformanceMetricType.ACCURACY:
                        recommendations.append("🎯 Improve prediction accuracy through enhanced training data")
                    elif metric_type == PerformanceMetricType.CONFIDENCE_CALIBRATION:
                        recommendations.append("⚖️ Recalibrate confidence scoring mechanisms")
                    elif metric_type == PerformanceMetricType.INSIGHT_QUALITY:
                        recommendations.append("💡 Enhance insight generation algorithms")
                    elif metric_type == PerformanceMetricType.PREDICTION_TIMING:
                        recommendations.append("⏰ Optimize prediction timing algorithms")

            # Check performance trends
            declining_trends = [trend for trend in performance_trends
                               if trend.trend_direction == "declining"]

            if declining_trends:
                recommendations.append(f"📉 Address declining performance in {len(declining_trends)} metrics")

            # Check validation patterns
            if recent_validations:
                avg_accuracy = statistics.mean([v.accuracy_score for v in recent_validations])
                if avg_accuracy < self.performance_benchmarks["good"]:
                    recommendations.append("🔄 Implement additional training cycles for accuracy improvement")

            # General recommendations
            if len(individual_scores) < 4:
                recommendations.append("📊 Expand performance tracking to cover more metrics")

            return recommendations[:10]  # Limit to top 10 recommendations

        except Exception as e:
            self.logger.error(f"Error generating improvement recommendations: {e}")
            return ["⚠️ Unable to generate recommendations due to analysis error"]

    async def _analyze_alert_conditions(self, individual_scores: Dict[PerformanceMetricType, float]) -> List[str]:
        """Analyze current alert conditions."""
        try:
            alert_conditions = []

            for metric_type, score in individual_scores.items():
                threshold = self.alert_thresholds.get(metric_type, 0.5)

                if score < threshold:
                    severity = "🔴 Critical" if score < threshold * 0.7 else "🟡 Warning"
                    alert_conditions.append(f"{severity}: {metric_type.value} below threshold ({score:.3f} < {threshold:.3f})")

            # Check recent alerts
            recent_alerts = [alert for alert in self.performance_alerts
                           if (datetime.now() - alert["timestamp"]).total_seconds() < 3600]  # Last hour

            if len(recent_alerts) > 5:
                alert_conditions.append(f"🚨 High alert frequency: {len(recent_alerts)} alerts in last hour")

            return alert_conditions

        except Exception as e:
            self.logger.error(f"Error analyzing alert conditions: {e}")
            return ["⚠️ Alert analysis failed"]

    async def _create_validation_summary(self, recent_validations: List[ValidationResult]) -> Dict[str, Any]:
        """Create summary of recent validations."""
        try:
            if not recent_validations:
                return {"status": "no_data", "message": "No recent validations available"}

            # Calculate summary statistics
            accuracy_scores = [v.accuracy_score for v in recent_validations]
            confidence_errors = [v.confidence_error for v in recent_validations]
            timing_scores = [v.timing_accuracy for v in recent_validations]
            insight_scores = [v.insight_relevance for v in recent_validations]

            summary = {
                "total_validations": len(recent_validations),
                "average_accuracy": statistics.mean(accuracy_scores),
                "accuracy_std": statistics.stdev(accuracy_scores) if len(accuracy_scores) > 1 else 0,
                "average_confidence_error": statistics.mean(confidence_errors),
                "average_timing_accuracy": statistics.mean(timing_scores),
                "average_insight_relevance": statistics.mean(insight_scores),
                "high_accuracy_rate": len([s for s in accuracy_scores if s > 0.8]) / len(accuracy_scores),
                "low_accuracy_rate": len([s for s in accuracy_scores if s < 0.6]) / len(accuracy_scores),
                "validation_period": {
                    "start": min(v.validation_timestamp for v in recent_validations).isoformat(),
                    "end": max(v.validation_timestamp for v in recent_validations).isoformat()
                }
            }

            return summary

        except Exception as e:
            self.logger.error(f"Error creating validation summary: {e}")
            return {"status": "error", "message": str(e)}

    # ===== DATABASE INTEGRATION METHODS =====

    async def _store_prediction_tracking(self, prediction_id: str, prediction_data: Dict[str, Any],
                                        agent_id: Optional[str], system_name: Optional[str]):
        """Store prediction tracking data in database."""
        try:
            if self.db_integration:
                # This would store prediction tracking data in Supabase
                self.logger.debug(f"🗄️ Storing prediction tracking for {prediction_id}")
        except Exception as e:
            self.logger.debug(f"Could not store prediction tracking: {e}")

    async def _store_validation_result(self, validation_result: ValidationResult):
        """Store validation result in database."""
        try:
            if self.db_integration:
                # This would store validation results in Supabase
                self.logger.debug(f"🗄️ Storing validation result for {validation_result.prediction_id}")
        except Exception as e:
            self.logger.debug(f"Could not store validation result: {e}")

    # ===== PUBLIC INTERFACE METHODS =====

    async def get_real_time_performance_metrics(self) -> Dict[str, Any]:
        """Get real-time performance metrics."""
        try:
            # Get recent performance scores (last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_scores = [score for score in self.performance_history
                           if score.timestamp >= cutoff_time]

            if not recent_scores:
                return {"status": "no_recent_data", "message": "No performance data in last 24 hours"}

            # Calculate real-time metrics
            metrics = {}
            for metric_type in PerformanceMetricType:
                metric_scores = [score.score for score in recent_scores
                               if score.metric_type == metric_type]
                if metric_scores:
                    metrics[metric_type.value] = {
                        "current_score": metric_scores[-1],
                        "average_score": statistics.mean(metric_scores),
                        "trend": "improving" if len(metric_scores) > 1 and metric_scores[-1] > metric_scores[0] else "stable",
                        "sample_count": len(metric_scores)
                    }

            return {
                "status": "active",
                "timestamp": datetime.now().isoformat(),
                "metrics": metrics,
                "active_predictions": len(self.active_predictions),
                "recent_validations": len([v for v in self.validation_results
                                         if (datetime.now() - v.validation_timestamp).total_seconds() < 86400])
            }

        except Exception as e:
            self.logger.error(f"Error getting real-time performance metrics: {e}")
            return {"status": "error", "message": str(e)}

    async def get_performance_alerts(self, hours_back: int = 24) -> List[Dict[str, Any]]:
        """Get recent performance alerts."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            recent_alerts = [alert for alert in self.performance_alerts
                           if alert["timestamp"] >= cutoff_time]

            # Sort by timestamp (most recent first)
            recent_alerts.sort(key=lambda x: x["timestamp"], reverse=True)

            return recent_alerts

        except Exception as e:
            self.logger.error(f"Error getting performance alerts: {e}")
            return []

    async def get_agent_performance_ranking(self, days_back: int = 30) -> List[Dict[str, Any]]:
        """Get performance ranking of AI agents."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)
            recent_validations = [val for val in self.validation_results
                                if val.validation_timestamp >= cutoff_date]

            return await self._identify_top_performing_agents(recent_validations)

        except Exception as e:
            self.logger.error(f"Error getting agent performance ranking: {e}")
            return []

    async def optimize_performance_thresholds(self) -> Dict[str, Any]:
        """Optimize performance alert thresholds based on historical data."""
        try:
            optimization_results = {}

            for metric_type in PerformanceMetricType:
                if metric_type in self.alert_thresholds:
                    # Get historical scores for this metric
                    metric_scores = [score.score for score in self.performance_history
                                   if score.metric_type == metric_type]

                    if len(metric_scores) >= 10:
                        # Calculate optimal threshold (e.g., 25th percentile)
                        optimal_threshold = np.percentile(metric_scores, 25)
                        current_threshold = self.alert_thresholds[metric_type]

                        # Update threshold if significantly different
                        if abs(optimal_threshold - current_threshold) > 0.05:
                            self.alert_thresholds[metric_type] = optimal_threshold
                            optimization_results[metric_type.value] = {
                                "old_threshold": current_threshold,
                                "new_threshold": optimal_threshold,
                                "improvement": abs(optimal_threshold - current_threshold)
                            }

            return {
                "status": "completed",
                "optimized_thresholds": len(optimization_results),
                "changes": optimization_results,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error optimizing performance thresholds: {e}")
            return {"status": "error", "message": str(e)}

    async def export_performance_data(self, days_back: int = 30, format: str = "json") -> Dict[str, Any]:
        """Export performance data for external analysis."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)

            # Filter data
            recent_scores = [score for score in self.performance_history
                           if score.timestamp >= cutoff_date]
            recent_validations = [val for val in self.validation_results
                                if val.validation_timestamp >= cutoff_date]

            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "period_days": days_back,
                "performance_scores": [score.model_dump() for score in recent_scores],
                "validation_results": [val.model_dump() for val in recent_validations],
                "alert_thresholds": {k.value: v for k, v in self.alert_thresholds.items()},
                "performance_benchmarks": self.performance_benchmarks,
                "summary": {
                    "total_scores": len(recent_scores),
                    "total_validations": len(recent_validations),
                    "unique_metrics": len(set(score.metric_type for score in recent_scores))
                }
            }

            return {
                "status": "success",
                "format": format,
                "data": export_data,
                "size": len(str(export_data))
            }

        except Exception as e:
            self.logger.error(f"Error exporting performance data: {e}")
            return {"status": "error", "message": str(e)}


# ===== GLOBAL PERFORMANCE TRACKER INSTANCE =====

_global_performance_tracker: Optional[ElitePerformanceTracker] = None

async def get_performance_tracker() -> ElitePerformanceTracker:
    """Get or create the global performance tracker instance."""
    global _global_performance_tracker

    if _global_performance_tracker is None:
        # Initialize database integration if available
        db_integration = None
        if DATABASE_AVAILABLE:
            try:
                db_integration = await get_ai_database_integration()
            except Exception as e:
                logger.warning(f"Could not initialize database integration: {e}")

        _global_performance_tracker = ElitePerformanceTracker(db_integration)
        logger.info("🎯 Global Elite Performance Tracker initialized")

    return _global_performance_tracker

async def track_ai_prediction(prediction_id: str, prediction_data: Dict[str, Any],
                             agent_id: Optional[str] = None, system_name: Optional[str] = None) -> bool:
    """Track an AI prediction for performance validation."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.track_prediction_performance(prediction_id, prediction_data, agent_id, system_name)
    except Exception as e:
        logger.error(f"Error tracking AI prediction: {e}")
        return False

async def validate_ai_prediction(prediction_id: str, actual_outcome: Dict[str, Any]) -> ValidationResult:
    """Validate an AI prediction against actual market outcomes."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.validate_prediction_performance(prediction_id, actual_outcome)
    except Exception as e:
        logger.error(f"Error validating AI prediction: {e}")
        return ValidationResult(
            prediction_id=prediction_id,
            predicted_outcome={},
            actual_outcome=actual_outcome,
            accuracy_score=0.5,
            confidence_error=0.5,
            timing_accuracy=0.5,
            insight_relevance=0.5
        )

async def get_performance_report(days_back: int = 30) -> SystemPerformanceReport:
    """Get comprehensive performance report."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.generate_performance_report(days_back)
    except Exception as e:
        logger.error(f"Error getting performance report: {e}")
        return SystemPerformanceReport(
            overall_performance_score=0.5,
            improvement_recommendations=[f"Report generation failed: {str(e)}"]
        )

async def get_real_time_metrics() -> Dict[str, Any]:
    """Get real-time performance metrics."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.get_real_time_performance_metrics()
    except Exception as e:
        logger.error(f"Error getting real-time metrics: {e}")
        return {"status": "error", "message": str(e)}

async def get_performance_alerts(hours_back: int = 24) -> List[Dict[str, Any]]:
    """Get recent performance alerts."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.get_performance_alerts(hours_back)
    except Exception as e:
        logger.error(f"Error getting performance alerts: {e}")
        return []

async def get_agent_rankings(days_back: int = 30) -> List[Dict[str, Any]]:
    """Get AI agent performance rankings."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.get_agent_performance_ranking(days_back)
    except Exception as e:
        logger.error(f"Error getting agent rankings: {e}")
        return []

async def optimize_thresholds() -> Dict[str, Any]:
    """Optimize performance thresholds based on historical data."""
    try:
        tracker = await get_performance_tracker()
        return await tracker.optimize_performance_thresholds()
    except Exception as e:
        logger.error(f"Error optimizing thresholds: {e}")
        return {"status": "error", "message": str(e)}
