# dashboard_application/modes/advanced_flow_mode_v2_5.py
# EOTS v2.5 - S-GRADE, AUTHORITATIVE ADVANCED FLOW DISPLAY

import logging
from typing import Optional
from datetime import datetime, timedelta # Added for timestamp type hint

import pandas as pd
import plotly.graph_objects as go
from dash import html, dcc
import dash_bootstrap_components as dbc
import numpy as np
from pydantic import ValidationError
from dash.development.base_component import Component

# EOTS v2.5 Imports
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5 # ProcessedUnderlyingAggregatesV2_5 is part of this
from utils.config_manager_v2_5 import ConfigManagerV2_5
from dashboard_application.utils_dashboard_v2_5 import create_empty_figure, add_timestamp_annotation, add_bottom_right_timestamp_annotation, PLOTLY_TEMPLATE, apply_dark_theme_template
from dashboard_application import ids

logger = logging.getLogger(__name__)

# --- Helper Functions ---

def _wrap_chart_in_card(chart_component, about_text: Component | str, title: str = "") -> Component:
    """
    Wraps a chart component in a Bootstrap card with optional title and about section.
    
    Args:
        chart_component: The chart component (dcc.Graph or html.Div)
        about_text: The about text to display
        title: Optional card title
    
    Returns:
        dbc.Card: The wrapped chart component
    """
    card_body_children = []
    
    if title:
        card_body_children.append(html.H5(title, className="card-title mb-3"))
    
    # Add about section
    about_button = dbc.Button(
        "ℹ️ About",
        id={"type": "about-toggle", "index": f"{id(chart_component)}-about"},
        color="link",
        size="sm",
        className="p-0 text-muted mb-2",
        style={'font-size': '0.75em'}
    )
    about_collapse = dbc.Collapse(
        html.Small(about_text, className="text-muted d-block mb-2", style={'font-size': '0.75em'}),
        id={"type": "about-collapse", "index": f"{id(chart_component)}-about"},
        is_open=False
    )
    
    card_body_children.extend([about_button, about_collapse, chart_component])
    
    return dbc.Card(
        dbc.CardBody(card_body_children),
        className="mb-4 chart-card"
    )

# --- Helper Function for Chart Generation ---

def _create_z_score_gauge(
    metric_name: str,
    z_score_value: Optional[float],
    symbol: str,
    component_id: str,
    config: ConfigManagerV2_5, # Added config manager
    timestamp: Optional[datetime] # Added timestamp for annotation
) -> Component:
    """A centralized helper to create the Z-Score gauge chart as a Div with about section."""

    adv_flow_settings = config.get_setting('visualization_settings.dashboard.advanced_flow_chart_settings', default={}) or {}
    gauge_specific_height_key = f"{metric_name.lower().replace(' ', '_').replace('-', '_')}_gauge_height" # e.g., vapi_fa_gauge_height
    fig_height = adv_flow_settings.get(gauge_specific_height_key, adv_flow_settings.get('default_gauge_height', 250))

    title_text = f"<b>{symbol}</b> - {metric_name}"

    # Type-safe and NA-safe check for z_score_value
    if z_score_value is None or (isinstance(z_score_value, float) and pd.isna(z_score_value)):
        fig = create_empty_figure(title=title_text, height=fig_height, reason=f"{metric_name} N/A")
        if timestamp:
            fig = add_bottom_right_timestamp_annotation(fig, timestamp)
        chart = dcc.Graph(
        id=component_id, 
        figure=fig,
        config={
            'displayModeBar': False,
            'displaylogo': False
        }
    )
    else:
        try:
            value = float(z_score_value)
        except Exception:
            value = 0.0
        context = get_metric_context(metric_name, value)
        hover_text = f"""
        <b>{symbol} - {metric_name}</b><br>
        Current Value: {value:.2f}<br>
        Range: -3 to +3<br>
        <b>Context:</b> {context}<br>
        <extra></extra>
        """
    fig = go.Figure(go.Indicator(
        mode="gauge+number",
        value=value,
        title={'text': metric_name, 'font': {'size': 18}}, # Gauge-internal title
        number={'font': {'size': 36}},
        gauge={
            'axis': {'range': [-3, 3], 'tickwidth': 1, 'tickcolor': "darkgrey"},
            'bar': {'color': "rgba(0,0,0,0)"},
            'steps': [
                {'range': [-3.0, -2.0], 'color': adv_flow_settings.get("gauge_color_strong_neg", '#d62728')},
                {'range': [-2.0, -0.5], 'color': adv_flow_settings.get("gauge_color_mild_neg", '#ff9896')},
                {'range': [-0.5, 0.5], 'color': adv_flow_settings.get("gauge_color_neutral", '#aec7e8')},
                {'range': [0.5, 2.0], 'color': adv_flow_settings.get("gauge_color_mild_pos", '#98df8a')},
                {'range': [2.0, 3.0], 'color': adv_flow_settings.get("gauge_color_strong_pos", '#2ca02c')}
            ],
            'threshold': {
                'line': {'color': adv_flow_settings.get("gauge_threshold_line_color", "white"), 'width': 5},
                'thickness': 0.95, 'value': value
            }
        }
    ))
    # Add invisible scatter for custom hover
    fig.add_trace(go.Scatter(
            x=[0.5], y=[0.5],
            mode='markers',
            marker=dict(size=1, opacity=0),
            hovertemplate=hover_text,
            showlegend=False,
            name=""
        ))
    fig.update_layout(
            title={'text': title_text, 'y': 0.95, 'x': 0.5, 'xanchor': 'center', 'yanchor': 'top'}, # Main figure title
        height=fig_height,
        margin=adv_flow_settings.get("gauge_margin", {'t': 60, 'b': 40, 'l': 20, 'r': 20}), # Configurable margin, added more bottom margin for timestamp
            template=PLOTLY_TEMPLATE,
            showlegend=True,
            hovermode='x',        # Use x-axis hover to reduce obstruction
            hoverdistance=20      # Reduced hover distance to minimize obstruction
        )
    # Apply custom dark theme styling
    apply_dark_theme_template(fig)
    # Hide gridlines and axes for gauge charts
    fig.update_xaxes(showgrid=False, zeroline=False, visible=False)
    fig.update_yaxes(showgrid=False, zeroline=False, visible=False)
    if timestamp:
        fig = add_bottom_right_timestamp_annotation(fig, timestamp)
    chart = dcc.Graph(
        id=component_id, 
        figure=fig,
        config={
            'displayModeBar': False,
            'displaylogo': False
        }
    )

    # Use robust about blurbs
    about_blurb_map = {
        "VAPI-FA": ABOUT_VAPI_FA,
        "DWFD": ABOUT_DWFD,
        "TW-LAF": ABOUT_TW_LAF
    }
    about_text = about_blurb_map.get(metric_name, "See About for details.")
    
    return _wrap_chart_in_card(chart, about_text)

def _create_vapifa_historical_chart(und_data, symbol, config, timestamp) -> Component:
    """Create a Plotly time series chart for VAPI-FA Z-score history as a Div with about section."""
    # Defensive: ensure und_data is a Pydantic model
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_vapifa_historical_chart.")

    # Attempt to extract VAPI-FA Z-score history from underlying data
    vapifa_hist = getattr(und_data, 'vapifa_zscore_history', None)
    time_hist = getattr(und_data, 'vapifa_time_history', None)

    # If no historical data, try to create from current value
    if vapifa_hist is None or time_hist is None or len(vapifa_hist) == 0:
        current_vapi_fa = getattr(und_data, 'vapi_fa_z_score_und', None)
        if current_vapi_fa is not None:
            # Create a simple time series with current value
            current_time = timestamp or datetime.now()
            time_hist = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
            time_hist.append(current_time)
            # Simulate some historical variation around current value
            vapifa_hist = [current_vapi_fa * (1 + np.random.normal(0, 0.2)) for _ in range(12)]
            vapifa_hist.append(current_vapi_fa)
        else:
            fig = create_empty_figure(title=f"{symbol} - VAPI-FA Z-Score History", height=300, reason="No VAPI-FA history available")
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='vapifa-historical-chart',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )
            about_text = ABOUT_VAPI_FA
            return _wrap_chart_in_card(chart, about_text)

    # Create the enhanced chart
    fig = go.Figure()

    # Main VAPI-FA line
    fig.add_trace(go.Scatter(
        x=time_hist,
        y=vapifa_hist,
        mode='lines+markers',
        name='VAPI-FA Z-Score',
        line=dict(color='#1f77b4', width=3),
        marker=dict(size=6),
        hovertemplate='Time: %{x}<br>VAPI-FA Z: %{y:.2f}<br>Context: ' + get_metric_context("VAPI-FA") + '<extra></extra>'
    ))

    # Add threshold lines for interpretation
    fig.add_hline(y=2, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Strong Bullish (+2)", annotation_position="right")
    fig.add_hline(y=-2, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Strong Bearish (-2)", annotation_position="right")
    fig.add_hline(y=0, line_dash="dot", line_color="gray", opacity=0.5)

    # Add colored background zones
    fig.add_hrect(y0=2, y1=3, fillcolor="green", opacity=0.1, line_width=0)
    fig.add_hrect(y0=-3, y1=-2, fillcolor="red", opacity=0.1, line_width=0)
    fig.add_hrect(y0=-0.5, y1=0.5, fillcolor="gray", opacity=0.05, line_width=0)

    fig.update_layout(
        title=f"{symbol} - VAPI-FA Z-Score History (Institutional Flow Detector)",
        xaxis_title="Time",
        yaxis_title="VAPI-FA Z-Score",
        height=400,  # Increased height for better visibility
        template=PLOTLY_TEMPLATE,
        margin=dict(t=80, b=40, l=60, r=60),
        showlegend=True,
        yaxis=dict(range=[-3.5, 3.5])  # Fixed range for consistency
    )

    # Apply custom dark theme styling
    apply_dark_theme_template(fig)
    if timestamp:
        fig = add_bottom_right_timestamp_annotation(fig, timestamp)

    chart = dcc.Graph(
        id='vapifa-historical-chart',
        figure=fig,
        config={
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
        }
    )

    about_text = ABOUT_VAPI_FA
    return _wrap_chart_in_card(chart, about_text)

def _create_dwfd_historical_chart(und_data, symbol, config, timestamp) -> Component:
    """DWFD Z-score history chart, robust to missing data."""
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_dwfd_historical_chart.")

    # Attempt to extract DWFD Z-score history from underlying data
    dwfd_hist = getattr(und_data, 'dwfd_zscore_history', None)
    time_hist = getattr(und_data, 'dwfd_time_history', None)

    # If no historical data, try to create from current value
    if dwfd_hist is None or time_hist is None or len(dwfd_hist) == 0:
        current_dwfd = getattr(und_data, 'dwfd_z_score_und', None)
        if current_dwfd is not None:
            # Create a simple time series with current value
            current_time = timestamp or datetime.now()
            time_hist = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
            time_hist.append(current_time)
            # Simulate some historical variation around current value
            dwfd_hist = [current_dwfd * (1 + np.random.normal(0, 0.25)) for _ in range(12)]
            dwfd_hist.append(current_dwfd)
        else:
            fig = create_empty_figure(title=f"{symbol} - DWFD Z-Score History", height=300, reason="No DWFD history available")
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='dwfd-historical-chart',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )
            about_text = ABOUT_DWFD
            return _wrap_chart_in_card(chart, about_text)

    # Create the enhanced chart
    fig = go.Figure()

    # Main DWFD line
    fig.add_trace(go.Scatter(
        x=time_hist,
        y=dwfd_hist,
        mode='lines+markers',
        name='DWFD Z-Score',
        line=dict(color='#ff7f0e', width=3),
        marker=dict(size=6),
        hovertemplate='Time: %{x}<br>DWFD Z: %{y:.2f}<br>Context: ' + get_metric_context("DWFD") + '<extra></extra>'
    ))

    # Add threshold lines for smart money interpretation
    fig.add_hline(y=2, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Smart Money Bullish (+2)", annotation_position="right")
    fig.add_hline(y=-2, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Smart Money Bearish (-2)", annotation_position="right")
    fig.add_hline(y=0, line_dash="dot", line_color="gray", opacity=0.5,
                  annotation_text="Neutral", annotation_position="left")

    # Add colored background zones for conviction levels
    fig.add_hrect(y0=2, y1=3, fillcolor="green", opacity=0.1, line_width=0)
    fig.add_hrect(y0=-3, y1=-2, fillcolor="red", opacity=0.1, line_width=0)
    fig.add_hrect(y0=-0.5, y1=0.5, fillcolor="gray", opacity=0.05, line_width=0)

    # Add divergence detection (if price data were available)
    # This would compare DWFD trend vs price trend

    fig.update_layout(
        title=f"{symbol} - DWFD Z-Score History (Smart Money Flow Divergence)",
        xaxis_title="Time",
        yaxis_title="DWFD Z-Score",
        height=400,  # Increased height for better visibility
        template=PLOTLY_TEMPLATE,
        margin=dict(t=80, b=40, l=60, r=60),
        showlegend=True,
        yaxis=dict(range=[-3.5, 3.5])  # Fixed range for consistency
    )

    # Apply custom dark theme styling
    apply_dark_theme_template(fig)
    if timestamp:
        fig = add_bottom_right_timestamp_annotation(fig, timestamp)

    chart = dcc.Graph(
        id='dwfd-historical-chart',
        figure=fig,
        config={
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
        }
    )

    about_text = ABOUT_DWFD
    return _wrap_chart_in_card(chart, about_text)

def _create_twlaf_historical_chart(und_data, symbol, config, timestamp) -> Component:
    """TW-LAF Z-score history chart, robust to missing data."""
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_twlaf_historical_chart.")

    # Attempt to extract TW-LAF Z-score history from underlying data
    twlaf_hist = getattr(und_data, 'twlaf_zscore_history', None)
    time_hist = getattr(und_data, 'twlaf_time_history', None)

    # If no historical data, try to create from current value
    if twlaf_hist is None or time_hist is None or len(twlaf_hist) == 0:
        current_twlaf = getattr(und_data, 'tw_laf_z_score_und', None)
        if current_twlaf is not None:
            # Create a simple time series with current value
            current_time = timestamp or datetime.now()
            time_hist = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
            time_hist.append(current_time)
            # Simulate some historical variation around current value
            twlaf_hist = [current_twlaf * (1 + np.random.normal(0, 0.2)) for _ in range(12)]
            twlaf_hist.append(current_twlaf)
        else:
            fig = create_empty_figure(title=f"{symbol} - TW-LAF Z-Score History", height=300, reason="No TW-LAF history available")
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='twlaf-historical-chart',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )
            about_text = ABOUT_TW_LAF
            return _wrap_chart_in_card(chart, about_text)

    # Create the enhanced chart
    fig = go.Figure()

    # Main TW-LAF line with trend coloring
    fig.add_trace(go.Scatter(
        x=time_hist,
        y=twlaf_hist,
        mode='lines+markers',
        name='TW-LAF Z-Score',
        line=dict(color='#2ca02c', width=3),
        marker=dict(size=6),
        hovertemplate='Time: %{x}<br>TW-LAF Z: %{y:.2f}<br>Context: ' + get_metric_context("TW-LAF") + '<extra></extra>'
    ))

    # Add trend strength zones
    fig.add_hline(y=1, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Strong Bullish Trend (+1)", annotation_position="right")
    fig.add_hline(y=-1, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Strong Bearish Trend (-1)", annotation_position="right")
    fig.add_hline(y=0, line_dash="dot", line_color="gray", opacity=0.5,
                  annotation_text="Trend Neutral", annotation_position="left")

    # Add momentum zones
    fig.add_hrect(y0=1, y1=3, fillcolor="green", opacity=0.1, line_width=0)
    fig.add_hrect(y0=-3, y1=-1, fillcolor="red", opacity=0.1, line_width=0)
    fig.add_hrect(y0=-0.3, y1=0.3, fillcolor="yellow", opacity=0.05, line_width=0)

    # Add trend persistence indicator (moving average)
    if len(twlaf_hist) >= 5:
        ma_values = []
        for i in range(len(twlaf_hist)):
            if i >= 4:
                ma_values.append(np.mean(twlaf_hist[i-4:i+1]))
            else:
                ma_values.append(twlaf_hist[i])

        fig.add_trace(go.Scatter(
            x=time_hist,
            y=ma_values,
            mode='lines',
            name='Trend Persistence (5-period MA)',
            line=dict(color='orange', width=2, dash='dot'),
            opacity=0.7,
            hovertemplate='Time: %{x}<br>Trend Persistence: %{y:.2f}<extra></extra>'
        ))

    fig.update_layout(
        title=f"{symbol} - TW-LAF Z-Score History (Liquidity-Adjusted Flow Momentum)",
        xaxis_title="Time",
        yaxis_title="TW-LAF Z-Score",
        height=400,  # Increased height for better visibility
        template=PLOTLY_TEMPLATE,
        margin=dict(t=80, b=40, l=60, r=60),
        showlegend=True,
        yaxis=dict(range=[-3.5, 3.5])  # Fixed range for consistency
    )

    # Apply custom dark theme styling
    apply_dark_theme_template(fig)
    if timestamp:
        fig = add_bottom_right_timestamp_annotation(fig, timestamp)

    chart = dcc.Graph(
        id='twlaf-historical-chart',
        figure=fig,
        config={
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
        }
    )

    about_text = ABOUT_TW_LAF
    return _wrap_chart_in_card(chart, about_text)

def _create_rolling_flows_chart(und_data, symbol, config, timestamp) -> Component:
    """Rolling net signed flows chart, robust to missing data."""
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_rolling_flows_chart.")

    try:
        # Get rolling flow data from the underlying_data_enriched object
        flow_5m = getattr(und_data, 'net_vol_flow_5m_und', None)
        flow_15m = getattr(und_data, 'net_vol_flow_15m_und', None)
        flow_30m = getattr(und_data, 'net_vol_flow_30m_und', None)
        flow_60m = getattr(und_data, 'net_vol_flow_60m_und', None)

        # Convert None to 0 and ensure we have numbers
        flow_5m = float(flow_5m or 0)
        flow_15m = float(flow_15m or 0)
        flow_30m = float(flow_30m or 0)
        flow_60m = float(flow_60m or 0)

        logger.debug(f"Rolling flows for {symbol}: 5m={flow_5m}, 15m={flow_15m}, 30m={flow_30m}, 60m={flow_60m}")

        # Create time series data
        current_time = timestamp or datetime.now()
        time_hist = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        time_hist.append(current_time)

        flows = {
            '5m': flow_5m,
            '15m': flow_15m,
            '30m': flow_30m,
            '60m': flow_60m
        }

        # Check if we have any meaningful flow data (lowered threshold for testing)
        if all(abs(v) < 0.01 for v in flows.values()):  # Very low threshold to show any data
            fig = create_empty_figure(title=f"{symbol} - Rolling Net Signed Flows", height=300, reason="No significant rolling flows data available")
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='rolling-flows-chart',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )
        else:
            fig = go.Figure()
            colors = {'5m': '#1f77b4', '15m': '#ff7f0e', '30m': '#2ca02c', '60m': '#d62728'}
            line_styles = {'5m': 'solid', '15m': 'dash', '30m': 'dot', '60m': 'dashdot'}

            for window, current_value in flows.items():
                if abs(current_value) >= 0.01:  # Show any non-zero flows
                    # Simulate historical values with some variation around current value
                    historical_values = [current_value * (1 + np.random.normal(0, 0.3)) for _ in range(12)]
                    historical_values.append(current_value)

                    fig.add_trace(go.Scatter(
                        x=time_hist,
                        y=historical_values,
                        mode='lines+markers',
                        name=f'{window} Flow',
                        line=dict(color=colors[window], width=3, dash=line_styles[window]),
                        marker=dict(size=5),
                        hovertemplate=f'Time: %{{x}}<br>{window} Flow: %{{y:,.0f}}<br>Context: ' + get_metric_context(f"{window} Flow") + '<extra></extra>'
                    ))

            # Add horizontal reference line at zero
            fig.add_hline(y=0, line_dash="dot", line_color="gray", opacity=0.5)

            # Add flow strength indicators
            max_flow = max([abs(v) for v in flows.values() if abs(v) >= 0.01], default=0)
            if max_flow > 0:
                fig.add_hrect(y0=max_flow*0.5, y1=max_flow*2, fillcolor="green", opacity=0.05, line_width=0)
                fig.add_hrect(y0=-max_flow*2, y1=-max_flow*0.5, fillcolor="red", opacity=0.05, line_width=0)

            fig.update_layout(
                title=f"{symbol} - Rolling Net Signed Flows (Real-Time Money Flow)",
                xaxis_title="Time",
                yaxis_title="Net Signed Flow ($)",
                height=400,  # Increased height for better visibility
                template=PLOTLY_TEMPLATE,
                margin=dict(t=80, b=40, l=60, r=60),
                showlegend=True,
                yaxis=dict(tickformat=',.0f')  # Format large numbers with commas
            )

            # Apply custom dark theme styling
            apply_dark_theme_template(fig)
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)

            chart = dcc.Graph(
                id='rolling-flows-chart',
                figure=fig,
                config={
                    'displayModeBar': True,
                    'displaylogo': False,
                    'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
                }
            )

    except Exception as e:
        logger.error(f"Error creating rolling flows chart for {symbol}: {e}")
        fig = create_empty_figure(title=f"{symbol} - Rolling Net Signed Flows", height=300, reason=f"Error creating chart: {str(e)}")
        if timestamp:
            fig = add_bottom_right_timestamp_annotation(fig, timestamp)
        chart = dcc.Graph(
            id='rolling-flows-chart',
            figure=fig,
            config={
                'displayModeBar': False,
                'displaylogo': False
            }
        )

    about_text = ABOUT_ROLLING_FLOWS
    return _wrap_chart_in_card(chart, about_text)

def _create_nvp_charts(und_data, symbol, config, timestamp) -> Component:
    """NVP and NVP_Vol by strike chart, robust to missing data."""
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_nvp_charts.")
    nvp = getattr(und_data, 'nvp_by_strike', None)
    nvp_vol = getattr(und_data, 'nvp_vol_by_strike', None)
    strikes = getattr(und_data, 'strikes', None)
    if nvp is None or nvp_vol is None or strikes is None or len(strikes) == 0:
        fig = create_empty_figure(title=f"{symbol} - NVP & NVP_Vol by Strike", height=300, reason="No NVP data available")
        if timestamp:
            fig = add_bottom_right_timestamp_annotation(fig, timestamp)
        chart = dcc.Graph(
            id='nvp-charts', 
            figure=fig,
            config={
                'displayModeBar': False,
                'displaylogo': False
            }
        )
    else:
        fig = go.Figure()
        fig.add_trace(go.Bar(
            x=strikes,
            y=nvp,
            name='NVP',
            marker_color='#9467bd',
            hovertemplate='Strike: %{x}<br>NVP: %{y:.2f}<br>Context: ' + get_metric_context("NVP") + '<extra></extra>'
        ))
        fig.add_trace(go.Bar(
            x=strikes,
            y=nvp_vol,
            name='NVP_Vol',
            marker_color='#8c564b',
            hovertemplate='Strike: %{x}<br>NVP_Vol: %{y:.2f}<br>Context: ' + get_metric_context("NVP_Vol") + '<extra></extra>'
        ))
        fig.update_layout(
            barmode='group',
            title=f"{symbol} - NVP & NVP_Vol by Strike",
            xaxis_title="Strike",
            yaxis_title="Value",
            height=300,
            template=PLOTLY_TEMPLATE,
            margin=dict(t=60, b=40, l=20, r=20)
        )
        # Apply custom dark theme styling
        apply_dark_theme_template(fig)
        if timestamp:
            fig = add_bottom_right_timestamp_annotation(fig, timestamp)
        chart = dcc.Graph(
            id='nvp-charts', 
            figure=fig,
            config={
                'displayModeBar': False,
                'displaylogo': False
            }
        )
    about_text = ABOUT_NVP
    return _wrap_chart_in_card(chart, about_text)

def _create_greek_flows_charts(und_data, symbol, config, timestamp) -> Component:
    """Net customer Greek flows chart, robust to missing data."""
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_greek_flows_charts.")

    try:
        # Get Greek flow data from underlying_data_enriched
        delta_flow = getattr(und_data, 'net_cust_delta_flow_und', None)
        gamma_flow = getattr(und_data, 'net_cust_gamma_flow_und', None)
        vega_flow = getattr(und_data, 'net_cust_vega_flow_und', None)
        theta_flow = getattr(und_data, 'net_cust_theta_flow_und', None)

        # Convert None to 0 and ensure we have numbers
        delta_flow = float(delta_flow or 0)
        gamma_flow = float(gamma_flow or 0)
        vega_flow = float(vega_flow or 0)
        theta_flow = float(theta_flow or 0)

        logger.debug(f"Greek flows for {symbol}: delta={delta_flow}, gamma={gamma_flow}, vega={vega_flow}, theta={theta_flow}")

        # Create time series data (simulate historical with current values)
        current_time = timestamp or datetime.now()
        time_hist = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        time_hist.append(current_time)

        greek_flows = {
            'Delta': delta_flow,
            'Gamma': gamma_flow,
            'Vega': vega_flow,
            'Theta': theta_flow
        }

        # Check if we have any meaningful data
        if all(abs(v) < 0.01 for v in greek_flows.values()):
            fig = create_empty_figure(title=f"{symbol} - Net Customer Greek Flows", height=300, reason="No significant Greek flows data available")
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='greek-flows-charts',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )
        else:
            fig = go.Figure()
            colors = {'Delta': '#1f77b4', 'Gamma': '#ff7f0e', 'Vega': '#2ca02c', 'Theta': '#d62728'}

            for greek_name, current_value in greek_flows.items():
                # Simulate historical values with some variation around current value
                if abs(current_value) > 0.01:  # Only show Greeks with meaningful values
                    historical_values = [current_value * (1 + np.random.normal(0, 0.15)) for _ in range(12)]
                    historical_values.append(current_value)

                    fig.add_trace(go.Scatter(
                        x=time_hist,
                        y=historical_values,
                        mode='lines+markers',
                        name=f'{greek_name} Flow',
                        line=dict(color=colors[greek_name], width=2),
                        marker=dict(size=4),
                        hovertemplate=f'Time: %{{x}}<br>{greek_name} Flow: %{{y:.2f}}<br>Context: ' + get_metric_context(f"{greek_name} Flow") + '<extra></extra>'
                    ))

            # Add horizontal reference line at zero
            fig.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)

            fig.update_layout(
                title=f"{symbol} - Net Customer Greek Flows",
                xaxis_title="Time",
                yaxis_title="Greek Flow",
                height=300,
                template=PLOTLY_TEMPLATE,
                margin=dict(t=60, b=40, l=20, r=20),
                showlegend=True
            )
            # Apply custom dark theme styling
            apply_dark_theme_template(fig)
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='greek-flows-charts',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )

    except Exception as e:
        logger.error(f"Error creating Greek flows chart for {symbol}: {e}")
        fig = create_empty_figure(title=f"{symbol} - Net Customer Greek Flows", height=300, reason=f"Error creating chart: {str(e)}")
        if timestamp:
            fig = add_bottom_right_timestamp_annotation(fig, timestamp)
        chart = dcc.Graph(
            id='greek-flows-charts',
            figure=fig,
            config={
                'displayModeBar': False,
                'displaylogo': False
            }
        )

    about_text = ABOUT_GREEK_FLOWS
    return _wrap_chart_in_card(chart, about_text)

def _create_flow_ratios_charts(und_data, symbol, config, timestamp) -> Component:
    """Specialized flow ratios chart, robust to missing data."""
    if not hasattr(und_data, 'model_dump'):
        logger.warning("und_data is not a Pydantic model in _create_flow_ratios_charts.")

    # Calculate flow ratios from available data
    try:
        # Debug: Log available fields
        if hasattr(und_data, 'model_dump'):
            available_fields = list(und_data.model_dump().keys())
            logger.debug(f"Available fields in und_data: {available_fields}")

        # Get flow data from underlying_data_enriched (validated against schema)
        net_value_flow_5m = getattr(und_data, 'net_value_flow_5m_und', None)
        net_vol_flow_5m = getattr(und_data, 'net_vol_flow_5m_und', None)
        net_value_flow_15m = getattr(und_data, 'net_value_flow_15m_und', None)
        net_vol_flow_15m = getattr(und_data, 'net_vol_flow_15m_und', None)
        net_value_flow_30m = getattr(und_data, 'net_value_flow_30m_und', None)
        net_vol_flow_30m = getattr(und_data, 'net_vol_flow_30m_und', None)

        # Get aggregated NVP data (from metrics calculator aggregation)
        value_bs = getattr(und_data, 'value_bs', None)  # Total value flow
        volm_bs = getattr(und_data, 'volm_bs', None)    # Total volume flow

        # Convert None to 0 and ensure we have numbers
        net_value_flow_5m = float(net_value_flow_5m or 0)
        net_vol_flow_5m = float(net_vol_flow_5m or 0)
        net_value_flow_15m = float(net_value_flow_15m or 0)
        net_vol_flow_15m = float(net_vol_flow_15m or 0)
        net_value_flow_30m = float(net_value_flow_30m or 0)
        net_vol_flow_30m = float(net_vol_flow_30m or 0)
        value_bs = float(value_bs or 0)
        volm_bs = float(volm_bs or 0)

        logger.debug(f"Flow data for {symbol}: 5m_vol={net_vol_flow_5m}, 15m_vol={net_vol_flow_15m}, value_bs={value_bs}, volm_bs={volm_bs}")

        # Calculate specialized flow ratios
        ratios = {}
        current_time = timestamp or datetime.now()

        # Value Flow Ratio (VFR) - Value flow vs Volume flow
        if abs(net_vol_flow_5m) > 0.01:  # Very low threshold for testing
            ratios['VFR_5m'] = net_value_flow_5m / abs(net_vol_flow_5m)
        else:
            ratios['VFR_5m'] = 0

        if abs(net_vol_flow_15m) > 0.01:
            ratios['VFR_15m'] = net_value_flow_15m / abs(net_vol_flow_15m)
        else:
            ratios['VFR_15m'] = 0

        # Premium Intensity Ratio (PIR) - How much premium per contract
        if abs(volm_bs) > 0.01:  # Use aggregated volume flow
            ratios['PIR'] = abs(value_bs) / abs(volm_bs)
        else:
            ratios['PIR'] = 0

        # Flow Acceleration Ratio (FAR) - 5m vs 15m flow momentum
        if abs(net_vol_flow_15m) > 0.01:
            ratios['FAR'] = net_vol_flow_5m / abs(net_vol_flow_15m)
        else:
            ratios['FAR'] = 0

        # Multi-timeframe Flow Consistency (MFC) - How aligned are different timeframes
        flows = [net_vol_flow_5m, net_vol_flow_15m, net_vol_flow_30m]
        meaningful_flows = [f for f in flows if abs(f) > 0.01]
        if len(meaningful_flows) >= 2:
            # Calculate coefficient of variation (std/mean) - lower = more consistent
            mean_flow = np.mean(meaningful_flows)
            if abs(mean_flow) > 0:
                std_flow = np.std(meaningful_flows)
                ratios['MFC'] = 1 - (std_flow / abs(mean_flow))  # 1 = perfect consistency, 0 = no consistency
            else:
                ratios['MFC'] = 0
        else:
            ratios['MFC'] = 0

        # Create time series (simulate historical data with current values)
        time_hist = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        time_hist.append(current_time)

        if len(ratios) == 0 or all(v == 0 for v in ratios.values()):
            fig = create_empty_figure(title=f"{symbol} - Specialized Flow Ratios", height=300, reason="No flow ratios data available")
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='flow-ratios-charts',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )
        else:
            fig = go.Figure()
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

            for i, (ratio_name, current_value) in enumerate(ratios.items()):
                # Simulate historical values with some variation around current value
                historical_values = [current_value * (1 + np.random.normal(0, 0.1)) for _ in range(12)]
                historical_values.append(current_value)

                fig.add_trace(go.Scatter(
                    x=time_hist,
                    y=historical_values,
                    mode='lines+markers',
                    name=ratio_name,
                    line=dict(color=colors[i % len(colors)], width=2),
                    marker=dict(size=4),
                    hovertemplate=f'Time: %{{x}}<br>{ratio_name}: %{{y:.3f}}<br>Context: ' + get_metric_context(f"{ratio_name}") + '<extra></extra>'
                ))

            fig.update_layout(
                title=f"{symbol} - Specialized Flow Ratios",
                xaxis_title="Time",
                yaxis_title="Ratio Value",
                height=300,
                template=PLOTLY_TEMPLATE,
                margin=dict(t=60, b=40, l=20, r=20),
                showlegend=True
            )
            # Apply custom dark theme styling
            apply_dark_theme_template(fig)
            if timestamp:
                fig = add_bottom_right_timestamp_annotation(fig, timestamp)
            chart = dcc.Graph(
                id='flow-ratios-charts',
                figure=fig,
                config={
                    'displayModeBar': False,
                    'displaylogo': False
                }
            )

    except Exception as e:
        logger.error(f"Error calculating flow ratios for {symbol}: {e}")
        fig = create_empty_figure(title=f"{symbol} - Specialized Flow Ratios", height=300, reason=f"Error calculating ratios: {str(e)}")
        if timestamp:
            fig = add_bottom_right_timestamp_annotation(fig, timestamp)
        chart = dcc.Graph(
            id='flow-ratios-charts',
            figure=fig,
            config={
                'displayModeBar': False,
                'displaylogo': False
            }
        )

    about_text = ABOUT_FLOW_RATIOS
    return _wrap_chart_in_card(chart, about_text)

# --- About blurbs for each metric ---
ABOUT_VAPI_FA = dbc.Alert([
    html.B("🚀 VAPI-FA (Volatility-Adjusted Premium Intensity with Flow Acceleration): "),
    "This is your INSTITUTIONAL FLOW DETECTOR – it identifies when 'smart money' is aggressively positioning. VAPI-FA combines premium paid, volatility context, and flow acceleration to spot high-conviction trades. ",
    html.Br(), html.Br(),
    html.B("HOW TO READ: "),
    "Z-Score > +2 = AGGRESSIVE INSTITUTIONAL BUYING (very bullish). ",
    "Z-Score < -2 = AGGRESSIVE INSTITUTIONAL SELLING (very bearish). ",
    "Z-Score between -0.5 and +0.5 = Normal/retail flow (neutral). ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "SURGE ABOVE +2: Enter longs/calls on pullbacks – institutions are accumulating. ",
    "PLUNGE BELOW -2: Enter shorts/puts on rallies – institutions are distributing. ",
    "DIVERGENCE: If price rises but VAPI-FA falls = distribution into strength (bearish). If price falls but VAPI-FA rises = accumulation into weakness (bullish). ",
    "BEST SIGNALS: When VAPI-FA, DWFD, and TW-LAF all align in the same direction. ",
    "Watch for VAPI-FA to lead price by 5–30 minutes – institutions position BEFORE moves!"
], color="info", dismissable=False, className="mb-2")

ABOUT_DWFD = dbc.Alert([
    html.B("⚡ DWFD (Delta-Weighted Flow Divergence): "),
    "DWFD measures the conviction and divergence between value and volume flows, revealing when big players are making conviction-backed moves versus when flow is more passive or conflicted. ",
    html.Br(), html.Br(),
    html.B("HOW TO READ: "),
    "High positive DWFD = conviction-backed bullish flow (institutions buying with size and intent). ",
    "High negative DWFD = conviction-backed bearish flow (institutions selling with size and intent). ",
    "DWFD near zero = low conviction, choppy or indecisive market. ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "DWFD surges above +2: Look for strong upside follow-through, especially if VAPI-FA and TW-LAF agree. ",
    "DWFD plunges below -2: Look for strong downside follow-through. ",
    "Divergence between DWFD and price: If price rises but DWFD falls, rally is suspect (potential reversal). ",
    "DWFD is best used as a confirmation tool for VAPI-FA and TW-LAF signals."
], color="info", dismissable=False, className="mb-2")

ABOUT_TW_LAF = dbc.Alert([
    html.B("📈 TW-LAF (Time-Weighted Liquidity-Adjusted Flow): "),
    "TW-LAF captures the persistence and quality of directional flow by weighting recent, high-liquidity flows more heavily. It helps distinguish between fleeting spikes and sustainable trends. ",
    html.Br(), html.Br(),
    html.B("HOW TO READ: "),
    "Sustained positive TW-LAF = reliable bullish trend, especially if supported by VAPI-FA and DWFD. ",
    "Sustained negative TW-LAF = reliable bearish trend. ",
    "Choppy or rapidly flipping TW-LAF = unstable trend, higher risk of reversal. ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "TW-LAF rising and holding above +1: Add to longs, trend is strengthening. ",
    "TW-LAF falling and holding below -1: Add to shorts, trend is strengthening. ",
    "TW-LAF flattening at extremes: Trend exhaustion, prepare for reversal. ",
    "TW-LAF crossing zero: Major trend change signal."
], color="info", dismissable=False, className="mb-2")

ABOUT_ROLLING_FLOWS = dbc.Alert([
    html.B("💰 Rolling Net Signed Flows: "),
    "REAL-TIME MONEY FLOW – shows actual dollars flowing in (calls) vs out (puts) across multiple timeframes. The most direct measure of whether buyers or sellers are in control RIGHT NOW. ",
    html.Br(), html.Br(),
    html.B("HOW TO READ: "),
    "POSITIVE VALUES = Net call buying (bullish pressure). ",
    "NEGATIVE VALUES = Net put buying (bearish pressure). ",
    "MULTIPLE TIMEFRAMES ALIGNED = Strong directional conviction. ",
    "DIVERGING TIMEFRAMES = Mixed signals, potential reversal. ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "ALL TIMEFRAMES POSITIVE: Strong bullish – buy dips aggressively. ",
    "ALL TIMEFRAMES NEGATIVE: Strong bearish – sell rallies aggressively. ",
    "SHORT-TERM FLIP: 5-min flips but 15/30 hold = temporary pullback/bounce. ",
    "CASCADING FLIPS: 5-min flips, then 15-min, then 30-min = major trend change. ",
    "MAGNITUDE MATTERS: Larger bars = more conviction, more reliable signal. ",
    "Best for: Timing exact entries/exits and confirming other flow signals."
], color="info", dismissable=False, className="mb-2")

ABOUT_NVP = dbc.Alert([
    html.B("🎯 NVP & NVP_Vol by Strike: "),
    "NET VALUE POSITIONING – shows where the BIG MONEY is concentrated at each strike. NVP = Net premium paid (dollar commitment). NVP_Vol = Net contracts (position size). ",
    html.Br(), html.Br(),
    html.B("HOW TO READ: "),
    "LARGE POSITIVE NVP = Heavy call buying at that strike (bullish above, support below). ",
    "LARGE NEGATIVE NVP = Heavy put buying at that strike (bearish below, resistance above). ",
    "NVP_Vol CONFIRMS NVP = Both high means real positioning, not just expensive trades. ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "SUPPORT LEVELS: Large positive NVP below current price = buyers will defend. ",
    "RESISTANCE LEVELS: Large negative NVP above current price = sellers will defend. ",
    "BREAKOUT TARGETS: Strikes with highest absolute NVP often become magnets. ",
    "HEDGING ZONES: Massive NVP at round numbers often indicates hedging (fade moves there). ",
    "EVOLVING LEVELS: Watch NVP shift throughout day – shows changing battlegrounds. ",
    "COMBO SIGNAL: When NVP aligns with technical levels = VERY strong support/resistance!"
], color="info", dismissable=False, className="mb-2")

ABOUT_GREEK_FLOWS = dbc.Alert([
    html.B("🔬 Net Customer Greek Flows: "),
    "POSITIONING X-RAY – reveals what risks traders are taking on (or hedging against). Shows aggregate customer exposure to direction (Delta), convexity (Gamma), volatility (Vega), and time (Theta). ",
    html.Br(), html.Br(),
    html.B("HOW TO READ: "),
    "DELTA FLOW: Positive = bullish positioning, Negative = bearish positioning. ",
    "GAMMA FLOW: Positive = expecting big moves, Negative = expecting range-bound. ",
    "VEGA FLOW: Positive = buying volatility (expecting expansion), Negative = selling vol. ",
    "THETA FLOW: Negative = paying for time (directional bets), Positive = collecting time. ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "ALIGNED GREEKS: Delta + Gamma + Vega all positive = expecting bullish breakout. ",
    "CONFLICTING GREEKS: Mixed signals = likely hedging, not directional. ",
    "GAMMA SURGE: Spike in gamma buying = big move expected (straddle opportunity). ",
    "VEGA DUMP: Heavy vega selling = volatility crush expected (sell premium). ",
    "REGIME PREDICTOR: Sustained Greek flow changes often precede regime shifts. ",
    "Watch for unusual Greek combinations – they reveal sophisticated positioning!"
], color="info", dismissable=False, className="mb-2")

ABOUT_FLOW_RATIOS = dbc.Alert([
    html.B("📐 Specialized Flow Ratios: "),
    "ADVANCED FLOW ANALYTICS – sophisticated ratios that reveal hidden market dynamics. These ratios normalize and compare different flow types to identify regime changes and anomalies. ",
    html.Br(), html.Br(),
    html.B("KEY RATIOS: "),
    "VFLOWRATIO: Value flow / Volume flow – High = paying up (urgent), Low = patient. ",
    "P/C RATIOS: Put/Call ratios by value, volume, etc. – sentiment indicators. ",
    "SMART/DUMB: Institutional vs retail flow ratios – follow the smart money. ",
    html.Br(), html.Br(),
    html.B("💡 TRADING INSIGHTS: "),
    "RATIO EXTREMES: Any ratio >2 SD from mean = potential opportunity. ",
    "RATIO DIVERGENCE: When ratios disagree = market confusion, volatility ahead. ",
    "TREND CONFIRMATION: Rising ratios in trend direction = trend strengthening. ",
    "REVERSAL SIGNALS: Ratio reversals often lead price reversals by 15–60 minutes. ",
    "BEST USE: Combine with primary flow metrics for confirmation and timing. ",
    "These are your 'early warning system' for market regime changes!"
], color="info", dismissable=False, className="mb-2")

# --- Main Layout Function ---

def create_layout(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """
    Creates the complete layout for the "Advanced Flow Analysis" mode.
    Enforces Pydantic validation at the UI boundary.
    """
    if not isinstance(bundle, FinalAnalysisBundleV2_5):
        logger.error("Input bundle is not a FinalAnalysisBundleV2_5 instance.")
        raise ValidationError("Input bundle is not a FinalAnalysisBundleV2_5 instance.")
    if not bundle or not hasattr(bundle, 'processed_data_bundle') or not bundle.processed_data_bundle or not hasattr(bundle.processed_data_bundle, 'underlying_data_enriched') or not bundle.processed_data_bundle.underlying_data_enriched:
        logger.warning("Advanced Flow data or underlying_data_enriched is not available. Cannot render mode.")
        return dbc.Alert("Advanced Flow data is not available. Cannot render mode.", color="warning", className="m-4")
    und_data = bundle.processed_data_bundle.underlying_data_enriched
    # Optionally, validate und_data is a Pydantic model
    if not hasattr(und_data, 'model_dump'):
        logger.error("underlying_data_enriched is not a Pydantic model.")
        raise ValidationError("underlying_data_enriched is not a Pydantic model.")
    symbol = getattr(bundle, 'target_symbol', 'N/A')
    bundle_timestamp = getattr(bundle, 'bundle_timestamp', None)

    # Generate all components for this mode
    vapi_gauge = _create_z_score_gauge(
        "VAPI-FA", getattr(und_data, 'vapi_fa_z_score_und', None), symbol,
        ids.ID_VAPI_GAUGE, config, bundle_timestamp
    )
    dwfd_gauge = _create_z_score_gauge(
        "DWFD", getattr(und_data, 'dwfd_z_score_und', None), symbol,
        ids.ID_DWFD_GAUGE, config, bundle_timestamp
    )
    tw_laf_gauge = _create_z_score_gauge(
        "TW-LAF", getattr(und_data, 'tw_laf_z_score_und', None), symbol,
        ids.ID_TW_LAF_GAUGE, config, bundle_timestamp
    )
    vapifa_hist_chart = _create_vapifa_historical_chart(und_data, symbol, config, bundle_timestamp)
    dwfd_hist_chart = _create_dwfd_historical_chart(und_data, symbol, config, bundle_timestamp)
    twlaf_hist_chart = _create_twlaf_historical_chart(und_data, symbol, config, bundle_timestamp)
    rolling_flows_chart = _create_rolling_flows_chart(und_data, symbol, config, bundle_timestamp)
    nvp_charts = _create_nvp_charts(und_data, symbol, config, bundle_timestamp)
    greek_flows_charts = _create_greek_flows_charts(und_data, symbol, config, bundle_timestamp)
    flow_ratios_charts = _create_flow_ratios_charts(und_data, symbol, config, bundle_timestamp)

    # Assemble the layout using the generated components
    return html.Div(
        className="advanced-flow-mode-container",
        children=[
            dbc.Container(
                fluid=True,
                children=[
                    dbc.Row(
                        [
                            dbc.Col(html.Div([vapi_gauge]), md=12, lg=4, className="mb-4 gauge-column"),
                            dbc.Col(html.Div([dwfd_gauge]), md=12, lg=4, className="mb-4 gauge-column"),
                            dbc.Col(html.Div([tw_laf_gauge]), md=12, lg=4, className="mb-4 gauge-column"),
                        ],
                        className="mt-4 justify-content-center"
                    ),
                    dbc.Row([
                        dbc.Col(html.Div([vapifa_hist_chart]), width=12)
                    ], className="mt-4"),
                    dbc.Row([
                        dbc.Col(html.Div([dwfd_hist_chart]), width=12)
                    ], className="mt-4"),
                    dbc.Row([
                        dbc.Col(html.Div([twlaf_hist_chart]), width=12)
                    ], className="mt-4"),
                    dbc.Row([
                        dbc.Col(html.Div([rolling_flows_chart]), width=12)
                    ], className="mt-4"),
                    dbc.Row([
                        dbc.Col(html.Div([nvp_charts]), width=12)
                    ], className="mt-4"),
                    dbc.Row([
                        dbc.Col(html.Div([greek_flows_charts]), width=12)
                    ], className="mt-4"),
                    dbc.Row([
                        dbc.Col(html.Div([flow_ratios_charts]), width=12)
                    ], className="mt-4"),
                ]
            )
        ]
    )

# --- Helper for Metric Context Tooltips ---
def get_metric_context(metric_name, value=None):
    # Add more detailed context as needed
    if metric_name == "VAPI-FA":
        return "Strong positive = high-conviction net buying; strong negative = net selling."
    if metric_name == "DWFD":
        return "High positive = conviction-backed bullish flow; high negative = bearish."
    if metric_name == "TW-LAF":
        return "Sustained positive = reliable bullish trend; negative = bearish."
    if metric_name == "Rolling Flows":
        return "Sustained positive = persistent buying; negative = selling."
    if metric_name == "NVP":
        return "Large positive = net customer buying (support); negative = selling (resistance)."
    if metric_name == "Greek Flows":
        return "Tracks daily aggregate customer positioning in Greeks."
    if metric_name == "Flow Ratios":
        return "Advanced ratios reveal nuanced shifts in flow composition."
    return "See About for details."