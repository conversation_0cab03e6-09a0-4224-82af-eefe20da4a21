"""
Targeted Signal Threshold Fix v2.5
==================================

This script directly addresses the high signal thresholds that are preventing
signal generation in the EOTS v2.5 system by modifying the signal generator
parameter methods to use more sensitive thresholds.

Issues addressed:
1. VAPI-FA threshold too high (2.0 -> 1.0)
2. DWFD threshold too high (2.0 -> 1.2) 
3. TW-LAF threshold too high (1.5 -> 0.8)
4. VRI thresholds too high (5000 -> 2000)
5. A-MSPI thresholds too high (0.7 -> 0.4)

Author: EOTS v2.5 Development Team - "Signal Threshold Optimization Division"
Version: 2.5.0 - "SIGNAL SENSITIVITY RESTORATION"
"""

import os
import sys
import logging
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_signal_generator_thresholds():
    """Fix signal generator thresholds directly in the code."""
    logger.info("🔧 Fixing signal generator thresholds...")
    
    try:
        signal_generator_path = "core_analytics_engine/signal_generator_v2_5.py"
        
        if not os.path.exists(signal_generator_path):
            logger.error(f"Signal generator file not found: {signal_generator_path}")
            return False
        
        # Read the current file
        with open(signal_generator_path, 'r') as f:
            content = f.read()
        
        # Store original content for backup
        backup_path = f"{signal_generator_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_path, 'w') as f:
            f.write(content)
        logger.info(f"📁 Backup created: {backup_path}")
        
        # Apply threshold fixes
        fixes_applied = 0
        
        # Fix 1: VAPI-FA threshold (2.0 -> 1.0)
        if 'vapi_fa_z_thresh", 2.0' in content:
            content = content.replace('vapi_fa_z_thresh", 2.0', 'vapi_fa_z_thresh", 1.0')
            fixes_applied += 1
            logger.info("✅ Fixed VAPI-FA threshold: 2.0 -> 1.0")
        
        # Fix 2: DWFD threshold (2.0 -> 1.2)
        if 'dwfd_z_thresh", 2.0' in content:
            content = content.replace('dwfd_z_thresh", 2.0', 'dwfd_z_thresh", 1.2')
            fixes_applied += 1
            logger.info("✅ Fixed DWFD threshold: 2.0 -> 1.2")
        
        # Fix 3: TW-LAF threshold (1.5 -> 0.8)
        if 'tw_laf_z_thresh", 1.5' in content:
            content = content.replace('tw_laf_z_thresh", 1.5', 'tw_laf_z_thresh", 0.8')
            fixes_applied += 1
            logger.info("✅ Fixed TW-LAF threshold: 1.5 -> 0.8")
        
        # Fix 4: VRI 2.0 threshold (5000 -> 2000)
        if '"vri_2_0_thresh": 5000' in content:
            content = content.replace('"vri_2_0_thresh": 5000', '"vri_2_0_thresh": 2000')
            fixes_applied += 1
            logger.info("✅ Fixed VRI 2.0 threshold: 5000 -> 2000")
        
        # Fix 5: VRI 0DTE threshold (2000 -> 1000)
        if '"vri_0dte_thresh": 2000' in content:
            content = content.replace('"vri_0dte_thresh": 2000', '"vri_0dte_thresh": 1000')
            fixes_applied += 1
            logger.info("✅ Fixed VRI 0DTE threshold: 2000 -> 1000")
        
        # Fix 6: A-MSPI threshold (0.7 -> 0.4)
        if '"a_mspi_thresh": 0.7' in content:
            content = content.replace('"a_mspi_thresh": 0.7', '"a_mspi_thresh": 0.4')
            fixes_applied += 1
            logger.info("✅ Fixed A-MSPI threshold: 0.7 -> 0.4")
        
        # Fix 7: A-SAI threshold (0.6 -> 0.4)
        if '"a_sai_thresh": 0.6' in content:
            content = content.replace('"a_sai_thresh": 0.6', '"a_sai_thresh": 0.4')
            fixes_applied += 1
            logger.info("✅ Fixed A-SAI threshold: 0.6 -> 0.4")
        
        # Fix 8: A-DAG threshold (25000 -> 15000)
        if '"a_dag_thresh": 25000' in content:
            content = content.replace('"a_dag_thresh": 25000', '"a_dag_thresh": 15000')
            fixes_applied += 1
            logger.info("✅ Fixed A-DAG threshold: 25000 -> 15000")
        
        # Fix 9: DWFD Z-score threshold in complex signals (1.8 -> 1.2)
        if '"dwfd_z_thresh": 1.8' in content:
            content = content.replace('"dwfd_z_thresh": 1.8', '"dwfd_z_thresh": 1.2')
            fixes_applied += 1
            logger.info("✅ Fixed DWFD Z-score threshold: 1.8 -> 1.2")
        
        # Fix 10: A-SSI threshold (0.3 -> 0.5) - Higher threshold for structure change
        if '"a_ssi_thresh": 0.3' in content:
            content = content.replace('"a_ssi_thresh": 0.3', '"a_ssi_thresh": 0.5')
            fixes_applied += 1
            logger.info("✅ Fixed A-SSI threshold: 0.3 -> 0.5")
        
        # Write the updated content
        if fixes_applied > 0:
            with open(signal_generator_path, 'w') as f:
                f.write(content)
            logger.info(f"✅ Applied {fixes_applied} threshold fixes to signal generator")
            return True
        else:
            logger.warning("⚠️ No threshold patterns found to fix")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error fixing signal generator thresholds: {str(e)}")
        return False

def test_signal_generation_with_sample_data():
    """Test signal generation with sample data that should trigger signals."""
    logger.info("🧪 Testing signal generation with sample data...")
    
    try:
        from core_analytics_engine.signal_generator_v2_5 import SignalGeneratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from data_models.eots_schemas_v2_5 import (
            ProcessedDataBundleV2_5, 
            ProcessedUnderlyingAggregatesV2_5
        )
        import pandas as pd
        from datetime import datetime
        
        # Initialize components
        config_manager = ConfigManagerV2_5()
        signal_generator = SignalGeneratorV2_5(config_manager)
        
        # Create sample data that should generate signals with new thresholds
        underlying_data = ProcessedUnderlyingAggregatesV2_5(
            symbol="SPY",
            timestamp=datetime.now(),
            # Flow metrics - should trigger flow signals
            vapi_fa_z_score_und=1.1,    # Above new threshold of 1.0
            dwfd_z_score_und=1.3,       # Above new threshold of 1.2
            tw_laf_z_score_und=0.9,     # Above new threshold of 0.8
            # Structure metrics - should trigger directional signals
            a_mspi_und_summary_score=0.5,  # Above new threshold of 0.4
            a_sai_und_avg=0.45,            # Above new threshold of 0.4
            a_dag_und=16000,               # Above new threshold of 15000
            # Volatility metrics - should trigger volatility signals
            vri_2_0_und_aggregate=2500,    # Above new threshold of 2000
            vri_0dte_und_sum=1200,         # Above new threshold of 1000
            # Complex metrics - should trigger complex signals
            a_ssi_und_avg=0.4,             # Below new threshold of 0.5 (structure change)
            # Other required fields
            gib_oi_based_und=0.3,
            current_market_regime_v2_5="BULLISH_MOMENTUM"
        )
        
        # Create bundle
        bundle = ProcessedDataBundleV2_5(
            underlying_data_enriched=underlying_data,
            symbol="SPY",
            timestamp=datetime.now(),
            processing_timestamp=datetime.now()
        )
        
        # Create sample strike data
        df_strike = pd.DataFrame({
            'strike': [580, 585, 590, 595, 600],
            'd_tdpi_strike': [0.35, 0.4, 0.25, 0.3, 0.2],
            'vci_0dte': [0.45, 0.5, 0.3, 0.4, 0.25]
        })
        
        # Generate signals
        all_signals = signal_generator.generate_all_signals(bundle)
        
        # Count signals by type
        total_signals = 0
        for signal_type, signals in all_signals.items():
            signal_count = len(signals)
            total_signals += signal_count
            logger.info(f"   - {signal_type}: {signal_count} signals")
            
            # Log first signal of each type for verification
            if signals:
                first_signal = signals[0]
                logger.info(f"     Example: {first_signal.signal_name} (strength: {first_signal.strength_score:.2f})")
        
        logger.info(f"✅ Signal generation test completed - Total signals: {total_signals}")
        
        if total_signals > 0:
            logger.info("🎉 SUCCESS: Signals are now being generated!")
            return True
        else:
            logger.warning("⚠️ No signals generated - may need further threshold adjustments")
            return False
        
    except Exception as e:
        logger.error(f"❌ Signal generation test failed: {str(e)}")
        return False

def main():
    """Main function to fix signal thresholds."""
    logger.info("🚀 Starting Targeted Signal Threshold Fix for EOTS v2.5...")
    
    try:
        # Fix signal generator thresholds
        if not fix_signal_generator_thresholds():
            logger.error("❌ Failed to fix signal generator thresholds")
            return False
        
        # Test signal generation
        if test_signal_generation_with_sample_data():
            logger.info("✅ Signal generation test passed")
        else:
            logger.warning("⚠️ Signal generation test failed - may need manual review")
        
        logger.info("🎉 Targeted signal threshold fix completed!")
        logger.info("✅ The system should now generate signals with more sensitive thresholds")
        logger.info("🔄 Please restart the EOTS system to apply changes")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Targeted signal threshold fix failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("✨ Targeted signal threshold fix completed successfully!")
    else:
        logger.error("❌ Targeted signal threshold fix failed")
    sys.exit(0 if success else 1)
