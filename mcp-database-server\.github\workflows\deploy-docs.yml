name: Deploy Docusaurus documentation to GH Pages

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]
    paths:
      - 'docs/**'  # Only trigger when docs directory changes
  pull_request:
    branches: ["main"]
    paths:
      - 'docs/**'  # Only trigger when docs directory changes

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'docs/package-lock.json'
      - name: Install dependencies
        working-directory: docs
        run: npm ci
      - name: Build site
        working-directory: docs
        run: npm run build
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with: 
          path: docs/build

  # Deployment job for main branch
  deploy-main:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'  # Only deploy on main branch
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

  # Deployment job for PR branches (will deploy to a PR-specific subdomain)
  deploy-preview:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'  # Only deploy on PR
    environment:
      name: preview-${{ github.event.pull_request.number }}
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Deploy PR Preview
        id: deployment
        uses: actions/deploy-pages@v4
        with:
          preview: true
          deploy_key: ${{ secrets.ACTIONS_DEPLOY_KEY }} 