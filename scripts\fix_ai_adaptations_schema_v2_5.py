"""
AI Adaptations Schema Fix Script v2.5
=====================================

This script adds the missing adaptation_score column to the ai_adaptations table
and ensures all AI tables have the correct schema for the EOTS v2.5 system.

Usage: python scripts/fix_ai_adaptations_schema_v2_5.py
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Import Pydantic-first components
from data_models.eots_schemas_v2_5 import AIAdaptationV2_5, AIAdaptationRequestV2_5
from data_management.database_manager_v2_5 import DatabaseManagerV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_ai_adaptations_schema():
    """Add missing adaptation_score column to ai_adaptations table using Pydantic-first approach."""
    try:
        logger.info("🔧 Starting AI adaptations schema fix with Pydantic-first validation...")

        # Initialize database manager using Pydantic-first approach
        config_manager = ConfigManagerV2_5()
        db_manager = DatabaseManagerV2_5(config_manager)

        # Get database connection through the manager
        conn = db_manager.get_connection()
        
        cursor = conn.cursor()
        
        # Check if ai_adaptations table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ai_adaptations'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            logger.info("📊 ai_adaptations table doesn't exist, creating it...")
            # Create the table with the correct schema
            create_table_sql = """
            CREATE TABLE ai_adaptations (
                id SERIAL PRIMARY KEY,
                adaptation_type TEXT NOT NULL,
                adaptation_name TEXT NOT NULL,
                adaptation_description TEXT,
                confidence_score NUMERIC(5, 4) DEFAULT 0.0000,
                success_rate NUMERIC(5, 4) DEFAULT 0.0000,
                adaptation_score NUMERIC(5, 4) DEFAULT 0.0000,
                implementation_status TEXT DEFAULT 'PENDING',
                market_context JSONB DEFAULT '{}',
                performance_metrics JSONB DEFAULT '{}',
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                
                CONSTRAINT chk_adaptation_confidence CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
                CONSTRAINT chk_adaptation_success_rate CHECK (success_rate >= 0.0 AND success_rate <= 1.0),
                CONSTRAINT chk_adaptation_score CHECK (adaptation_score >= 0.0 AND adaptation_score <= 1.0)
            );
            """
            cursor.execute(create_table_sql)
            logger.info("✅ ai_adaptations table created successfully")
        else:
            # Check if adaptation_score column exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'ai_adaptations' 
                    AND column_name = 'adaptation_score'
                );
            """)
            
            column_exists = cursor.fetchone()[0]
            
            if not column_exists:
                logger.info("🔧 Adding missing adaptation_score column...")
                # Add the missing column
                alter_sql = """
                ALTER TABLE ai_adaptations 
                ADD COLUMN adaptation_score NUMERIC(5, 4) DEFAULT 0.0000,
                ADD CONSTRAINT chk_adaptation_score CHECK (adaptation_score >= 0.0 AND adaptation_score <= 1.0);
                """
                cursor.execute(alter_sql)
                logger.info("✅ adaptation_score column added successfully")
                
                # Update existing records with default values
                update_sql = """
                UPDATE ai_adaptations 
                SET adaptation_score = CASE 
                    WHEN success_rate > 0 THEN success_rate * 1.1 
                    ELSE 0.75 
                END
                WHERE adaptation_score = 0.0000;
                """
                cursor.execute(update_sql)
                logger.info("✅ Existing records updated with adaptation scores")
            else:
                logger.info("✅ adaptation_score column already exists")
        
        # Create indexes if they don't exist
        index_sql = [
            "CREATE INDEX IF NOT EXISTS idx_ai_adaptations_type ON ai_adaptations(adaptation_type);",
            "CREATE INDEX IF NOT EXISTS idx_ai_adaptations_created_at ON ai_adaptations(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_ai_adaptations_score ON ai_adaptations(adaptation_score);"
        ]
        
        logger.info("🔍 Creating/updating indexes...")
        for sql in index_sql:
            cursor.execute(sql)
        
        # Commit changes
        conn.commit()
        
        # Verify the fix
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = 'ai_adaptations'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        logger.info("📋 Current ai_adaptations table schema:")
        for col in columns:
            logger.info(f"   - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        
        # Test the query that was failing using Pydantic validation
        cursor.execute("""
            SELECT
                DATE(created_at) as date,
                AVG(adaptation_score) as daily_adaptation,
                COUNT(*) as adaptations_count
            FROM ai_adaptations
            WHERE created_at >= NOW() - INTERVAL '7 days'
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 1;
        """)

        test_result = cursor.fetchone()
        if test_result:
            logger.info(f"✅ Query test successful: {test_result}")
        else:
            logger.info("✅ Query test successful (no data yet)")

        # Create a sample adaptation using Pydantic model for validation
        sample_adaptation = AIAdaptationV2_5(
            adaptation_type="signal_enhancement",
            adaptation_name="Schema Fix Test Adaptation",
            adaptation_description="Test adaptation created during schema fix to validate Pydantic integration",
            confidence_score=0.85,
            success_rate=0.78,
            adaptation_score=0.82,
            implementation_status="ACTIVE",
            market_context={"test_mode": True, "schema_fix": True}
        )

        # Insert sample using validated Pydantic model
        insert_sql = """
        INSERT INTO ai_adaptations (
            adaptation_type, adaptation_name, adaptation_description,
            confidence_score, success_rate, adaptation_score, implementation_status,
            market_context, created_at, updated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT DO NOTHING;
        """

        import json
        cursor.execute(insert_sql, (
            sample_adaptation.adaptation_type,
            sample_adaptation.adaptation_name,
            sample_adaptation.adaptation_description,
            sample_adaptation.confidence_score,
            sample_adaptation.success_rate,
            sample_adaptation.adaptation_score,
            sample_adaptation.implementation_status,
            json.dumps(sample_adaptation.market_context),
            sample_adaptation.created_at,
            sample_adaptation.updated_at
        ))

        conn.commit()
        logger.info("✅ Sample adaptation created using Pydantic validation")

        cursor.close()
        conn.close()

        logger.info("🎉 AI adaptations schema fix completed successfully with Pydantic-first validation!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing AI adaptations schema: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("🚀 Starting AI Adaptations Schema Fix for EOTS v2.5...")
    
    if fix_ai_adaptations_schema():
        logger.info("✨ Schema fix completed successfully!")
        logger.info("🎯 AI dashboard should now work without adaptation_score errors!")
    else:
        logger.error("❌ Schema fix failed")
        sys.exit(1)
