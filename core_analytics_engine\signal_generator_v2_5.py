# core_analytics_engine/signal_generator_v2_5.py
# EOTS v2.5 - S-GRADE PRODUCTION HARDENED ARTIFACT

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

from data_models.eots_schemas_v2_5 import ProcessedDataBundleV2_5, SignalPayloadV2_5, ProcessedUnderlyingAggregatesV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5


logger = logging.getLogger(__name__)
EPSILON = 1e-9

class SignalGeneratorV2_5:
    """
    Generates discrete, scored trading signals based on a fully processed data bundle.
    This hardened version ensures production-grade resilience through robust error
    handling and employs vectorized logic where possible for maximum performance.
    """

    def __init__(self, config_manager: ConfigManagerV2_5):
        """
        Initializes the SignalGeneratorV2_5.

        Args:
            config_manager (ConfigManagerV2_5): The system's configuration manager,
                providing access to signal_generator_settings_v2_5.
        """

        self.logger = logger.getChild(self.__class__.__name__)
        

        self.config_manager = config_manager
        
        self.settings = self.config_manager.get_setting("signal_generator_settings_v2_5", default={})
        if not self.settings: # Log if settings are empty, as it might affect signal generation
            self.logger.warning("signal_generator_settings_v2_5 not found or empty in configuration. Using default behaviors.")

        # PYDANTIC COMPLIANCE FIX: Check if settings is Pydantic model or dict
        if hasattr(self.settings, '__dict__') and hasattr(self.settings, '__fields__'):
            # Pydantic model - access attributes directly
            self.activation = getattr(self.settings, 'signal_activation', {"EnableAllSignals": True})
        else:
            # Dictionary-style access for backward compatibility
            self.activation = (self.settings.get("signal_activation", {"EnableAllSignals": True}) 
                             if hasattr(self.settings, 'get') 
                             else getattr(self.settings, 'signal_activation', {"EnableAllSignals": True}))
        
        self.logger.info("SignalGeneratorV2_5 initialized with activation settings.")

    def generate_all_signals(self, bundle: ProcessedDataBundleV2_5) -> Dict[str, List[SignalPayloadV2_5]]:
        """
        Orchestrates the generation of all signal categories, passing the full data
        bundle to the specialized methods.
        """
        if not isinstance(bundle, ProcessedDataBundleV2_5):
            self.logger.error("generate_all_signals received an invalid data bundle type. Returning empty signals dict.")
            return {}

        if not bundle.underlying_data_enriched or not bundle.underlying_data_enriched.symbol:
             self.logger.error("Underlying data or symbol missing in bundle. Cannot generate signals.")
             return {}


        regime = getattr(bundle.underlying_data_enriched, 'current_market_regime_v2_5', 'UNKNOWN') # Default to UNKNOWN
        signals: Dict[str, List[SignalPayloadV2_5]] = {
            'directional': [], 'volatility': [], 'time_decay': [],
            'complex': [], 'v2_5_enhanced_flow': []
        }
        
        enable_all = self.activation.get("EnableAllSignals", False)

        # Create DataFrames once for performance
        # Ensure strike_level_data_with_metrics is not None before list comprehension
        strike_metrics_list = bundle.strike_level_data_with_metrics or []
        df_strike = pd.DataFrame([s.model_dump() for s in strike_metrics_list])
        if not df_strike.empty:
            df_strike.set_index('strike', inplace=True, drop=False)
        else:
            self.logger.debug("Strike level metrics data is empty or None. df_strike will be empty.")


        if enable_all or self.activation.get("v2_5_enhanced_flow_signals", False):
            signals['v2_5_enhanced_flow'] = self._generate_v2_5_enhanced_flow_signals(bundle.underlying_data_enriched, regime)
        
        if enable_all or self.activation.get("directional_signals", False):
            signals['directional'] = self._generate_directional_signals(bundle, regime, df_strike)

        if enable_all or self.activation.get("volatility_signals", False):
            signals['volatility'] = self._generate_volatility_signals(bundle, regime, df_strike)

        if enable_all or self.activation.get("time_decay_signals", False):
            signals['time_decay'] = self._generate_time_decay_signals(bundle, regime, df_strike)

        if enable_all or self.activation.get("complex_signals", False):
            signals['complex'] = self._generate_complex_signals(bundle, regime, df_strike)


        total_signals_generated = sum(len(v) for v in signals.values())
        self.logger.info(f"Signal generation complete for {bundle.underlying_data_enriched.symbol}. Found {total_signals_generated} total signals.")
        return signals

    # --- Signal Generation Methods ---
    def _generate_directional_signals(self, bundle: ProcessedDataBundleV2_5, regime: str, df_strike: pd.DataFrame) -> List[SignalPayloadV2_5]:
        """Generate Adaptive Directional Signals based on A-MSPI, A-SAI, and flow confirmations."""
        signals = []
        try:
            und_data = bundle.underlying_data_enriched
            if not und_data:
                return signals

            # Get directional signal parameters
            params = self._get_directional_signals_params()
            regime_params = params.get("regime_thresholds", {}).get(regime, params.get("default_thresholds", {}))

            # Primary Trigger: A-MSPI (Adaptive Market Structure Pressure Index)
            a_mspi = und_data.a_mspi_und_summary_score
            a_mspi_thresh = regime_params.get("a_mspi_thresh", 0.7)

            # Secondary Trigger: A-SAI (Adaptive Signal Alignment Index)
            a_sai = und_data.a_sai_und_avg
            a_sai_thresh = regime_params.get("a_sai_thresh", 0.6)

            # Flow Confirmation: Net Customer Delta Flow (proxy for A-DAG)
            a_dag = und_data.net_cust_delta_flow_und
            a_dag_thresh = regime_params.get("a_dag_thresh", 25000)

            # Generate Bullish Directional Signal
            if (a_mspi is not None and a_mspi > a_mspi_thresh and
                a_sai is not None and a_sai > a_sai_thresh):

                # Calculate signal strength based on confluence
                strength = min((a_mspi + a_sai) / 2.0, 5.0)

                # Flow confirmation boost
                if a_dag is not None and a_dag > a_dag_thresh:
                    strength *= 1.2

                signals.append(self._create_signal_payload(
                    und_data, "Adaptive_Directional_Bullish", strength, regime,
                    details={
                        "a_mspi": a_mspi, "a_sai": a_sai, "a_dag": a_dag,
                        "a_mspi_thresh": a_mspi_thresh, "a_sai_thresh": a_sai_thresh
                    }
                ))

            # Generate Bearish Directional Signal
            elif (a_mspi is not None and a_mspi < -a_mspi_thresh and
                  a_sai is not None and a_sai > a_sai_thresh):

                # Calculate signal strength (negative for bearish)
                strength = max(-(abs(a_mspi) + a_sai) / 2.0, -5.0)

                # Flow confirmation boost
                if a_dag is not None and a_dag < -a_dag_thresh:
                    strength *= 1.2

                signals.append(self._create_signal_payload(
                    und_data, "Adaptive_Directional_Bearish", strength, regime,
                    details={
                        "a_mspi": a_mspi, "a_sai": a_sai, "a_dag": a_dag,
                        "a_mspi_thresh": a_mspi_thresh, "a_sai_thresh": a_sai_thresh
                    }
                ))

            self.logger.info(f"Generated {len(signals)} directional signals for {und_data.symbol}")
            return signals

        except Exception as e:
            self.logger.error(f"Error generating directional signals: {str(e)}")
            return []

    def _generate_volatility_signals(self, bundle: ProcessedDataBundleV2_5, regime: str, df_strike: pd.DataFrame) -> List[SignalPayloadV2_5]:
        """Generate Volatility Regime Signals based on VRI 2.0, vri_0dte, and IVSDH context."""
        signals = []
        try:
            und_data = bundle.underlying_data_enriched
            if not und_data:
                return signals

            # Get volatility signal parameters
            params = self._get_volatility_signals_params()
            regime_params = params.get("regime_thresholds", {}).get(regime, params.get("default_thresholds", {}))

            # Primary Trigger: VRI 2.0 (Volatility Regime Indicator 2.0)
            vri_2_0 = und_data.vri_2_0_und_aggregate
            vri_thresh = regime_params.get("vri_2_0_thresh", 5000)

            # Secondary Trigger: vri_0dte for short-term context
            vri_0dte = und_data.vri_0dte_und_sum
            vri_0dte_thresh = regime_params.get("vri_0dte_thresh", 2000)

            # Generate Volatility Expansion Signal
            if (vri_2_0 is not None and abs(vri_2_0) > vri_thresh):

                # Calculate signal strength
                strength = min(abs(vri_2_0) / vri_thresh, 5.0)
                if vri_2_0 < 0:
                    strength = -strength

                # 0DTE confirmation boost
                if vri_0dte is not None and abs(vri_0dte) > vri_0dte_thresh:
                    strength *= 1.3

                signal_name = "Volatility_Expansion" if abs(strength) > 2.0 else "Volatility_Regime_Shift"

                signals.append(self._create_signal_payload(
                    und_data, signal_name, strength, regime,
                    details={
                        "vri_2_0": vri_2_0, "vri_0dte": vri_0dte,
                        "vri_thresh": vri_thresh, "vri_0dte_thresh": vri_0dte_thresh
                    }
                ))

            # Generate Volatility Contraction Signal (low VRI readings)
            elif (vri_2_0 is not None and abs(vri_2_0) < vri_thresh * 0.3):

                strength = max(0.5 - abs(vri_2_0) / (vri_thresh * 0.3), 0.1)

                signals.append(self._create_signal_payload(
                    und_data, "Volatility_Contraction", strength, regime,
                    details={
                        "vri_2_0": vri_2_0, "vri_0dte": vri_0dte,
                        "contraction_threshold": vri_thresh * 0.3
                    }
                ))

            self.logger.info(f"Generated {len(signals)} volatility signals for {und_data.symbol}")
            return signals

        except Exception as e:
            self.logger.error(f"Error generating volatility signals: {str(e)}")
            return []

    def _generate_time_decay_signals(self, bundle: ProcessedDataBundleV2_5, regime: str, df_strike: pd.DataFrame) -> List[SignalPayloadV2_5]:
        """Generate Enhanced Time Decay Signals based on D-TDPI and vci_0dte context."""
        signals = []
        try:
            und_data = bundle.underlying_data_enriched
            if not und_data or df_strike.empty:
                return signals

            # Get time decay signal parameters
            params = self._get_time_decay_signals_params()
            regime_params = params.get("regime_thresholds", {}).get(regime, params.get("default_thresholds", {}))

            # Primary Trigger: D-TDPI (Dynamic Time Decay Pinning Index) at strikes
            d_tdpi_thresh = regime_params.get("d_tdpi_thresh", 0.3)
            vci_0dte_thresh = regime_params.get("vci_0dte_thresh", 0.4)

            # Look for high D-TDPI values near ATM
            current_price = getattr(und_data, 'current_price', None)
            if current_price is None:
                return signals

            # Filter strikes near ATM (within 5% for pin risk analysis)
            atm_range = current_price * 0.05
            near_atm_strikes = df_strike[
                (df_strike['strike'] >= current_price - atm_range) &
                (df_strike['strike'] <= current_price + atm_range)
            ]

            for _, strike_row in near_atm_strikes.iterrows():
                strike_price = strike_row['strike']

                # Check for D-TDPI pin risk
                d_tdpi = strike_row.get('d_tdpi_strike', 0.0)
                vci_0dte = strike_row.get('vci_0dte', 0.0)

                if abs(d_tdpi) > d_tdpi_thresh:
                    # Calculate pin risk strength
                    strength = min(abs(d_tdpi) / d_tdpi_thresh, 5.0)

                    # VCI confirmation boost
                    if abs(vci_0dte) > vci_0dte_thresh:
                        strength *= 1.4

                    signals.append(self._create_signal_payload(
                        und_data, "Time_Decay_Pin_Risk", strength, regime,
                        strike=strike_price,
                        details={
                            "d_tdpi": d_tdpi, "vci_0dte": vci_0dte,
                            "d_tdpi_thresh": d_tdpi_thresh, "vci_thresh": vci_0dte_thresh,
                            "distance_from_atm": abs(strike_price - current_price)
                        }
                    ))

            # Generate Charm Cascade signal if multiple high D-TDPI strikes
            high_tdpi_count = len([s for s in signals if "Pin_Risk" in s.signal_name])
            if high_tdpi_count >= 2:
                cascade_strength = min(high_tdpi_count * 0.8, 4.0)

                signals.append(self._create_signal_payload(
                    und_data, "Time_Decay_Charm_Cascade", cascade_strength, regime,
                    details={
                        "high_tdpi_strikes": high_tdpi_count,
                        "cascade_risk": "HIGH" if cascade_strength > 3.0 else "MODERATE"
                    }
                ))

            self.logger.info(f"Generated {len(signals)} time decay signals for {und_data.symbol}")
            return signals

        except Exception as e:
            self.logger.error(f"Error generating time decay signals: {str(e)}")
            return []

    def _generate_complex_signals(self, bundle: ProcessedDataBundleV2_5, regime: str, df_strike: pd.DataFrame) -> List[SignalPayloadV2_5]:
        """Generate Predictive Complex Signals based on A-SSI, DWFD, and flow divergences."""
        signals = []
        try:
            und_data = bundle.underlying_data_enriched
            if not und_data:
                return signals

            # Get complex signal parameters
            params = self._get_complex_signals_params()
            regime_params = params.get("regime_thresholds", {}).get(regime, params.get("default_thresholds", {}))

            # Structure Change Trigger: A-SSI (Adaptive Structure Stability Index)
            a_ssi = und_data.a_ssi_und_avg
            a_ssi_thresh = regime_params.get("a_ssi_thresh", 0.3)

            # Flow Divergence Trigger: DWFD (Dollar-Weighted Flow Divergence)
            dwfd_z = und_data.dwfd_z_score_und
            dwfd_thresh = regime_params.get("dwfd_z_thresh", 1.8)

            # Generate Structure Change Signal (Low A-SSI indicates instability)
            if a_ssi is not None and a_ssi < a_ssi_thresh:

                # Calculate signal strength (inverse relationship - lower A-SSI = higher signal)
                strength = min((a_ssi_thresh - a_ssi) / a_ssi_thresh * 4.0, 5.0)

                signals.append(self._create_signal_payload(
                    und_data, "Structure_Change_Warning", strength, regime,
                    details={
                        "a_ssi": a_ssi, "a_ssi_thresh": a_ssi_thresh,
                        "stability_level": "LOW" if a_ssi < 0.2 else "MODERATE"
                    }
                ))

            # Generate Smart Money Divergence Signal
            if dwfd_z is not None and abs(dwfd_z) > dwfd_thresh:

                direction = "bullish" if dwfd_z > 0 else "bearish"
                strength = min(abs(dwfd_z) / dwfd_thresh * 2.0, 5.0)
                if dwfd_z < 0:
                    strength = -strength

                signals.append(self._create_signal_payload(
                    und_data, f"Smart_Money_Divergence_{direction.title()}", strength, regime,
                    details={
                        "dwfd_z_score": dwfd_z, "dwfd_thresh": dwfd_thresh,
                        "divergence_type": direction.upper()
                    }
                ))

            # Generate Complex Confluence Signal if multiple triggers align
            if len(signals) >= 2:
                # Calculate confluence strength
                confluence_strength = min(sum(abs(s.strength_score) for s in signals) / len(signals), 4.5)

                signals.append(self._create_signal_payload(
                    und_data, "Complex_Signal_Confluence", confluence_strength, regime,
                    details={
                        "signal_count": len(signals),
                        "confluence_type": "STRUCTURE_FLOW_ALIGNMENT",
                        "risk_level": "HIGH" if confluence_strength > 3.5 else "MODERATE"
                    }
                ))

            # Generate Bubble/Mispricing Warning for extreme conditions
            if (a_ssi is not None and a_ssi < 0.15 and
                dwfd_z is not None and abs(dwfd_z) > 2.5):

                warning_strength = min((abs(dwfd_z) + (0.15 - a_ssi) * 10) / 2, 5.0)

                signals.append(self._create_signal_payload(
                    und_data, "Bubble_Mispricing_Warning", warning_strength, regime,
                    details={
                        "a_ssi": a_ssi, "dwfd_z": dwfd_z,
                        "warning_level": "EXTREME" if warning_strength > 4.0 else "HIGH"
                    }
                ))

            self.logger.info(f"Generated {len(signals)} complex signals for {und_data.symbol}")
            return signals

        except Exception as e:
            self.logger.error(f"Error generating complex signals: {str(e)}")
            return []

    # --- Existing Implemented Signal Category ---
    def _generate_v2_5_enhanced_flow_signals(self, und_data: ProcessedUnderlyingAggregatesV2_5, regime: str) -> List[SignalPayloadV2_5]:
        """Generates underlying-level signals from Tier 3 flow metrics."""
        signals = []
        if not isinstance(und_data, ProcessedUnderlyingAggregatesV2_5):
            self.logger.error("Invalid und_data provided to _generate_v2_5_enhanced_flow_signals.")
            return []

        try:
            params = self._get_v2_5_enhanced_flow_signals_params()
            regime_params = params.get("regime_thresholds", {}).get(regime, params.get("default_thresholds", {}))

            # VAPI-FA Signal
            vapi_z = und_data.vapi_fa_z_score_und
            vapi_thresh = regime_params.get("vapi_fa_z_thresh", 1.8) # Default threshold if not in config
            if vapi_z is not None and abs(vapi_z) > vapi_thresh:
                signals.append(self._create_signal_payload(
                    und_data, "VAPI-FA_Momentum_Surge", vapi_z, regime,
                    details={"vapi_z_score": vapi_z, "threshold": vapi_thresh}
                ))
            
            # DWFD Signal
            dwfd_z = und_data.dwfd_z_score_und
            dwfd_thresh = regime_params.get("dwfd_z_thresh", 1.8) # Default threshold
            if dwfd_z is not None and abs(dwfd_z) > dwfd_thresh:
                signals.append(self._create_signal_payload(
                    und_data, "DWFD_Smart_Money_Flow", dwfd_z, regime,
                    details={"dwfd_z_score": dwfd_z, "threshold": dwfd_thresh}
                ))
            
            # TW-LAF Signal
            tw_laf_z = und_data.tw_laf_z_score_und
            tw_laf_thresh = regime_params.get("tw_laf_z_thresh", 1.3) # Default threshold
            if tw_laf_z is not None and abs(tw_laf_z) > tw_laf_thresh:
                signals.append(self._create_signal_payload(
                    und_data, "TW-LAF_Sustained_Trend", tw_laf_z, regime,
                    details={"tw_laf_z_score": tw_laf_z, "threshold": tw_laf_thresh}
                ))

            return signals
        except (KeyError, AttributeError, TypeError) as e: # Added TypeError
            self.logger.error(f"Failed to generate v2.5 enhanced flow signals for {und_data.symbol if und_data else 'UNKNOWN'} due to data/config issue: {e}", exc_info=True)
            return []
        except Exception as e_unhandled: # Catch any other unhandled exceptions
             self.logger.error(f"Unhandled exception in _generate_v2_5_enhanced_flow_signals for {und_data.symbol if und_data else 'UNKNOWN'}: {e_unhandled}", exc_info=True)
             return []

    def _get_v2_5_enhanced_flow_signals_params(self):
        """Get parameters for v2.5 enhanced flow signals."""
        # PYDANTIC COMPLIANCE FIX: Check if settings is Pydantic model or dict
        if hasattr(self.settings, '__dict__') and hasattr(self.settings, '__fields__'):
            # Pydantic model - access attributes directly
            return getattr(self.settings, 'v2_5_enhanced_flow_signals', {})
        else:
            # Dictionary-style access for backward compatibility
            return self.settings.get("v2_5_enhanced_flow_signals", {})

    def _create_signal_payload(self, und_data: ProcessedUnderlyingAggregatesV2_5, name: str, strength: float, regime: str, strike: Optional[float] = None, details: Optional[Dict] = None) -> SignalPayloadV2_5:
        """Helper to construct the SignalPayloadV2_5 Pydantic model."""
        direction = "Neutral"
        if strength > EPSILON: # Using EPSILON for float comparison
            direction = "Bullish"
        elif strength < -EPSILON: # Using EPSILON
            direction = "Bearish"

        # Determine signal type based on name (case-insensitive check)
        name_upper = name.upper()
        signal_type = "Complex" # Default
        if any(k in name_upper for k in ["VAPI", "DWFD", "TW-LAF", "FLOW"]):
            signal_type = "Flow_Momentum"
        elif any(k in name_upper for k in ["MSPI", "SDAG", "DIRECTIONAL"]):
            signal_type = "Directional"
        elif any(k in name_upper for k in ["VOLATILITY", "VRI"]):
            signal_type = "Volatility"
        elif any(k in name_upper for k in ["DECAY", "PIN", "TDPI"]):
            signal_type = "Time_Decay"
        else:
            self.logger.debug(f"Signal '{name}' did not match specific type keywords, defaulting to '{signal_type}'.")


        return SignalPayloadV2_5(
            signal_id=f"sig_{uuid.uuid4().hex[:8]}",
            signal_name=name,
            symbol=und_data.symbol if und_data and und_data.symbol else "UNKNOWN", # Ensure symbol is present
            timestamp=datetime.now(), # Consider using bundle timestamp if consistency is key
            signal_type=signal_type,
            direction=direction,
            strength_score=float(np.clip(strength, -5.0, 5.0)), # Ensure float and clip
            strike_impacted=strike,
            regime_at_signal_generation=regime,
            supporting_metrics=details or {}
        )

    # --- Parameter Helper Methods ---
    def _get_directional_signals_params(self) -> Dict[str, Any]:
        """Get parameters for directional signal generation."""
        return {
            "default_thresholds": {
                "a_mspi_thresh": 0.6,
                "a_sai_thresh": 0.5,
                "a_dag_thresh": 20000
            },
            "regime_thresholds": {
                "REGIME_BULLISH_TREND": {
                    "a_mspi_thresh": 0.6,  # Lower threshold in bullish regime
                    "a_sai_thresh": 0.5,
                    "a_dag_thresh": 20000
                },
                "REGIME_BEARISH_TREND": {
                    "a_mspi_thresh": 0.8,  # Higher threshold in bearish regime
                    "a_sai_thresh": 0.7,
                    "a_dag_thresh": 30000
                }
            }
        }

    def _get_volatility_signals_params(self) -> Dict[str, Any]:
        """Get parameters for volatility signal generation."""
        return {
            "default_thresholds": {
                "vri_2_0_thresh": 4000,
                "vri_0dte_thresh": 1600
            },
            "regime_thresholds": {
                "REGIME_VOLATILE": {
                    "vri_2_0_thresh": 3000,  # Lower threshold in volatile regime
                    "vri_0dte_thresh": 1500
                },
                "REGIME_QUIET": {
                    "vri_2_0_thresh": 7000,  # Higher threshold in quiet regime
                    "vri_0dte_thresh": 3000
                }
            }
        }

    def _get_time_decay_signals_params(self) -> Dict[str, Any]:
        """Get parameters for time decay signal generation."""
        return {
            "default_thresholds": {
                "d_tdpi_thresh": 0.3,
                "vci_0dte_thresh": 0.4
            },
            "regime_thresholds": {
                "REGIME_FINAL_HOUR": {
                    "d_tdpi_thresh": 0.2,  # Lower threshold near close
                    "vci_0dte_thresh": 0.3
                },
                "REGIME_OPENING": {
                    "d_tdpi_thresh": 0.4,  # Higher threshold at open
                    "vci_0dte_thresh": 0.5
                }
            }
        }

    def _get_complex_signals_params(self) -> Dict[str, Any]:
        """Get parameters for complex signal generation."""
        return {
            "default_thresholds": {
                "a_ssi_thresh": 0.35,
                "dwfd_z_thresh": 1.6
            },
            "regime_thresholds": {
                "REGIME_TRENDING": {
                    "a_ssi_thresh": 0.4,  # Higher stability expected in trends
                    "dwfd_z_thresh": 1.5
                },
                "REGIME_RANGE_BOUND": {
                    "a_ssi_thresh": 0.2,  # Lower stability expected in ranges
                    "dwfd_z_thresh": 2.0
                }
            }
        }