"""
AI Intelligence Database Integration for EOTS v2.5
=================================================

This module integrates the Pydantic AI Intelligence Engine with the
Supabase AI Intelligence Database, providing seamless persistence,
learning, and evolution capabilities for AI agents.

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "SENTIENT DATABASE INTEGRATION"
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# Import the AI Intelligence Database Manager
from .ai_intelligence_database_manager import (
    AIIntelligenceDatabaseManager,
    AIAgentRecord,
    AILearningSession,
    AIMemoryRecord,
    AIAdaptiveThreshold,
    AIPerformanceMetrics
)

# Import the Pydantic AI Intelligence Engine
from dashboard_application.modes.ai_dashboard.intelligence import (
    PydanticAIIntelligenceEngine,
    MarketMetrics,
    AIInsight,
    RegimeAnalysis,
    ConfidenceAssessment
)

logger = logging.getLogger(__name__)

class AIIntelligenceDatabaseIntegration:
    """
    INTEGRATION LAYER FOR AI INTELLIGENCE AND DATABASE
    
    This class provides seamless integration between the Pydantic AI
    Intelligence Engine and the Supabase AI Intelligence Database.
    """
    
    def __init__(self, db_config: Optional[Dict[str, Any]] = None):
        self.db_manager = AIIntelligenceDatabaseManager(db_config)
        self.logger = logger.getChild(self.__class__.__name__)
        self.agent_id_mapping = {}  # Map agent names to database IDs
        self.is_initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the database integration."""
        try:
            # Initialize database connection
            if not await self.db_manager.initialize_connection():
                self.logger.error("Failed to initialize database connection")
                return False
            
            # Register or retrieve existing AI agents
            await self._register_ai_agents()
            
            # Load adaptive thresholds
            await self._load_adaptive_thresholds()
            
            self.is_initialized = True
            self.logger.info("🏛️ AI Intelligence Database Integration initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI Intelligence Database Integration: {e}")
            return False
    
    async def _register_ai_agents(self):
        """Register the core AI agents in the database."""
        agents_to_register = [
            {
                "agent_name": "MarketAnalystAgent",
                "agent_type": "market_analyst",
                "specialization": "Institutional flow patterns and gamma positioning effects",
                "capabilities": {
                    "pattern_recognition": True,
                    "flow_analysis": True,
                    "institutional_behavior": True,
                    "gamma_positioning": True
                },
                "configuration": {
                    "confidence_threshold": 0.7,
                    "max_retries": 2,
                    "learning_enabled": True,
                    "pattern_recognition_depth": "advanced"
                }
            },
            {
                "agent_name": "RegimeAnalystAgent",
                "agent_type": "regime_analyst",
                "specialization": "Market regime detection and transition prediction",
                "capabilities": {
                    "regime_detection": True,
                    "transition_prediction": True,
                    "regime_validation": True,
                    "multi_timeframe_analysis": True
                },
                "configuration": {
                    "confidence_threshold": 0.75,
                    "transition_sensitivity": 0.8,
                    "regime_validation_enabled": True,
                    "max_retries": 2
                }
            },
            {
                "agent_name": "ConfidenceCalculatorAgent",
                "agent_type": "confidence_calculator",
                "specialization": "Confidence calibration and ensemble validation",
                "capabilities": {
                    "confidence_calibration": True,
                    "self_validation": True,
                    "ensemble_methods": True,
                    "historical_accuracy_tracking": True
                },
                "configuration": {
                    "confidence_threshold": 0.8,
                    "self_validation_enabled": True,
                    "historical_accuracy_weight": 0.3,
                    "max_retries": 1
                }
            }
        ]
        
        for agent_config in agents_to_register:
            try:
                # Check if agent already exists
                existing_agents = await self.db_manager.connection_pool.fetch(
                    "SELECT id FROM ai_agents WHERE agent_name = $1",
                    agent_config["agent_name"]
                )
                
                if existing_agents:
                    agent_id = str(existing_agents[0]['id'])
                    self.logger.debug(f"Found existing agent: {agent_config['agent_name']} ({agent_id})")
                else:
                    # Register new agent
                    agent_record = AIAgentRecord(**agent_config)
                    agent_id = await self.db_manager.register_agent(agent_record)
                    self.logger.info(f"🤖 Registered new agent: {agent_config['agent_name']} ({agent_id})")
                
                self.agent_id_mapping[agent_config["agent_name"]] = agent_id
                
            except Exception as e:
                self.logger.error(f"Failed to register agent {agent_config['agent_name']}: {e}")
    
    async def _load_adaptive_thresholds(self):
        """Load adaptive thresholds for all agents."""
        for agent_name, agent_id in self.agent_id_mapping.items():
            try:
                thresholds = await self.db_manager.get_adaptive_thresholds(agent_id)
                self.logger.debug(f"Loaded {len(thresholds)} thresholds for {agent_name}")
            except Exception as e:
                self.logger.error(f"Failed to load thresholds for {agent_name}: {e}")
    
    async def record_insight_generation(self, agent_name: str, market_metrics: MarketMetrics, 
                                      insights: List[AIInsight]) -> bool:
        """Record insight generation session in the database."""
        if not self.is_initialized:
            return False
            
        try:
            agent_id = self.agent_id_mapping.get(agent_name)
            if not agent_id:
                self.logger.warning(f"Agent {agent_name} not found in mapping")
                return False
            
            # Create learning session record
            session = AILearningSession(
                agent_id=agent_id,
                session_type="insight_generation",
                market_context={
                    "symbol": market_metrics.symbol,
                    "regime": market_metrics.current_regime,
                    "timestamp": market_metrics.timestamp.isoformat()
                },
                input_data=market_metrics.model_dump(),
                output_data={
                    "insights_count": len(insights),
                    "insights": [insight.model_dump() for insight in insights],
                    "avg_confidence": sum(i.confidence for i in insights) / len(insights) if insights else 0
                },
                confidence_score=sum(i.confidence for i in insights) / len(insights) if insights else 0
            )
            
            session_id = await self.db_manager.record_learning_session(session)
            
            # Store individual insights
            for insight in insights:
                await self._store_insight_record(agent_id, session_id, market_metrics.symbol, insight)
            
            # Store patterns as memories
            await self._extract_and_store_patterns(agent_id, market_metrics, insights)
            
            self.logger.debug(f"📚 Recorded insight generation for {agent_name}: {len(insights)} insights")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to record insight generation for {agent_name}: {e}")
            return False
    
    async def _store_insight_record(self, agent_id: str, session_id: str, symbol: str, insight: AIInsight):
        """Store individual insight record."""
        try:
            async with self.db_manager.connection_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO ai_insights_history (
                        agent_id, session_id, symbol, insight_text, insight_type,
                        confidence_score, reasoning, supporting_metrics, risk_level,
                        actionability_score, insight_timestamp
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
                """,
                agent_id, session_id, symbol, insight.insight_text, "market_analysis",
                insight.confidence, insight.reasoning, json.dumps(insight.metric_basis),
                insight.risk_level, insight.actionability)
                
        except Exception as e:
            self.logger.error(f"Failed to store insight record: {e}")
    
    async def _extract_and_store_patterns(self, agent_id: str, market_metrics: MarketMetrics, 
                                        insights: List[AIInsight]):
        """Extract and store patterns as memories."""
        try:
            # Create pattern signature based on market conditions
            pattern_signature = f"{market_metrics.current_regime}_{market_metrics.symbol}_{len(insights)}_insights"
            
            # Calculate pattern success probability based on confidence
            avg_confidence = sum(i.confidence for i in insights) / len(insights) if insights else 0
            
            memory = AIMemoryRecord(
                agent_id=agent_id,
                memory_type="insight_pattern",
                memory_category=market_metrics.current_regime,
                pattern_signature=pattern_signature,
                pattern_data={
                    "market_metrics": market_metrics.model_dump(),
                    "insights_generated": len(insights),
                    "avg_confidence": avg_confidence,
                    "high_confidence_insights": sum(1 for i in insights if i.confidence > 0.8),
                    "risk_distribution": {level: sum(1 for i in insights if i.risk_level == level) 
                                        for level in ["LOW", "MODERATE", "HIGH", "EXTREME"]}
                },
                success_rate=avg_confidence,
                confidence_level=avg_confidence
            )
            
            await self.db_manager.store_memory(memory)
            
        except Exception as e:
            self.logger.error(f"Failed to extract and store patterns: {e}")
    
    async def record_confidence_assessment(self, agent_name: str, market_metrics: MarketMetrics,
                                         assessment: ConfidenceAssessment) -> bool:
        """Record confidence assessment session."""
        if not self.is_initialized:
            return False
            
        try:
            agent_id = self.agent_id_mapping.get(agent_name)
            if not agent_id:
                return False
            
            session = AILearningSession(
                agent_id=agent_id,
                session_type="confidence_assessment",
                market_context={
                    "symbol": market_metrics.symbol,
                    "regime": market_metrics.current_regime
                },
                input_data=market_metrics.model_dump(),
                output_data=assessment.model_dump(),
                confidence_score=assessment.overall_confidence
            )
            
            await self.db_manager.record_learning_session(session)
            
            # Store confidence calibration pattern
            await self._store_confidence_pattern(agent_id, market_metrics, assessment)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to record confidence assessment for {agent_name}: {e}")
            return False
    
    async def _store_confidence_pattern(self, agent_id: str, market_metrics: MarketMetrics,
                                      assessment: ConfidenceAssessment):
        """Store confidence assessment pattern as memory."""
        try:
            pattern_signature = f"confidence_{market_metrics.current_regime}_{assessment.overall_confidence:.2f}"
            
            memory = AIMemoryRecord(
                agent_id=agent_id,
                memory_type="confidence_pattern",
                memory_category="confidence_calibration",
                pattern_signature=pattern_signature,
                pattern_data={
                    "market_conditions": market_metrics.model_dump(),
                    "confidence_breakdown": assessment.model_dump(),
                    "calibration_factors": {
                        "signal_strength": assessment.signal_strength_factor,
                        "data_quality": assessment.data_quality_factor,
                        "system_health": assessment.system_health_factor,
                        "historical_accuracy": assessment.historical_accuracy_factor,
                        "ensemble_agreement": assessment.ensemble_agreement_factor
                    }
                },
                success_rate=assessment.overall_confidence,
                confidence_level=assessment.overall_confidence
            )
            
            await self.db_manager.store_memory(memory)
            
        except Exception as e:
            self.logger.error(f"Failed to store confidence pattern: {e}")
    
    async def get_adaptive_thresholds(self, agent_name: str) -> Dict[str, float]:
        """Get adaptive thresholds for an agent."""
        if not self.is_initialized:
            return {}
            
        agent_id = self.agent_id_mapping.get(agent_name)
        if not agent_id:
            return {}
        
        return await self.db_manager.get_adaptive_thresholds(agent_id)
    
    async def update_adaptive_threshold(self, agent_name: str, threshold_name: str,
                                      new_value: float, reason: str) -> bool:
        """Update an adaptive threshold."""
        if not self.is_initialized:
            return False
            
        agent_id = self.agent_id_mapping.get(agent_name)
        if not agent_id:
            return False
        
        return await self.db_manager.update_adaptive_threshold(agent_id, threshold_name, new_value, reason)
    
    async def get_learning_insights(self, agent_name: str, days: int = 7) -> List[Dict[str, Any]]:
        """Get recent learning insights for an agent."""
        if not self.is_initialized:
            return []
            
        agent_id = self.agent_id_mapping.get(agent_name)
        if not agent_id:
            return []
        
        try:
            sessions = await self.db_manager.get_learning_history(agent_id, limit=50)
            
            # Filter recent sessions
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_sessions = [s for s in sessions if s.session_timestamp >= cutoff_date]
            
            insights = []
            for session in recent_sessions:
                insights.append({
                    "session_type": session.session_type,
                    "confidence": session.confidence_score,
                    "accuracy": session.accuracy_score,
                    "timestamp": session.session_timestamp,
                    "market_context": session.market_context,
                    "learning_extracted": session.learning_extracted
                })
            
            return insights
            
        except Exception as e:
            self.logger.error(f"Failed to get learning insights for {agent_name}: {e}")
            return []
    
    async def record_daily_performance(self, agent_name: str, symbol: str, 
                                     performance_data: Dict[str, Any]) -> bool:
        """Record daily performance metrics for an agent."""
        if not self.is_initialized:
            return False
            
        agent_id = self.agent_id_mapping.get(agent_name)
        if not agent_id:
            return False
        
        try:
            metrics = AIPerformanceMetrics(
                agent_id=agent_id,
                metric_date=datetime.now(),
                symbol=symbol,
                **performance_data
            )
            
            return await self.db_manager.record_performance_metrics(metrics)
            
        except Exception as e:
            self.logger.error(f"Failed to record daily performance for {agent_name}: {e}")
            return False
    
    async def get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system overview."""
        if not self.is_initialized:
            return {}
        
        return await self.db_manager.get_system_overview()
    
    async def cleanup_and_maintain(self) -> bool:
        """Perform database cleanup and maintenance."""
        if not self.is_initialized:
            return False
        
        try:
            # Record system health
            await self.db_manager.record_system_health()
            
            # Cleanup old data
            await self.db_manager.cleanup_old_data(days_to_keep=90)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to perform cleanup and maintenance: {e}")
            return False
    
    async def close(self):
        """Close the database integration."""
        if self.db_manager:
            await self.db_manager.close_connection()
        self.is_initialized = False
        self.logger.info("🏛️ AI Intelligence Database Integration closed")

# ===== GLOBAL INTEGRATION INSTANCE =====

_ai_db_integration = None

async def get_ai_database_integration(db_config: Optional[Dict[str, Any]] = None) -> AIIntelligenceDatabaseIntegration:
    """Get or create the global AI database integration instance."""
    global _ai_db_integration
    
    if _ai_db_integration is None:
        _ai_db_integration = AIIntelligenceDatabaseIntegration(db_config)
        await _ai_db_integration.initialize()
    
    return _ai_db_integration
