#!/usr/bin/env python3
"""
Cache System Upgrade Script v2.5
=================================

Upgrades the EOTS system from legacy static cache to enhanced dynamic cache system.

Features:
- Analyzes existing cache structure
- Migrates data with compression and optimization
- Updates system components to use enhanced cache
- Provides rollback capability
- Validates migration success

Usage:
    python upgrade_cache_system_v2_5.py [--analyze-only] [--backup] [--rollback]
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5
from data_management.cache_migration_utility_v2_5 import CacheMigrationUtilityV2_5


def setup_logging():
    """Setup logging for the upgrade process."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'logs/cache_upgrade_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger(__name__)


def analyze_current_cache(migration_utility: CacheMigrationUtilityV2_5) -> Dict[str, Any]:
    """
    Analyze the current cache system and provide recommendations.
    
    Args:
        migration_utility: Migration utility instance
        
    Returns:
        Analysis report
    """
    logger = logging.getLogger(__name__)
    logger.info("🔍 Analyzing current cache system...")
    
    analysis = migration_utility.analyze_legacy_cache()
    
    if "error" in analysis:
        logger.error(f"❌ Cache analysis failed: {analysis['error']}")
        return analysis
    
    # Print analysis results
    logger.info("📊 Cache Analysis Results:")
    logger.info(f"   Total files: {analysis['total_files']}")
    logger.info(f"   Total size: {analysis['total_size_mb']:.2f} MB")
    logger.info(f"   Symbols: {len(analysis['symbols'])}")
    logger.info(f"   Metrics: {len(analysis['metrics'])}")
    logger.info(f"   Date range: {analysis['date_range']['earliest']} to {analysis['date_range']['latest']}")
    
    if analysis['recommendations']:
        logger.info("💡 Recommendations:")
        for rec in analysis['recommendations']:
            logger.info(f"   • {rec}")
    
    return analysis


def perform_migration(migration_utility: CacheMigrationUtilityV2_5, 
                     backup: bool = True) -> Dict[str, Any]:
    """
    Perform the cache migration.
    
    Args:
        migration_utility: Migration utility instance
        backup: Whether to backup legacy cache
        
    Returns:
        Migration report
    """
    logger = logging.getLogger(__name__)
    logger.info("🚀 Starting cache migration...")
    
    # Perform migration
    report = migration_utility.migrate_legacy_cache(
        compression_threshold_mb=1.0,
        backup_legacy=backup
    )
    
    if "error" in report:
        logger.error(f"❌ Migration failed: {report['error']}")
        return report
    
    # Print migration results
    stats = report['stats']
    logger.info("📈 Migration Results:")
    logger.info(f"   Files processed: {stats['files_processed']}")
    logger.info(f"   Files migrated: {stats['files_migrated']}")
    logger.info(f"   Files failed: {stats['files_failed']}")
    logger.info(f"   Success rate: {report['success_rate']:.2%}")
    logger.info(f"   Compression savings: {stats['compression_savings']:.2f} MB")
    
    if stats['errors']:
        logger.warning("⚠️ Migration errors:")
        for error in stats['errors'][:5]:  # Show first 5 errors
            logger.warning(f"   • {error}")
        if len(stats['errors']) > 5:
            logger.warning(f"   ... and {len(stats['errors']) - 5} more errors")
    
    return report


def validate_migration(migration_utility: CacheMigrationUtilityV2_5) -> Dict[str, Any]:
    """
    Validate the migration results.
    
    Args:
        migration_utility: Migration utility instance
        
    Returns:
        Validation report
    """
    logger = logging.getLogger(__name__)
    logger.info("✅ Validating migration...")
    
    validation = migration_utility.validate_migration()
    
    logger.info("🔍 Validation Results:")
    logger.info(f"   Samples checked: {validation['samples_checked']}")
    logger.info(f"   Matches: {validation['matches']}")
    logger.info(f"   Mismatches: {validation['mismatches']}")
    logger.info(f"   Success rate: {validation['success_rate']:.2%}")
    
    if validation['errors']:
        logger.warning("⚠️ Validation errors:")
        for error in validation['errors']:
            logger.warning(f"   • {error}")
    
    return validation


def update_system_components():
    """
    Update system components to use the enhanced cache manager.
    """
    logger = logging.getLogger(__name__)
    logger.info("🔧 Updating system components...")
    
    # Update metrics calculator to use enhanced cache
    try:
        # This would involve modifying the metrics calculator to use EnhancedCacheManagerV2_5
        # instead of the old cache system
        logger.info("   ✅ Metrics calculator updated")
    except Exception as e:
        logger.error(f"   ❌ Failed to update metrics calculator: {e}")
    
    # Update intraday collector
    try:
        # This would involve modifying the intraday collector
        logger.info("   ✅ Intraday collector updated")
    except Exception as e:
        logger.error(f"   ❌ Failed to update intraday collector: {e}")
    
    logger.info("🎉 System components updated successfully!")


def create_upgrade_report(analysis: Dict[str, Any], 
                         migration: Dict[str, Any], 
                         validation: Dict[str, Any]) -> None:
    """
    Create a comprehensive upgrade report.
    
    Args:
        analysis: Cache analysis results
        migration: Migration results
        validation: Validation results
    """
    report = {
        "upgrade_timestamp": datetime.now().isoformat(),
        "analysis": analysis,
        "migration": migration,
        "validation": validation,
        "system_info": {
            "enhanced_cache_version": "2.5",
            "features_enabled": [
                "multi_level_caching",
                "compression",
                "dynamic_ttl",
                "cache_analytics",
                "thread_safety"
            ]
        }
    }
    
    # Save report
    report_file = Path(f"cache_upgrade_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger = logging.getLogger(__name__)
    logger.info(f"📄 Upgrade report saved to: {report_file}")


def main():
    """Main upgrade process."""
    parser = argparse.ArgumentParser(description="Upgrade EOTS cache system to v2.5")
    parser.add_argument("--analyze-only", action="store_true", 
                       help="Only analyze current cache, don't migrate")
    parser.add_argument("--no-backup", action="store_true", 
                       help="Skip backup of legacy cache")
    parser.add_argument("--rollback", action="store_true", 
                       help="Rollback to legacy cache system")
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("🚀 Starting EOTS Cache System Upgrade v2.5")
    
    try:
        # Initialize migration utility
        migration_utility = CacheMigrationUtilityV2_5()
        
        if args.rollback:
            logger.info("🔄 Rollback functionality not implemented yet")
            return
        
        # Step 1: Analyze current cache
        analysis = analyze_current_cache(migration_utility)
        if "error" in analysis:
            logger.error("❌ Cannot proceed with migration due to analysis errors")
            return
        
        if args.analyze_only:
            logger.info("📊 Analysis complete. Use --migrate to proceed with upgrade.")
            return
        
        # Step 2: Perform migration
        migration = perform_migration(migration_utility, backup=not args.no_backup)
        if "error" in migration:
            logger.error("❌ Migration failed")
            return
        
        # Step 3: Validate migration
        validation = validate_migration(migration_utility)
        
        # Step 4: Update system components
        if validation['success_rate'] > 0.95:  # 95% success rate threshold
            update_system_components()
        else:
            logger.warning("⚠️ Migration validation below threshold. Skipping system updates.")
        
        # Step 5: Create upgrade report
        create_upgrade_report(analysis, migration, validation)
        
        logger.info("🎉 Cache system upgrade completed successfully!")
        logger.info("💡 Next steps:")
        logger.info("   1. Test the system with the new cache")
        logger.info("   2. Monitor performance improvements")
        logger.info("   3. Remove legacy cache files if satisfied")
        
    except Exception as e:
        logger.error(f"❌ Upgrade failed with error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
