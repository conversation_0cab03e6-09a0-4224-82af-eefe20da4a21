# dashboard_application/modes/flow_mode_display_v2_5.py
# EOTS v2.5 - S-GRADE, AUTHORITATIVE FLOW MODE DISPLAY
# Enhanced with SGDHP, IVSDH, and UGCH Heatmaps

import pandas as pd
import plotly.graph_objects as go
from dash import dcc, html
import dash_bootstrap_components as dbc
import numpy as np
from typing import Optional
from dash.development.base_component import Component
from pydantic import ValidationError

from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5
from dashboard_application.utils_dashboard_v2_5 import (
    create_empty_figure, apply_dark_theme_template, 
    add_price_line, add_timestamp_annotation, PLOTLY_TEMPLATE
)
import logging

logger = logging.getLogger(__name__)

# --- Helper Functions for Chart Generation ---

def _about_section(text, component_id):
    # Collapsible about section with toggle button
    return html.Div([
        dbc.Button(
            "About",
            id={"type": "about-toggle-btn", "section": component_id},
            color="info",
            size="sm",
            className="mb-2",
            n_clicks=0,
        ),
        dbc.Collapse(
            html.Div(text, className="about-section mb-2"),
            id={"type": "about-collapse", "section": component_id},
            is_open=False,
        ),
        # Note: Wire up a Dash callback to toggle is_open on button click for each card.
        # Example callback signature:
        # @app.callback(
        #   Output({"type": "about-collapse", "section": MATCH}, "is_open"),
        #   Input({"type": "about-toggle-btn", "section": MATCH}, "n_clicks"),
        #   State({"type": "about-collapse", "section": MATCH}, "is_open"),
        # )
        # def toggle_about(n, is_open):
        #     if n:
        #         return not is_open
        #     return is_open
    ], id=f"about-{component_id}", className="about-section-container mb-2")

def _wrap_chart_in_card(chart_component, about_text, component_id):
    return dbc.Card([
        dbc.CardHeader(html.H5(component_id.replace('-', ' ').title())),
        dbc.CardBody([
            _about_section(about_text, component_id),
            chart_component
        ])
    ], className="mb-4", id=f"card-{component_id}")

def _generate_net_value_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """
    Generates a heatmap of net value pressure by strike and option type.
    Fixed to eliminate compression/distortion issues and follow system guide NVP calculations.
    """
    chart_name = "Net Value by Strike"
    about_text = (
        "💰 Net Value by Strike (NVP): This shows the TOTAL DOLLAR VALUE of options positioning at each strike - your MONEY FLOW MAP. "
        "GREEN = NET BUYING VALUE (institutional accumulation zones). RED = NET SELLING VALUE (distribution/hedging zones). "
        "INTENSITY = Dollar magnitude - darker colors mean LARGER money flows. This is calculated as the sum of Black-Scholes values "
        "for all contracts at each strike. 💡 TRADING INSIGHT: LARGE GREEN zones = where big money is ACCUMULATING positions (potential support). "
        "LARGE RED zones = where institutions are DISTRIBUTING or hedging (potential resistance). The BIGGEST value concentrations often become "
        "MAGNET LEVELS where price gravitates. When price approaches a large NVP zone, expect INCREASED VOLATILITY as dealers adjust hedges. "
        "Multiple green levels below = BULLISH value structure. Multiple red levels above = BEARISH value structure. "
        "This updates in real-time as new options trades flow through the system!"
    )

    try:
        # First try to get NVP data from strike-level metrics (preferred)
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        if strike_data is not None:
            df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
            if not df_strike.empty and 'nvp_at_strike' in df_strike.columns:
                # Use pre-calculated NVP data
                df_plot = df_strike.dropna(subset=['strike', 'nvp_at_strike']).sort_values('strike')
                if not df_plot.empty:
                    # Create single-row heatmap for NVP values
                    strikes = df_plot['strike'].values
                    nvp_values = df_plot['nvp_at_strike'].values

                    # Create proper horizontal heatmap: strikes on Y-axis, categories on X-axis
                    # Create meaningful categories for horizontal display
                    categories = ['Net Value', 'Call Flow', 'Put Flow', 'Total Flow', 'Value Density']

                    # Create data matrix: each row is a strike, each column is a category
                    z_data = []
                    for nvp_val in nvp_values:
                        # Create different perspectives of the same NVP data
                        row = [
                            nvp_val,                    # Net Value
                            max(0, nvp_val),           # Call Flow (positive values only)
                            min(0, nvp_val),           # Put Flow (negative values only)
                            abs(nvp_val),              # Total Flow (absolute value)
                            nvp_val / 1000 if nvp_val != 0 else 0  # Value Density (scaled)
                        ]
                        z_data.append(row)

                    z_data = np.array(z_data)

                    fig = go.Figure(data=go.Heatmap(
                        z=z_data,
                        x=categories,
                        y=[f"${strike}" for strike in strikes],
                        colorscale=[[0, '#8B0000'], [0.2, '#DC143C'], [0.4, '#FF6B6B'], [0.5, '#2C2C2C'],
                                   [0.6, '#4CAF50'], [0.8, '#2E7D32'], [1, '#1B5E20']],
                        zmid=0,
                        hoverongaps=False,
                        hovertemplate='Strike: %{y}<br>Category: %{x}<br>Value: $%{z:,.0f}<extra></extra>',
                        colorbar=dict(
                            title=dict(text="NVP ($)", font=dict(color='white', size=12)),
                            x=1.02,
                            tickfont=dict(color='white', size=10),
                            bgcolor='rgba(0,0,0,0.8)',
                            bordercolor='white',
                            borderwidth=1
                        )
                    ))

                    # Calculate appropriate height based on number of strikes
                    num_strikes = len(strikes)
                    fig_height = max(400, min(800, num_strikes * 25 + 100))

                    fig.update_layout(
                        title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
                        height=fig_height,
                        template=PLOTLY_TEMPLATE,
                        xaxis_title="Flow Categories",
                        yaxis_title="Strike Price",
                        yaxis=dict(
                            type='category',
                            tickmode='linear',
                            showgrid=True,
                            gridcolor='rgba(128,128,128,0.2)',
                            tickfont=dict(size=10)
                        ),
                        margin=dict(l=80, r=100, t=80, b=60)
                    )

                    apply_dark_theme_template(fig)

                    # Price line removed to prevent heatmap distortion/compression
                    # add_price_line(fig, current_price)  # Commented out

                    add_timestamp_annotation(fig, bundle.bundle_timestamp)

                    return _wrap_chart_in_card(dcc.Graph(
                        figure=fig,
                        config={'displayModeBar': False, 'displaylogo': False},
                        style={'height': f'{fig_height}px', 'width': '100%'}
                    ), about_text, "net-value-heatmap")

        # Fallback: Calculate NVP from options chain data using system guide formula
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if options_data is None:
            logger.error(f"[{chart_name}] options_data_with_metrics missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("Options data unavailable.", color="danger"), about_text, "net-value-heatmap")

        df_chain = pd.DataFrame([c.model_dump() for c in options_data])
        if df_chain.empty or 'value_bs' not in df_chain.columns:
            fig_height = 400
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "Per-contract 'value_bs' data not available."),
                config={'displayModeBar': False, 'displaylogo': False}
            ), about_text, "net-value-heatmap")

        # Calculate NVP according to system guide: NVP_at_strike = Sum of value_bs for all contracts at that strike
        df_plot = df_chain.dropna(subset=['strike', 'opt_kind', 'value_bs'])
        pivot_df = df_plot.pivot_table(index='strike', columns='opt_kind', values='value_bs', aggfunc='sum').fillna(0)

        if 'call' not in pivot_df.columns:
            pivot_df['call'] = 0
        if 'put' not in pivot_df.columns:
            pivot_df['put'] = 0
        pivot_df = pivot_df[['put', 'call']].sort_index(ascending=True)

        if pivot_df.empty:
            fig_height = 400
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No data to pivot."),
                config={'displayModeBar': False, 'displaylogo': False}
            ), about_text, "net-value-heatmap")

        # Fixed height calculation to prevent compression
        num_strikes = len(pivot_df.index)
        base_height = 400
        row_height = max(15, min(25, 600 // max(num_strikes, 1)))  # Adaptive row height with limits
        fig_height = min(800, max(base_height, num_strikes * row_height + 150))  # Cap at 800px

        fig = go.Figure(data=go.Heatmap(
            z=pivot_df.values,
            x=pivot_df.columns.str.capitalize(),
            y=pivot_df.index.astype(str),
            colorscale=[[0, '#8B0000'], [0.2, '#DC143C'], [0.4, '#FF6B6B'], [0.5, '#2C2C2C'],
                       [0.6, '#4CAF50'], [0.8, '#2E7D32'], [1, '#1B5E20']],  # Dark red to dark green
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{y}<br>Type: %{x}<br>Net Value: $%{z:,.0f}<extra></extra>',
            ygap=1,
            colorbar=dict(
                title=dict(text="Net Value ($)", font=dict(color='white', size=12)),
                x=1.02,
                tickfont=dict(color='white', size=10),
                bgcolor='rgba(0,0,0,0.8)',
                bordercolor='white',
                borderwidth=1
            )
        ))

        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            yaxis=dict(
                type='category',
                title="Strike Price",
                tickmode='linear',
                showgrid=True,
                gridcolor='rgba(128,128,128,0.2)',
                tickfont=dict(size=10),
                automargin=True
            ),
            xaxis=dict(title="Option Type"),
            margin=dict(l=60, r=80, t=80, b=60)
        )

        apply_dark_theme_template(fig)

        # Add current price line
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            y_values_numeric = pivot_df.index.to_series().astype(float)
            if not y_values_numeric.empty:
                closest_strike_val = y_values_numeric.iloc[(y_values_numeric - current_price).abs().argmin()]
                y_coordinate_str = str(closest_strike_val)

                fig.add_hline(
                    y=y_coordinate_str,
                    line_dash="dash",
                    line_color="white",
                    line_width=2
                )

                fig.add_annotation(
                    x=1.02,
                    y=y_coordinate_str,
                    xref="paper",
                    yref="y",
                    text=f"ATM ≈ {current_price:.2f}",
                    showarrow=False,
                    xanchor="left",
                    yanchor="middle",
                    font=dict(color="white", size=10),
                    bgcolor="rgba(0,0,0,0.5)"
                )

        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig_height = 400
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': f'{fig_height}px', 'width': '100%'}
    ), about_text, "net-value-heatmap")

def _generate_greek_flow_chart(bundle: FinalAnalysisBundleV2_5, metric: str, title: str, color: str) -> Component:
    """Generic helper to create a bar chart for a net customer Greek flow metric."""
    chart_name = f"{title} by Strike"
    if 'Delta' in title:
        about_text = (
            "🎯 Delta Flow by Strike: This shows NET DIRECTIONAL BETTING at each strike - your MOMENTUM PREDICTOR. "
            "🟢 GREEN bars = NET DELTA BUYING (bullish directional bets). 🔴 RED bars = NET DELTA SELLING (bearish directional bets). "
            "BAR HEIGHT = Magnitude of directional exposure. Delta flow represents the SPEED of price movement dealers must hedge against. "
            "💡 TRADING INSIGHT: LARGE GREEN deltas = expect UPWARD price pressure as dealers buy stock to hedge. "
            "LARGE RED deltas = expect DOWNWARD pressure as dealers sell stock. The BIGGEST delta concentrations often predict "
            "INTRADAY DIRECTION. When delta flow FLIPS from positive to negative (or vice versa) = MAJOR MOMENTUM SHIFT. "
            "Multiple green levels = SUSTAINED BULLISH pressure. Multiple red levels = SUSTAINED BEARISH pressure. "
            "Watch for delta flow DIVERGENCE from price - often signals upcoming reversals!"
        )
    elif 'Gamma' in title:
        about_text = (
            "⚡ Gamma Flow by Strike: This shows ACCELERATION ZONES - where price moves will AMPLIFY. "
            "🔵 BLUE bars = NET GAMMA BUYING (dealers must hedge MORE as price moves). 🟣 PURPLE bars = NET GAMMA SELLING (dealers hedge LESS). "
            "BAR HEIGHT = Hedging acceleration magnitude. Gamma is the 'TURBO BOOST' of options - it makes price moves FASTER and BIGGER. "
            "💡 TRADING INSIGHT: LARGE BLUE gamma = EXPLOSIVE MOVE POTENTIAL in either direction (high volatility zones). "
            "LARGE PURPLE gamma = DAMPENING EFFECT (low volatility zones). When price approaches big gamma levels, expect "
            "INCREASED VOLATILITY as dealers frantically adjust hedges. GAMMA SQUEEZES occur when price moves through large positive gamma - "
            "creates FEEDBACK LOOPS of buying/selling. Use gamma to predict WHERE volatility will spike!"
        )
    elif 'Vega' in title:
        about_text = (
            "🌊 Vega Flow by Strike: This shows VOLATILITY DEMAND/SUPPLY - your IV EXPANSION/CONTRACTION predictor. "
            "🟠 ORANGE bars = NET VEGA BUYING (volatility demand, IV expansion pressure). 🔥 RED-ORANGE bars = NET VEGA SELLING (vol supply, IV contraction). "
            "BAR HEIGHT = Volatility sensitivity magnitude. Vega flow shows where institutions are BETTING ON or HEDGING AGAINST volatility changes. "
            "💡 TRADING INSIGHT: LARGE ORANGE vega = expect IMPLIED VOLATILITY to RISE (options get more expensive). "
            "LARGE RED vega = expect IV to FALL (options get cheaper). When big events approach, watch vega flow for "
            "INSTITUTIONAL POSITIONING. Positive vega clusters = 'VOLATILITY MAGNETS' where IV spikes. "
            "Negative vega = 'VOLATILITY SINKS' where IV gets crushed. Trade options strategies ALIGNED with vega flow direction!"
        )
    else:
        about_text = (
            f"💡 {title} by Strike: Shows net customer {title.lower()} flow at each strike. "
            f"🟢 Positive bars = NET BUYING flow. 🔴 Negative bars = NET SELLING flow. "
            f"⚫ Gray bars = NEUTRAL (no flow). Use this to identify where the largest institutional bets are being placed."
        )
    fig = go.Figure()
    
    try:
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        if strike_data is None:
            logger.error(f"[{chart_name}] strike_level_data_with_metrics missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert(f"Strike data unavailable for {title}.", color="danger"), about_text, f"{title.lower()}-flow-chart")
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        
        logger.info(f"[{chart_name}] Strike data shape: {df_strike.shape}")
        logger.info(f"[{chart_name}] Available columns: {df_strike.columns.tolist()}")
        logger.info(f"[{chart_name}] Looking for metric: {metric}")
        
        if not df_strike.empty:
            if metric in df_strike.columns:
                logger.info(f"[{chart_name}] Metric column sample values: {df_strike[metric].head(10).tolist()}")
                logger.info(f"[{chart_name}] Metric column null count: {df_strike[metric].isnull().sum()}")
                logger.info(f"[{chart_name}] Metric column non-null count: {df_strike[metric].notnull().sum()}")
                
                df_plot = df_strike.dropna(subset=['strike', metric]).sort_values('strike')
                logger.info(f"[{chart_name}] Plot data shape after filtering: {df_plot.shape}")
                
                if df_plot.empty:
                    logger.info(f"[{chart_name}] Trying fallback: filling NaN values with 0")
                    df_fallback = df_strike.dropna(subset=['strike']).copy()
                    df_fallback[metric] = df_fallback[metric].fillna(0)
                    df_plot = df_fallback.sort_values('strike')
                    logger.info(f"[{chart_name}] Fallback data shape: {df_plot.shape}")
                
                if not df_plot.empty:
                    non_zero_count = (df_plot[metric] != 0).sum()
                    logger.info(f"[{chart_name}] Non-zero values: {non_zero_count}")

                    # Create color array based on positive/negative values
                    colors = []
                    for value in df_plot[metric]:
                        if value > 0:
                            if 'Delta' in title:
                                colors.append('#00E676')  # Bright green for positive delta
                            elif 'Gamma' in title:
                                colors.append('#2196F3')  # Bright blue for positive gamma
                            elif 'Vega' in title:
                                colors.append('#FF9800')  # Bright orange for positive vega
                            else:
                                colors.append('#4CAF50')  # Default green for positive
                        elif value < 0:
                            if 'Delta' in title:
                                colors.append('#F44336')  # Bright red for negative delta
                            elif 'Gamma' in title:
                                colors.append('#9C27B0')  # Purple for negative gamma
                            elif 'Vega' in title:
                                colors.append('#FF5722')  # Deep orange for negative vega
                            else:
                                colors.append('#F44336')  # Default red for negative
                        else:
                            colors.append('#424242')  # Dark gray for zero

                    fig.add_trace(go.Bar(
                        x=df_plot['strike'],
                        y=df_plot[metric],
                        name=title,
                        marker_color=colors,
                        marker_line=dict(color='white', width=0.5),  # Add white border for better contrast
                        hovertemplate=f'Strike: %{{x}}<br>{title}: %{{y:.4f}}<br>Direction: %{{customdata}}<extra></extra>',
                        customdata=['Positive' if v > 0 else 'Negative' if v < 0 else 'Neutral' for v in df_plot[metric]]
                    ))
                    fig.update_layout(
                        title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
                        height=400,
                        template=PLOTLY_TEMPLATE,
                        showlegend=False,
                        xaxis_title="Strike Price",
                        yaxis_title=title,
                        margin=dict(l=60, r=60, t=80, b=60)
                    )
                    apply_dark_theme_template(fig)
                    current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                    if current_price is not None:
                        add_price_line(fig, current_price)
                    add_timestamp_annotation(fig, bundle.bundle_timestamp)
                else:
                    logger.warning(f"[{chart_name}] No valid data after filtering")
                    fig = create_empty_figure(chart_name, 400, "No valid data after filtering.")
            else:
                logger.warning(f"[{chart_name}] Metric '{metric}' not found in columns: {df_strike.columns.tolist()}")
                fig = _try_calculate_greek_flow_fallback(df_strike, bundle, metric, title, color, chart_name)
        else:
            logger.warning(f"[{chart_name}] Strike data is empty")
            fig = create_empty_figure(chart_name, 400, "Strike data is empty.")
    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, 400, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': '400px', 'width': '100%'}
    ), about_text, f"{title.lower()}-flow-chart")

def _generate_sgdhp_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates SGDHP (Strike-level Gamma Delta Hedging Pressure) heatmap."""
    chart_name = "SGDHP - Strike Gamma Delta Hedging Pressure"
    fig_height = config.get_setting("visualization_settings.dashboard.flow_mode_settings.sgdhp_heatmap.height", 500)
    about_text = (
        "🧮 SGDHP Heatmap (Super Gamma Delta Hedging Pressure): This is your ADVANCED HEDGING PRESSURE DETECTOR - shows where dealers face "
        "MAXIMUM STRESS. 🟣 PURPLE = LOW pressure (dealers comfortable). 🟡 AMBER = HIGH pressure (dealers scrambling to hedge). "
        "This combines GAMMA exposure, DELTA positioning, proximity to current price, and recent flow confirmation into ONE POWERFUL METRIC. "
        "💡 TRADING INSIGHT: HIGH SGDHP zones (bright amber) = EXPLOSIVE VOLATILITY POTENTIAL. These are strikes where dealers "
        "must hedge AGGRESSIVELY, creating FEEDBACK LOOPS that amplify price moves. When price approaches high SGDHP levels, expect "
        "INCREASED VOLATILITY, WIDER SPREADS, and ERRATIC PRICE ACTION. LOW SGDHP zones = 'CALM WATERS' with predictable dealer behavior. "
        "SGDHP SPIKES often predict INTRADAY VOLATILITY EXPLOSIONS. Use this to time volatility trades and avoid getting caught in "
        "dealer hedging storms. The HIGHEST SGDHP strike often becomes the day's PIVOT POINT!"
    )

    try:
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if strike_data is None or options_data is None:
            logger.error(f"[{chart_name}] Required data missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("SGDHP data unavailable.", color="danger"), about_text, "sgdhp-heatmap")
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        df_options = pd.DataFrame([c.model_dump() for c in options_data])
        
        if df_strike.empty or 'sgdhp_score_strike' not in df_strike.columns:
            if not df_options.empty:
                current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                if current_price is not None:
                    sgdhp_data = _calculate_sgdhp_fallback(df_options, current_price)
                    if sgdhp_data is not None:
                        df_strike = sgdhp_data
                    else:
                        return _wrap_chart_in_card(dcc.Graph(
                            figure=create_empty_figure(chart_name, fig_height, "SGDHP data not available."),
                            config={'displayModeBar': False, 'displaylogo': False}
                        ), about_text, "sgdhp-heatmap")
                else:
                    return _wrap_chart_in_card(dcc.Graph(
                        figure=create_empty_figure(chart_name, fig_height, "Current price not available for SGDHP calculation."),
                        config={'displayModeBar': False, 'displaylogo': False}
                    ), about_text, "sgdhp-heatmap")
            else:
                return _wrap_chart_in_card(dcc.Graph(
                    figure=create_empty_figure(chart_name, fig_height, "No options data available for SGDHP calculation."),
                    config={'displayModeBar': False, 'displaylogo': False}
                ), about_text, "sgdhp-heatmap")

        df_plot = df_strike.dropna(subset=['strike', 'sgdhp_score_strike']).sort_values('strike')
        
        if df_plot.empty:
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No valid SGDHP data to display."),
                config={'displayModeBar': False, 'displaylogo': False}
            ), about_text, "sgdhp-heatmap")

        strikes = df_plot['strike'].values
        sgdhp_values = np.array(df_plot['sgdhp_score_strike'].values)
        z_data = sgdhp_values.reshape(1, -1)
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=strikes,
            y=['SGDHP'],
            colorscale=[[0, '#1A0033'], [0.2, '#4A148C'], [0.4, '#7B1FA2'], [0.5, '#2C2C2C'],
                       [0.6, '#FF6F00'], [0.8, '#FF8F00'], [1, '#FFB300']],  # Purple to amber
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{x}<br>SGDHP Score: %{z:.3f}<br>Pressure: %{customdata}<extra></extra>',
            customdata=['High' if v > 0.5 else 'Medium' if v > 0 else 'Low' if v > -0.5 else 'Negative' for v in sgdhp_values],
            colorbar=dict(
                title=dict(text="SGDHP Score", font=dict(color='white', size=12)),
                x=1.02,
                tickfont=dict(color='white', size=10),
                bgcolor='rgba(0,0,0,0.8)',
                bordercolor='white',
                borderwidth=1
            )
        ))
        
        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Strike Price",
            yaxis_title=""
        )
        
        apply_dark_theme_template(fig)
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            add_price_line(fig, current_price)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': f'{fig_height}px', 'width': '100%'}
    ), about_text, "sgdhp-heatmap")

def _generate_ivsdh_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates IVSDH (Implied Volatility Surface Delta Hedging) heatmap."""
    chart_name = "IVSDH - IV Surface Delta Hedging"
    fig_height = config.get_setting("visualization_settings.dashboard.flow_mode_settings.ivsdh_heatmap.height", 500)
    about_text = (
        "🌈 IVSDH Heatmap (Integrated Volatility Surface Dynamics): This is your VOLATILITY COMPLEXITY RADAR - shows where "
        "volatility surface dynamics create MAXIMUM HEDGING CHALLENGES. 🔵 BLUE = LOW volatility tension (stable hedging). "
        "🔥 RED-ORANGE = HIGH volatility tension (chaotic hedging). This integrates VANNA (vol-delta sensitivity), VOMMA (vol-vol sensitivity), "
        "and CHARM (time-delta decay) into a UNIFIED COMPLEXITY SCORE. 💡 TRADING INSIGHT: HIGH IVSDH zones (red-orange) = "
        "VOLATILITY SURFACE STRESS POINTS where small price moves cause LARGE IMPLIED VOLATILITY SHIFTS. These are your "
        "VOLATILITY TRADE GOLDMINES - where IV can spike or crash rapidly. LOW IVSDH zones = stable vol environment. "
        "When IVSDH is high near current price = expect WILD IV SWINGS intraday. Use this to time volatility entries/exits "
        "and predict when options will become dramatically more/less expensive!"
    )

    try:
        # Get strike-level data with IVSDH scores
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)

        if strike_data is None or options_data is None:
            logger.error(f"[{chart_name}] Required data missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("IVSDH data unavailable.", color="danger"), about_text, "ivsdh-heatmap")

        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        df_options = pd.DataFrame([c.model_dump() for c in options_data])

        if df_strike.empty or 'ivsdh_score_strike' not in df_strike.columns:
            if not df_options.empty:
                ivsdh_data = _calculate_ivsdh_fallback(df_options)
                if ivsdh_data is not None:
                    # Create surface from fallback data
                    pivot_df = ivsdh_data.pivot_table(
                        index='dte_calc',
                        columns='strike',
                        values='ivsdh_score',
                        aggfunc='mean'
                    ).fillna(0)

                    if not pivot_df.empty:
                        fig = go.Figure(data=go.Heatmap(
                            z=pivot_df.values,
                            x=pivot_df.columns,
                            y=pivot_df.index,
                            colorscale=[[0, '#0D47A1'], [0.2, '#1976D2'], [0.4, '#42A5F5'], [0.5, '#2C2C2C'],
                                       [0.6, '#FF7043'], [0.8, '#F4511E'], [1, '#BF360C']],  # Blue to red-orange
                            zmid=0,
                            hoverongaps=False,
                            hovertemplate='Strike: %{x}<br>DTE: %{y}<br>IVSDH Score: %{z:.3f}<extra></extra>',
                            colorbar=dict(
                                title=dict(text="IVSDH Score", font=dict(color='white', size=12)),
                                x=1.02,
                                tickfont=dict(color='white', size=10),
                                bgcolor='rgba(0,0,0,0.8)',
                                bordercolor='white',
                                borderwidth=1
                            )
                        ))

                        fig.update_layout(
                            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
                            height=fig_height,
                            template=PLOTLY_TEMPLATE,
                            xaxis_title="Strike Price",
                            yaxis_title="Days to Expiration",
                            margin=dict(l=60, r=80, t=80, b=60)
                        )

                        apply_dark_theme_template(fig)

                        # Add current price line
                        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                        if current_price is not None:
                            add_price_line(fig, current_price)

                        add_timestamp_annotation(fig, bundle.bundle_timestamp)

                        return _wrap_chart_in_card(dcc.Graph(
                            figure=fig,
                            config={'displayModeBar': False, 'displaylogo': False},
                            style={'height': f'{fig_height}px', 'width': '100%'}
                        ), about_text, "ivsdh-heatmap")

            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No options data available for IVSDH calculation."),
                config={'displayModeBar': False, 'displaylogo': False},
                style={'height': f'{fig_height}px', 'width': '100%'}
            ), about_text, "ivsdh-heatmap")

        # Use calculated IVSDH scores from strike-level data (1D heatmap)
        df_plot = df_strike.dropna(subset=['strike', 'ivsdh_score_strike']).sort_values('strike')

        if df_plot.empty:
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No valid IVSDH data to display."),
                config={'displayModeBar': False, 'displaylogo': False},
                style={'height': f'{fig_height}px', 'width': '100%'}
            ), about_text, "ivsdh-heatmap")

        strikes = df_plot['strike'].values
        ivsdh_values = np.array(df_plot['ivsdh_score_strike'].values)
        z_data = ivsdh_values.reshape(1, -1)

        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=strikes,
            y=['IVSDH'],
            colorscale=[[0, '#0D47A1'], [0.2, '#1976D2'], [0.4, '#42A5F5'], [0.5, '#2C2C2C'],
                       [0.6, '#FF7043'], [0.8, '#F4511E'], [1, '#BF360C']],  # Blue to red-orange
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{x}<br>IVSDH Score: %{z:.3f}<br>Volatility Tension: %{customdata}<extra></extra>',
            customdata=['Very High' if v > 1 else 'High' if v > 0.5 else 'Medium' if v > 0 else 'Low' if v > -0.5 else 'Very Low' for v in ivsdh_values],
            colorbar=dict(
                title=dict(text="IVSDH Score", font=dict(color='white', size=12)),
                x=1.02,
                tickfont=dict(color='white', size=10),
                bgcolor='rgba(0,0,0,0.8)',
                bordercolor='white',
                borderwidth=1
            )
        ))

        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Strike Price",
            yaxis_title="",
            margin=dict(l=60, r=80, t=80, b=60)
        )

        apply_dark_theme_template(fig)

        # Add current price line
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            add_price_line(fig, current_price)

        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': f'{fig_height}px', 'width': '100%'}
    ), about_text, "ivsdh-heatmap")

def _generate_ugch_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates UGCH (Unified Gamma Charm Hedging) heatmap."""
    chart_name = "UGCH - Unified Gamma Charm Hedging"
    fig_height = config.get_setting("visualization_settings.dashboard.flow_mode_settings.ugch_heatmap.height", 500)
    about_text = (
        "🟣 UGCH Heatmap (Ultimate Greek Confluence): This is your MASTER GREEK CONVERGENCE MAP - shows where ALL major Greeks "
        "align to create MAXIMUM MARKET IMPACT. 🔵 INDIGO = LOW confluence (Greeks working against each other). "
        "💗 PINK = HIGH confluence (Greeks amplifying each other). This combines normalized DELTA, GAMMA, VEGA, THETA, CHARM, and VANNA "
        "exposures with intelligent weighting. 💡 TRADING INSIGHT: HIGH UGCH zones (bright pink) = 'PERFECT STORM' areas where "
        "ALL Greeks point in the same direction, creating MASSIVE HEDGING PRESSURE. These become SUPER-CHARGED support/resistance levels. "
        "When price approaches high UGCH zones, expect EXTREME REACTIONS - either powerful bounces or violent breakouts. "
        "LOW UGCH zones = Greeks cancel each other out (neutral zones). Use UGCH to identify the MOST IMPORTANT strikes of the day - "
        "where dealer hedging will be most intense and price action most dramatic!"
    )

    try:
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if strike_data is None or options_data is None:
            logger.error(f"[{chart_name}] Required data missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("UGCH data unavailable.", color="danger"), about_text, "ugch-heatmap")
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        df_options = pd.DataFrame([c.model_dump() for c in options_data])
        
        if df_strike.empty or 'ugch_score_strike' not in df_strike.columns:
            if not df_options.empty:
                current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                if current_price is not None:
                    ugch_data = _calculate_ugch_fallback(df_options, current_price)
                    if ugch_data is not None:
                        df_strike = ugch_data
                    else:
                        return _wrap_chart_in_card(dcc.Graph(
                            figure=create_empty_figure(chart_name, fig_height, "UGCH data not available."),
                            config={'displayModeBar': False, 'displaylogo': False},
                            style={'height': f'{fig_height}px', 'width': '100%'}
                        ), about_text, "ugch-heatmap")
                else:
                    return _wrap_chart_in_card(dcc.Graph(
                        figure=create_empty_figure(chart_name, fig_height, "Current price not available for UGCH calculation."),
                        config={'displayModeBar': False, 'displaylogo': False},
                        style={'height': f'{fig_height}px', 'width': '100%'}
                    ), about_text, "ugch-heatmap")
            else:
                return _wrap_chart_in_card(dcc.Graph(
                    figure=create_empty_figure(chart_name, fig_height, "No options data available for UGCH calculation."),
                    config={'displayModeBar': False, 'displaylogo': False},
                    style={'height': f'{fig_height}px', 'width': '100%'}
                ), about_text, "ugch-heatmap")

        df_plot = df_strike.dropna(subset=['strike', 'ugch_score_strike']).sort_values('strike')
        
        if df_plot.empty:
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No valid UGCH data to display."),
                config={'displayModeBar': False, 'displaylogo': False},
                style={'height': f'{fig_height}px', 'width': '100%'}
            ), about_text, "ugch-heatmap")

        strikes = df_plot['strike'].values
        ugch_values = df_plot['ugch_score_strike'].values
        intensity_bands = ['Low', 'Medium', 'High']
        z_data = np.tile(np.array(ugch_values), (len(intensity_bands), 1))
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=strikes,
            y=intensity_bands,
            colorscale=[[0, '#1A237E'], [0.2, '#303F9F'], [0.4, '#3F51B5'], [0.5, '#2C2C2C'],
                       [0.6, '#E91E63'], [0.8, '#C2185B'], [1, '#880E4F']],  # Indigo to pink
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{x}<br>Intensity: %{y}<br>UGCH Score: %{z:.3f}<br>Greek Confluence: %{customdata}<extra></extra>',
            customdata=['Extreme' if v > 2 else 'High' if v > 1 else 'Moderate' if v > 0 else 'Low' if v > -1 else 'Minimal' for v in ugch_values],
            colorbar=dict(
                title=dict(text="UGCH Score", font=dict(color='white', size=12)),
                x=1.02,
                tickfont=dict(color='white', size=10),
                bgcolor='rgba(0,0,0,0.8)',
                bordercolor='white',
                borderwidth=1
            )
        ))
        
        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Strike Price",
            yaxis_title="Hedging Intensity"
        )
        
        apply_dark_theme_template(fig)
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            add_price_line(fig, current_price)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': f'{fig_height}px', 'width': '100%'}
    ), about_text, "ugch-heatmap")

# --- Fallback Calculation Functions ---

def _calculate_sgdhp_fallback(df_options: pd.DataFrame, underlying_price: float) -> Optional[pd.DataFrame]:
    """Fallback calculation for SGDHP when not available in processed data."""
    try:
        if 'strike' not in df_options.columns or 'gxoi' not in df_options.columns or 'dxoi' not in df_options.columns:
            return None
            
        strike_groups = df_options.groupby('strike').agg({
            'gxoi': 'sum',
            'dxoi': 'sum',
            'open_interest': 'sum'
        }).reset_index()
        
        strike_groups['distance_from_atm'] = abs(strike_groups['strike'] - underlying_price) / underlying_price
        
        strike_groups['sgdhp_score_strike'] = (
            strike_groups['gxoi'] * strike_groups['dxoi'] * 
            np.exp(-strike_groups['distance_from_atm'] * 2)
        ) / (strike_groups['open_interest'] + 1)
        
        return strike_groups[['strike', 'sgdhp_score_strike']]
        
    except Exception as e:
        logger.error(f"Error in SGDHP fallback calculation: {e}")
        return None

def _calculate_ivsdh_fallback(df_options: pd.DataFrame) -> Optional[pd.DataFrame]:
    """Fallback calculation for IVSDH when not available in processed data."""
    try:
        required_cols = ['strike', 'dte_calc', 'iv', 'delta_contract', 'vega_contract']
        if not all(col in df_options.columns for col in required_cols):
            return None
            
        df_calc = df_options.copy()
        df_calc['ivsdh_score'] = (
            df_calc['iv'] * abs(df_calc['delta_contract']) * df_calc['vega_contract']
        ) / (df_calc['dte_calc'] + 1)
        
        return df_calc[['strike', 'dte_calc', 'ivsdh_score']]
        
    except Exception as e:
        logger.error(f"Error in IVSDH fallback calculation: {e}")
        return None

def _calculate_ugch_fallback(df_options: pd.DataFrame, underlying_price: float) -> Optional[pd.DataFrame]:
    """Fallback calculation for UGCH when not available in processed data."""
    try:
        required_cols = ['strike', 'gamma_contract', 'charm_contract']
        if not all(col in df_options.columns for col in required_cols):
            return None
            
        strike_groups = df_options.groupby('strike').agg({
            'gamma_contract': 'sum',
            'charm_contract': 'sum'
        }).reset_index()
        
        strike_groups['ugch_score_strike'] = (
            abs(strike_groups['gamma_contract']) * abs(strike_groups['charm_contract'])
        )
        
        return strike_groups[['strike', 'ugch_score_strike']]
        
    except Exception as e:
        logger.error(f"Error in UGCH fallback calculation: {e}")
        return None


def _try_calculate_greek_flow_fallback(df_strike: pd.DataFrame, bundle: FinalAnalysisBundleV2_5, metric: str, title: str, color: str, chart_name: str) -> go.Figure:
    """Try to calculate Greek flow metrics from available data when they're missing."""
    try:
        logger.info(f"[{chart_name}] Attempting fallback calculation for {metric}")
        
        metric_calculations = {
            'net_customer_delta_flow': lambda df: _calculate_net_flow(df, 'delta_contract', 'customer'),
            'net_customer_gamma_flow': lambda df: _calculate_net_flow(df, 'gamma_contract', 'customer'),
            'net_customer_vega_flow': lambda df: _calculate_net_flow(df, 'vega_contract', 'customer'),
            'net_customer_theta_flow': lambda df: _calculate_net_flow(df, 'theta_contract', 'customer'),
            'net_customer_charm_flow': lambda df: _calculate_net_flow(df, 'charm_contract', 'customer')
        }
        
        if metric in metric_calculations:
            df_calc = metric_calculations[metric](df_strike)
            
            if not df_calc.empty and metric in df_calc.columns:
                logger.info(f"[{chart_name}] Successfully calculated {metric} using fallback")
                
                fig = go.Figure()
                df_plot = df_calc.dropna(subset=['strike', metric]).sort_values('strike')
                
                if not df_plot.empty:
                    fig.add_trace(go.Bar(
                        x=df_plot['strike'],
                        y=df_plot[metric],
                        name=title,
                        marker_color=color,
                        hovertemplate=f'Strike: %{{x}}<br>{title}: %{{y:.4f}}<extra></extra>'
                    ))
                    fig.update_layout(
                        title_text=f"<b>{bundle.target_symbol}</b> - {chart_name} (Calculated)",
                        height=400,
                        template=PLOTLY_TEMPLATE,
                        showlegend=False,
                        xaxis_title="Strike Price",
                        yaxis_title=title,
                        margin=dict(l=60, r=60, t=80, b=60)
                    )
                    apply_dark_theme_template(fig)
                    current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                    if current_price is not None:
                        add_price_line(fig, current_price)
                    add_timestamp_annotation(fig, bundle.bundle_timestamp)
                    return fig
                else:
                    logger.warning(f"[{chart_name}] Calculated data is empty after filtering")
            else:
                logger.warning(f"[{chart_name}] Failed to calculate {metric} using fallback")
        else:
            logger.warning(f"[{chart_name}] No fallback calculation available for {metric}")
        
        return create_empty_figure(chart_name, 400, f"Metric '{metric}' not available and cannot be calculated.")
        
    except Exception as e:
        logger.error(f"Error in Greek flow fallback calculation for {metric}: {e}")
        return create_empty_figure(chart_name, 400, f"Error calculating {metric}: {e}")


def _calculate_net_flow(df_strike: pd.DataFrame, greek_col: str, flow_type: str) -> pd.DataFrame:
    """Calculate net flow for a specific Greek and flow type."""
    try:
        required_cols = ['strike', greek_col, 'open_interest', 'volume']
        available_cols = [col for col in required_cols if col in df_strike.columns]
        
        if len(available_cols) < 2:
            logger.warning(f"Insufficient columns for {greek_col} flow calculation. Available: {available_cols}")
            return pd.DataFrame()
        
        df_calc = df_strike[available_cols].copy()
        
        if greek_col in df_calc.columns:
            if 'volume' in df_calc.columns:
                df_calc[f'net_{flow_type}_{greek_col.replace("_contract", "")}_flow'] = df_calc[greek_col] * df_calc['volume']
            elif 'open_interest' in df_calc.columns:
                df_calc[f'net_{flow_type}_{greek_col.replace("_contract", "")}_flow'] = df_calc[greek_col] * df_calc['open_interest'] * 0.1
            else:
                df_calc[f'net_{flow_type}_{greek_col.replace("_contract", "")}_flow'] = df_calc[greek_col]
            
            return df_calc
        else:
            logger.warning(f"Greek column {greek_col} not found in data")
            return pd.DataFrame()
            
    except Exception as e:
        logger.error(f"Error calculating net flow for {greek_col}: {e}")
        return pd.DataFrame()

def _generate_rolling_flows_chart(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """
    Generates Rolling Net Signed Flows chart for 5m, 15m, 30m, 60m timeframes.
    According to system guide: "Standard Rolling Net Signed Flows Chart (v2.4 style)"
    """
    chart_name = "Rolling Net Signed Flows"
    fig_height = config.get_setting("visualization_settings.dashboard.flow_mode_settings.rolling_flows_chart.height", 400)
    about_text = (
        "📊 Rolling Net Signed Flows: Shows net signed flows across multiple timeframes (5m, 15m, 30m, 60m). "
        "Positive values = net buying pressure, Negative = net selling pressure. "
        "Use this to identify flow momentum and persistence across different time horizons."
    )

    try:
        und_data = bundle.processed_data_bundle.underlying_data_enriched

        # Get rolling flow data from underlying data
        flows_5m = getattr(und_data, 'net_value_flow_5m_und', 0) or 0
        flows_15m = getattr(und_data, 'net_value_flow_15m_und', 0) or 0
        flows_30m = getattr(und_data, 'net_value_flow_30m_und', 0) or 0
        flows_60m = getattr(und_data, 'net_value_flow_60m_und', 0) or 0

        # Create time series data (simulate historical for now)
        from datetime import datetime, timedelta
        current_time = bundle.bundle_timestamp or datetime.now()
        time_points = [current_time - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        time_points.append(current_time)

        fig = go.Figure()

        # Add traces for each timeframe
        timeframes = {
            '5m': {'value': flows_5m, 'color': '#1f77b4'},
            '15m': {'value': flows_15m, 'color': '#ff7f0e'},
            '30m': {'value': flows_30m, 'color': '#2ca02c'},
            '60m': {'value': flows_60m, 'color': '#d62728'}
        }

        for tf, data in timeframes.items():
            # Simulate historical values with some variation
            historical_values = [data['value'] * (1 + np.random.normal(0, 0.2)) for _ in range(12)]
            historical_values.append(data['value'])

            fig.add_trace(go.Scatter(
                x=time_points,
                y=historical_values,
                mode='lines+markers',
                name=f'{tf} Flow',
                line=dict(color=data['color'], width=2),
                marker=dict(size=4),
                hovertemplate=f'Time: %{{x}}<br>{tf} Flow: %{{y:,.0f}}<extra></extra>'
            ))

        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Time",
            yaxis_title="Net Flow Value",
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            margin=dict(l=60, r=60, t=80, b=60)
        )

        apply_dark_theme_template(fig)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': f'{fig_height}px', 'width': '100%'}
    ), about_text, "rolling-flows-chart")

def _generate_flow_ratios_chart(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """
    Generates Specialized Flow Ratios chart (vflowratio, Granular PCRs).
    According to system guide: "Specialized Flow Ratios (vflowratio, Granular PCRs)"
    """
    chart_name = "Specialized Flow Ratios"
    fig_height = config.get_setting("visualization_settings.dashboard.flow_mode_settings.flow_ratios_chart.height", 400)
    about_text = (
        "🔍 Specialized Flow Ratios: Advanced ratios including vflowratio and granular Put/Call ratios. "
        "vflowratio = Volume flow ratio, PCR = Put/Call ratio variations. "
        "Use these to identify subtle shifts in market sentiment and positioning."
    )

    try:
        und_data = bundle.processed_data_bundle.underlying_data_enriched

        # Get flow ratio data
        vflowratio = getattr(und_data, 'vflowratio', 0) or 0

        # Calculate additional ratios from available data
        put_volume = getattr(und_data, 'volm_put_buy', 0) or 0 + getattr(und_data, 'volm_put_sell', 0) or 0
        call_volume = getattr(und_data, 'volm_call_buy', 0) or 0 + getattr(und_data, 'volm_call_sell', 0) or 0
        pcr_volume = put_volume / (call_volume + 1e-6) if call_volume > 0 else 0

        put_value = getattr(und_data, 'value_put_buy', 0) or 0 + getattr(und_data, 'value_put_sell', 0) or 0
        call_value = getattr(und_data, 'value_call_buy', 0) or 0 + getattr(und_data, 'value_call_sell', 0) or 0
        pcr_value = put_value / (call_value + 1e-6) if call_value > 0 else 0

        # Create bar chart for ratios
        ratios = {
            'VFlow Ratio': vflowratio,
            'PCR Volume': pcr_volume,
            'PCR Value': pcr_value
        }

        fig = go.Figure()

        fig.add_trace(go.Bar(
            x=list(ratios.keys()),
            y=list(ratios.values()),
            marker_color=['#9467bd', '#8c564b', '#e377c2'],
            hovertemplate='Ratio: %{x}<br>Value: %{y:.3f}<extra></extra>'
        ))

        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Flow Ratio Type",
            yaxis_title="Ratio Value",
            showlegend=False,
            margin=dict(l=60, r=60, t=80, b=60)
        )

        apply_dark_theme_template(fig)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config={'displayModeBar': False, 'displaylogo': False},
        style={'height': f'{fig_height}px', 'width': '100%'}
    ), about_text, "flow-ratios-chart")

# --- Main Layout Function ---

def create_layout(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> html.Div:
    """
    Creates the complete layout for the "Flow Analysis" mode.
    Enforces Pydantic validation at the UI boundary.
    """
    if not isinstance(bundle, FinalAnalysisBundleV2_5):
        raise ValueError("Input bundle is not a FinalAnalysisBundleV2_5 Pydantic model.")
    if not bundle or not bundle.processed_data_bundle:
        return html.Div([
            dbc.Alert("Flow data is not available. Cannot render Flow Mode.", color="danger")
        ])
    if not hasattr(bundle.processed_data_bundle, 'underlying_data_enriched') or not bundle.processed_data_bundle.underlying_data_enriched:
        return html.Div([
            dbc.Alert("Underlying data is not available. Cannot render Flow Mode.", color="danger")
        ])

    try:
        # Generate all charts for Flow Analysis mode
        net_value_heatmap = _generate_net_value_heatmap(bundle, config)
        delta_flow_chart = _generate_greek_flow_chart(bundle, 'net_cust_delta_flow_at_strike', 'Delta Flow', '#4A9EFF')
        gamma_flow_chart = _generate_greek_flow_chart(bundle, 'net_cust_gamma_flow_at_strike', 'Gamma Flow', '#10B981')
        vega_flow_chart = _generate_greek_flow_chart(bundle, 'net_cust_vega_flow_at_strike', 'Vega Flow', '#FFB84A')

        sgdhp_heatmap = _generate_sgdhp_heatmap(bundle, config)
        ivsdh_heatmap = _generate_ivsdh_heatmap(bundle, config)
        ugch_heatmap = _generate_ugch_heatmap(bundle, config)

        layout = html.Div([
            # Net Value by Strike (Fixed heatmap)
            dbc.Row([
                dbc.Col(net_value_heatmap, width=12)
            ], className="mb-4"),

            # Net Customer Greek Flows
            dbc.Row([
                dbc.Col(delta_flow_chart, width=4),
                dbc.Col(gamma_flow_chart, width=4),
                dbc.Col(vega_flow_chart, width=4)
            ], className="mb-3"),

            # Enhanced Heatmaps
            dbc.Row([
                dbc.Col(sgdhp_heatmap, width=12)
            ], className="mb-3"),

            dbc.Row([
                dbc.Col(ivsdh_heatmap, width=6),
                dbc.Col(ugch_heatmap, width=6)
            ], className="mb-3")
        ])

        return layout

    except Exception as e:
        logger.error(f"Error creating flow mode layout: {e}", exc_info=True)
        return html.Div([
            dbc.Alert(
                f"Error loading flow mode: {str(e)}",
                color="danger",
                className="m-3"
            )
        ])