"""
AI Predictions Manager v2.5 - "THE APEX PREDATOR'S CRYSTAL BALL"
================================================================

This module manages AI predictions, tracking accuracy, and learning from outcomes.
It creates, stores, evaluates, and learns from market predictions using the EOTS system.

Key Features:
- Create market predictions with confidence scores
- Track prediction accuracy over time
- Evaluate predictions against actual outcomes
- Generate performance metrics for AI dashboard
- Learn from prediction patterns

Author: EOTS v2.5 Development Team - "AI Predictions Division"
Version: 2.5.0
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
from pydantic import ValidationError

from data_models.eots_schemas_v2_5 import (
    FinalAnalysisBundleV2_5, ProcessedDataBundleV2_5,
    AIPredictionV2_5, AIPredictionPerformanceV2_5, AIPredictionRequestV2_5
)

logger = logging.getLogger(__name__)


class AIPredictionsManagerV2_5:
    """Manages AI predictions and performance tracking for EOTS v2.5."""
    
    def __init__(self, database_manager):
        """Initialize the AI Predictions Manager."""
        self.db_manager = database_manager
        self.logger = logger
        
    def create_prediction(self, prediction_request: AIPredictionRequestV2_5) -> Optional[AIPredictionV2_5]:
        """
        Create a new AI prediction using Pydantic model validation.

        Args:
            prediction_request: Validated prediction request model

        Returns:
            AIPredictionV2_5: Created prediction model or None if failed
        """
        try:
            if not self.db_manager:
                self.logger.error("No database manager available")
                return None

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # Create prediction model with validated data
            prediction = AIPredictionV2_5(
                id=None,
                symbol=prediction_request.symbol,
                prediction_type=prediction_request.prediction_type,
                prediction_value=prediction_request.prediction_value,
                prediction_direction=prediction_request.prediction_direction,
                confidence_score=prediction_request.confidence_score,
                time_horizon=prediction_request.time_horizon,
                target_timestamp=prediction_request.target_timestamp,
                market_context=prediction_request.market_context,
                prediction_timestamp=datetime.now(),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                actual_value=None,
                actual_direction=None,
                prediction_accurate=None,
                accuracy_score=None
            )

            # Insert into database (database-specific SQL)
            if hasattr(self.db_manager, 'db_type') and self.db_manager.db_type == "sqlite":
                insert_query = """
                INSERT INTO ai_predictions (
                    symbol, prediction_type, prediction_value, prediction_direction,
                    confidence_score, time_horizon, prediction_timestamp, target_timestamp,
                    market_context, model_version, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                cursor.execute(insert_query, (
                    prediction.symbol, prediction.prediction_type, prediction.prediction_value,
                    prediction.prediction_direction, prediction.confidence_score, prediction.time_horizon,
                    prediction.prediction_timestamp, prediction.target_timestamp,
                    json.dumps(prediction.market_context), prediction.model_version,
                    prediction.created_at, prediction.updated_at
                ))

                prediction_id = cursor.lastrowid
            else:
                insert_query = """
                INSERT INTO ai_predictions (
                    symbol, prediction_type, prediction_value, prediction_direction,
                    confidence_score, time_horizon, prediction_timestamp, target_timestamp,
                    market_context, model_version, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
                """

                cursor.execute(insert_query, (
                    prediction.symbol, prediction.prediction_type, prediction.prediction_value,
                    prediction.prediction_direction, prediction.confidence_score, prediction.time_horizon,
                    prediction.prediction_timestamp, prediction.target_timestamp,
                    json.dumps(prediction.market_context), prediction.model_version,
                    prediction.created_at, prediction.updated_at
                ))

                prediction_id = cursor.fetchone()[0]
            prediction.id = prediction_id
            conn.commit()

            self.logger.info(f"✅ Created AI prediction {prediction_id} for {prediction.symbol}: {prediction.prediction_direction} with {prediction.confidence_score:.1%} confidence")
            return prediction

        except ValidationError as e:
            self.logger.error(f"Pydantic validation error: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Error creating AI prediction: {str(e)}")
            if 'conn' in locals():
                conn.rollback()
            return None
    
    def create_eots_prediction(self, bundle_data: FinalAnalysisBundleV2_5) -> Optional[AIPredictionV2_5]:
        """
        Create a prediction based on EOTS analysis bundle using Pydantic models.

        Args:
            bundle_data: Complete EOTS analysis bundle

        Returns:
            AIPredictionV2_5: Created prediction model if successful
        """
        try:
            symbol = bundle_data.target_symbol
            processed_data = bundle_data.processed_data_bundle

            if not processed_data:
                return None

            # Extract key metrics for prediction using Pydantic model access
            metrics = processed_data.underlying_data_enriched.model_dump()

            # Calculate prediction based on EOTS metrics
            vapi_fa_z = metrics.get('vapi_fa_z_score_und', 0)
            dwfd_z = metrics.get('dwfd_z_score_und', 0)
            tw_laf_z = metrics.get('tw_laf_z_score_und', 0)

            # Determine prediction direction and confidence
            signal_strength = abs(vapi_fa_z) + abs(dwfd_z) + abs(tw_laf_z)

            if signal_strength > 3.0:
                # Strong signal
                if vapi_fa_z > 0 or dwfd_z > 0:
                    direction = 'UP'
                else:
                    direction = 'DOWN'
                confidence = min(0.85, 0.5 + (signal_strength / 10.0))
            elif signal_strength > 1.5:
                # Moderate signal
                if vapi_fa_z > 0 or dwfd_z > 0:
                    direction = 'UP'
                else:
                    direction = 'DOWN'
                confidence = min(0.70, 0.4 + (signal_strength / 15.0))
            else:
                # Weak signal
                direction = 'NEUTRAL'
                confidence = 0.5

            # Create market context
            market_context = {
                'vapi_fa_z': vapi_fa_z,
                'dwfd_z': dwfd_z,
                'tw_laf_z': tw_laf_z,
                'signal_strength': signal_strength,
                'market_regime': getattr(processed_data.underlying_data_enriched, 'current_market_regime_v2_5', 'UNKNOWN'),
                'analysis_timestamp': bundle_data.bundle_timestamp.isoformat(),
                'eots_version': 'v2.5'
            }

            # Set target timestamp (4 hours from now for intraday prediction)
            target_time = datetime.now() + timedelta(hours=4)

            # Create prediction request using Pydantic model
            prediction_request = AIPredictionRequestV2_5(
                symbol=symbol,
                prediction_type='eots_direction',
                prediction_value=signal_strength,
                prediction_direction=direction,
                confidence_score=confidence,
                time_horizon='4H',
                target_timestamp=target_time,
                market_context=market_context
            )

            # Create the prediction
            prediction = self.create_prediction(prediction_request)

            return prediction

        except ValidationError as e:
            self.logger.error(f"Pydantic validation error in EOTS prediction: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Error creating EOTS prediction: {str(e)}")
            return None
    
    def evaluate_predictions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Evaluate pending predictions against current market data.
        
        Args:
            symbol: Optional symbol to filter predictions
            
        Returns:
            Dict with evaluation results
        """
        try:
            if not self.db_manager:
                return {}
                
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Get pending predictions that are ready for evaluation (database-specific)
            if hasattr(self.db_manager, 'db_type') and self.db_manager.db_type == "sqlite":
                query = """
                SELECT id, symbol, prediction_type, prediction_value, prediction_direction,
                       confidence_score, time_horizon, target_timestamp, market_context
                FROM ai_predictions
                WHERE prediction_accurate IS NULL
                AND target_timestamp <= datetime('now')
                """

                if symbol:
                    query += " AND symbol = ?"
                    cursor.execute(query, (symbol,))
                else:
                    cursor.execute(query)
            else:
                query = """
                SELECT id, symbol, prediction_type, prediction_value, prediction_direction,
                       confidence_score, time_horizon, target_timestamp, market_context
                FROM ai_predictions
                WHERE prediction_accurate IS NULL
                AND target_timestamp <= NOW()
                """

                if symbol:
                    query += " AND symbol = %s"
                    cursor.execute(query, (symbol,))
                else:
                    cursor.execute(query)
                
            pending_predictions = cursor.fetchall()
            
            evaluated_count = 0
            accurate_count = 0
            
            for pred in pending_predictions:
                pred_id, pred_symbol, pred_type, pred_value, pred_direction, confidence, horizon, target_time, context = pred
                
                # Get actual market data for evaluation
                # This would typically fetch from your market data source
                # For now, we'll simulate evaluation
                actual_direction = self._get_actual_direction(pred_symbol, target_time)
                
                if actual_direction:
                    is_accurate = (pred_direction == actual_direction)
                    accuracy_score = 1.0 if is_accurate else 0.0
                    
                    # Update prediction with results (database-specific)
                    if hasattr(self.db_manager, 'db_type') and self.db_manager.db_type == "sqlite":
                        update_query = """
                        UPDATE ai_predictions
                        SET actual_direction = ?, prediction_accurate = ?,
                            accuracy_score = ?, updated_at = ?
                        WHERE id = ?
                        """
                    else:
                        update_query = """
                        UPDATE ai_predictions
                        SET actual_direction = %s, prediction_accurate = %s,
                            accuracy_score = %s, updated_at = %s
                        WHERE id = %s
                        """

                    cursor.execute(update_query, (
                        actual_direction, is_accurate, accuracy_score, datetime.now(), pred_id
                    ))
                    
                    evaluated_count += 1
                    if is_accurate:
                        accurate_count += 1
            
            conn.commit()
            
            return {
                'evaluated_predictions': evaluated_count,
                'accurate_predictions': accurate_count,
                'accuracy_rate': accurate_count / evaluated_count if evaluated_count > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error evaluating predictions: {str(e)}")
            return {}
    
    def _get_actual_direction(self, symbol: str, target_time: datetime) -> Optional[str]:
        """
        Get actual market direction for evaluation.
        This is a placeholder - implement with real market data.
        """
        # TODO: Implement actual market data fetching
        # For now, return a simulated result
        import random
        return random.choice(['UP', 'DOWN', 'NEUTRAL'])
    
    def get_prediction_performance(self, symbol: str, days: int = 30) -> Optional[AIPredictionPerformanceV2_5]:
        """
        Get prediction performance metrics using Pydantic models.

        Args:
            symbol: Trading symbol
            days: Number of days to look back

        Returns:
            AIPredictionPerformanceV2_5: Performance metrics model
        """
        try:
            if not self.db_manager:
                return None

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # Get performance metrics (database-specific)
            if hasattr(self.db_manager, 'db_type') and self.db_manager.db_type == "sqlite":
                query = """
                SELECT
                    COUNT(*) as total_predictions,
                    AVG(CASE WHEN prediction_accurate = 1 THEN 1.0 ELSE 0.0 END) as accuracy_rate,
                    AVG(confidence_score) as avg_confidence,
                    AVG(accuracy_score) as avg_accuracy_score,
                    COUNT(CASE WHEN prediction_accurate = 1 THEN 1 END) as correct_predictions,
                    COUNT(CASE WHEN prediction_accurate = 0 THEN 1 END) as incorrect_predictions,
                    COUNT(CASE WHEN prediction_accurate IS NULL THEN 1 END) as pending_predictions
                FROM ai_predictions
                WHERE symbol = ?
                AND created_at >= datetime('now', '-' || ? || ' days')
                """
                cursor.execute(query, (symbol, days))
            else:
                query = """
                SELECT
                    COUNT(*) as total_predictions,
                    AVG(CASE WHEN prediction_accurate = true THEN 1.0 ELSE 0.0 END) as accuracy_rate,
                    AVG(confidence_score) as avg_confidence,
                    AVG(accuracy_score) as avg_accuracy_score,
                    COUNT(CASE WHEN prediction_accurate = true THEN 1 END) as correct_predictions,
                    COUNT(CASE WHEN prediction_accurate = false THEN 1 END) as incorrect_predictions,
                    COUNT(CASE WHEN prediction_accurate IS NULL THEN 1 END) as pending_predictions
                FROM ai_predictions
                WHERE symbol = %s
                AND created_at >= NOW() - INTERVAL %s
                """
                cursor.execute(query, (symbol, f"'{days} days'"))
            result = cursor.fetchone()

            if result:
                total, accuracy, avg_conf, avg_acc, correct, incorrect, pending = result

                # Calculate performance trend (database-specific)
                if hasattr(self.db_manager, 'db_type') and self.db_manager.db_type == "sqlite":
                    trend_query = """
                    SELECT
                        AVG(CASE WHEN prediction_accurate = 1 THEN 1.0 ELSE 0.0 END) as recent_accuracy
                    FROM ai_predictions
                    WHERE symbol = ?
                    AND created_at >= datetime('now', '-7 days')
                    AND prediction_accurate IS NOT NULL
                    """
                    cursor.execute(trend_query, (symbol,))
                else:
                    trend_query = """
                    SELECT
                        AVG(CASE WHEN prediction_accurate = true THEN 1.0 ELSE 0.0 END) as recent_accuracy
                    FROM ai_predictions
                    WHERE symbol = %s
                    AND created_at >= NOW() - INTERVAL '7 days'
                    AND prediction_accurate IS NOT NULL
                    """
                    cursor.execute(trend_query, (symbol,))
                trend_result = cursor.fetchone()
                recent_accuracy = trend_result[0] if trend_result and trend_result[0] else accuracy

                # Determine trend
                if recent_accuracy and accuracy:
                    if recent_accuracy > accuracy * 1.05:
                        trend = "IMPROVING"
                    elif recent_accuracy < accuracy * 0.95:
                        trend = "DECLINING"
                    else:
                        trend = "STABLE"
                else:
                    trend = "UNKNOWN"

                # Create Pydantic performance model with safe calculations
                safe_accuracy = float(avg_acc or 0)
                safe_confidence = float(avg_conf or 0)
                safe_learning_score = safe_accuracy * safe_confidence if safe_accuracy > 0 and safe_confidence > 0 else 0.0

                performance = AIPredictionPerformanceV2_5(
                    symbol=symbol,
                    time_period_days=days,
                    total_predictions=total or 0,
                    correct_predictions=correct or 0,
                    incorrect_predictions=incorrect or 0,
                    pending_predictions=pending or 0,
                    success_rate=float(accuracy or 0),
                    avg_confidence=safe_confidence,
                    avg_accuracy_score=safe_accuracy,
                    learning_score=safe_learning_score,
                    performance_trend=trend,
                    last_updated=datetime.now()
                )

                return performance

            return None

        except ValidationError as e:
            self.logger.error(f"Pydantic validation error in performance metrics: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Error getting prediction performance: {str(e)}")
            return None
