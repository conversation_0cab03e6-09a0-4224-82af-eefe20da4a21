# EOTS v2.5 - S-GRA<PERSON>, AUTHORITATIVE MAIN DASHBOARD DISPLAY

import logging
from typing import List, Optional, Any, Union, Tuple
from datetime import datetime

import pandas as pd
import plotly.graph_objects as go
from dash import html, dcc, dash_table
import dash_bootstrap_components as dbc
import numpy as np
from pydantic import ValidationError

from dashboard_application import ids
from dashboard_application.utils_dashboard_v2_5 import create_empty_figure, PLOTLY_TEMPLATE, add_timestamp_annotation, apply_dark_theme_template
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5, ProcessedUnderlyingAggregatesV2_5, ActiveRecommendationPayloadV2_5, ProcessedStrikeLevelMetricsV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5

logger = logging.getLogger(__name__)

# --- Helper Functions for Component Generation ---

def _get_gauge_interpretation(value: float, metric_name: str) -> Tuple[str, str]:
    """Returns (synopsis, interpretation) for gauge values with enhanced trading insights."""
    if metric_name in ["VAPI-FA", "DWFD", "TW-LAF"]:
        # Enhanced Flow metrics interpretations
        if value >= 2.5:
            return ("🚀 EXPLOSIVE bullish momentum - Institutions loading up!", "MAXIMUM BULLISH CONVICTION - Consider aggressive long strategies")
        elif value >= 2:
            return ("📈 Strong bullish flow - Smart money is buying", "STRONG BULLISH SIGNAL - High conviction long setups")
        elif value >= 1:
            return ("📊 Moderate bullish flow - Upward bias confirmed", "MODERATE BULLISH - Trend-following strategies favored")
        elif value >= -1:
            return ("😐 Neutral flow - Market indecision", "NEUTRAL ZONE - Wait for clearer signals or use range strategies")
        elif value >= -2:
            return ("📉 Bearish flow building - Selling pressure mounting", "MODERATE BEARISH - Consider protective strategies")
        elif value >= -2.5:
            return ("🔻 Strong bearish flow - Smart money is selling", "STRONG BEARISH SIGNAL - High conviction short setups")
        else:
            return ("💥 EXPLOSIVE bearish momentum - Institutions dumping!", "MAXIMUM BEARISH CONVICTION - Consider aggressive short strategies")
    elif metric_name == "GIB OI-Based":
        # Enhanced Gamma Imbalance interpretations
        abs_val = abs(value)
        if value > 20000:
            return ("🛡️ FORTRESS MODE - MMs heavily long gamma", "MAXIMUM STABILITY - Perfect for premium selling strategies")
        elif value > 5000:
            return ("📊 Stable environment - MMs moderately long gamma", "HIGH STABILITY - Good for theta strategies and iron condors")
        elif value > 0:
            return ("⚖️ Slight stability bias - MMs slightly long gamma", "MILD STABILITY - Neutral to slightly range-bound")
        elif value > -5000:
            return ("🌪️ Volatility brewing - MMs slightly short gamma", "MILD VOLATILITY - Watch for increased movement")
        elif value > -20000:
            return ("⚡ High volatility expected - MMs moderately short gamma", "HIGH VOLATILITY - Great for straddles and breakout plays")
        else:
            return ("🚨 EXPLOSION ZONE - MMs heavily short gamma", "MAXIMUM VOLATILITY - Prime time for volatility strategies")
    elif metric_name == "TD-GIB":
        abs_val = abs(value)
        if abs_val > 1000:
            return ("📌 STRONG pin risk - Price wants to stick", "HIGH PIN EFFECT - Fade moves away from key strikes")
        elif abs_val > 500:
            return ("🎯 Moderate pin risk - Some gravitational pull", "MODERATE PIN EFFECT - Consider pin-aware strategies")
        else:
            return ("🆓 Low pin risk - Price can move freely", "LOW PIN EFFECT - Directional strategies favored")
    elif metric_name == "HP-EOD":
        if value > 50000:
            return ("🔴 MASSIVE selling pressure into close expected", "EXTREME SELLING PRESSURE - Strong bearish EOD bias")
        elif value > 10000:
            return ("📉 Significant selling pressure into close", "STRONG SELLING PRESSURE - Bearish EOD bias")
        elif value > 0:
            return ("📊 Mild selling pressure into close", "MILD SELLING PRESSURE - Slight bearish EOD bias")
        elif value > -10000:
            return ("📈 Mild buying pressure into close", "MILD BUYING PRESSURE - Slight bullish EOD bias")
        elif value > -50000:
            return ("🟢 Significant buying pressure into close", "STRONG BUYING PRESSURE - Bullish EOD bias")
        else:
            return ("🚀 MASSIVE buying pressure into close expected", "EXTREME BUYING PRESSURE - Strong bullish EOD bias")
    else:
        return (f"Value: {value:.2f}", f"Value: {value:.2f}")

def _get_heatmap_interpretation(value: float, metric_name: str) -> str:
    """Returns enhanced interpretation text for heatmap values with trading insights."""
    if metric_name == "SGDHP":
        if value > 100:
            return "🛡️ FORTRESS LEVEL - MMs will defend this strike with everything they have!"
        elif value > 50:
            return "🎯 VERY STRONG S/R - Major battle zone, expect fierce defense"
        elif value > 20:
            return "💪 STRONG S/R - Significant MM interest, good for entries/exits"
        elif value > 5:
            return "📊 MODERATE S/R - Some MM defense, watch for bounces/rejections"
        elif value > -5:
            return "😐 NEUTRAL ZONE - Minimal MM interest, price can pass through"
        elif value > -20:
            return "⚠️ MODERATE RESISTANCE - Some selling pressure expected"
        elif value > -50:
            return "🔴 STRONG RESISTANCE - Significant selling wall, tough to break"
        else:
            return "🚫 FORTRESS RESISTANCE - MMs will cap rallies aggressively here!"
    elif metric_name == "UGCH":
        if value > 10:
            return "🌟 GREEK SUPERNOVA - All forces align perfectly, maximum impact zone!"
        elif value > 5:
            return "⭐ VERY HIGH CONFLUENCE - Greeks strongly aligned, high-conviction level"
        elif value > 2:
            return "💫 HIGH CONFLUENCE - Strong Greek alignment, significant structural level"
        elif value > 0:
            return "✨ MODERATE CONFLUENCE - Some Greek alignment, decent structural importance"
        elif value > -2:
            return "🌫️ LOW CONFLUENCE - Weak Greek alignment, minimal structural significance"
        elif value > -5:
            return "🌑 VERY LOW CONFLUENCE - Greeks working against each other"
        else:
            return "💀 GREEK DISCORD - Avoid this strike, conflicting forces create chaos!"
    else:
        return f"📊 Value: {value:.2f}"

def _get_dashboard_settings(config: ConfigManagerV2_5) -> dict:
    """Get dashboard settings with proper fallbacks"""
    try:
        dashboard_config = config.config.visualization_settings.dashboard
        return dashboard_config.get("main_dashboard_settings", {})
    except Exception as e:
        logger.warning(f"Failed to load dashboard settings: {e}")
        return {}

def _create_flow_gauge(
    metric_name: str,
    value: Optional[float],
    component_id: str,
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    symbol: str
) -> html.Div:
    """Creates a flow gauge for enhanced flow metrics (VAPI-FA, DWFD, TW-LAF)."""
    main_dash_settings = _get_dashboard_settings(config)
    flow_gauge_settings = main_dash_settings.get("flow_gauge", {})
    fig_height = flow_gauge_settings.get("height", 350)
    
    # Chart blurbs for user guidance - ENHANCED WITH TRADING INSIGHTS
    gauge_blurbs = {
        "VAPI-FA": "📈 **VAPI-FA: The Flow Acceleration Detective** 🕵️‍♂️\n\n**What it reveals:** Combines premium intensity with flow acceleration to spot when institutional money is moving with conviction. This is your early warning system for momentum shifts!\n\n**Trading Applications:**\n• **+2 to +3**: 🚀 Strong bullish acceleration - Consider call spreads, long calls, or momentum plays\n• **+1 to +2**: 📈 Moderate bullish flow - Good for trend-following strategies\n• **-1 to +1**: 😐 Neutral zone - Wait for clearer signals or use range-bound strategies\n• **-2 to -1**: 📉 Bearish momentum building - Consider put spreads or protective puts\n• **-3 to -2**: 🔻 Strong bearish acceleration - Prime time for put plays or short strategies\n\n**Pro Tips:** 💡 Watch for divergences with price action! If price hits new highs but VAPI-FA weakens, institutions may be quietly exiting. Combine with volume analysis for maximum impact.",

        "DWFD": "⚖️ **DWFD: The Smart Money Tracker** 🎯\n\n**What it reveals:** Exposes when sophisticated traders (smart money) are positioning differently from the crowd. This metric catches institutional footprints before they become obvious!\n\n**Trading Applications:**\n• **+2 to +3**: 🧠 Smart money is aggressively bullish - High conviction long setups\n• **+1 to +2**: 💰 Institutional buying detected - Consider bullish strategies\n• **-1 to +1**: 🤔 Mixed signals - Smart money is neutral or conflicted\n• **-2 to -1**: 🐻 Institutional selling pressure - Bearish setups favored\n• **-3 to -2**: 🚨 Smart money dumping - Strong bearish conviction plays\n\n**Pro Tips:** 💡 DWFD shines during market turning points! When retail is buying the top, DWFD often shows smart money quietly selling. Use this as your contrarian edge - fade the crowd when DWFD diverges strongly from price action.",

        "TW-LAF": "⏰ **TW-LAF: The Trend Sustainability Meter** 📊\n\n**What it reveals:** Filters out noise to show you REAL, sustainable trends backed by liquid flow. This is your trend strength validator - no more getting faked out by weak moves!\n\n**Trading Applications:**\n• **+2 to +3**: 🔥 Bulletproof bullish trend - Ride the wave with confidence\n• **+1 to +2**: 📈 Solid uptrend - Good for swing trades and trend following\n• **-1 to +1**: ⚡ Choppy/ranging market - Use mean reversion strategies\n• **-2 to -1**: 📉 Reliable downtrend - Short-side opportunities\n• **-3 to -2**: 🌊 Powerful bearish tsunami - Maximum short conviction\n\n**Pro Tips:** 💡 TW-LAF is your trend filter! Only take directional trades when TW-LAF confirms the move. When TW-LAF is extreme (+/-2.5+), trends often continue longer than expected. Use it to size positions - bigger size when TW-LAF strongly confirms your bias!"
    }
    
    blurb_text = gauge_blurbs.get(metric_name, f"{metric_name} flow analysis")
    gauge_title_text = f"{metric_name}"  # Just the metric name to prevent overlapping
    indicator_title_text = f"{metric_name}"

    if value is None or pd.isna(value):
        fig = create_empty_figure(title=gauge_title_text, height=fig_height, reason="Data N/A")
    else:
        synopsis, interpretation = _get_gauge_interpretation(float(value), metric_name)
        hover_text = f"""
        <b>{symbol} - {metric_name}</b><br>
        Current Value: {float(value):.2f}<br>
        Range: -3 to +3<br>
        <b>Quick Synopsis:</b> {synopsis}<br>
        Interpretation: {interpretation}<br>
        <extra></extra>
        """
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=float(value),
            title={'text': indicator_title_text, 'font': {'size': flow_gauge_settings.get("indicator_font_size", 14)}},
            number={'font': {'size': flow_gauge_settings.get("number_font_size", 20)}},
            gauge={
                'axis': {'range': [-3, 3], 'tickwidth': 1, 'tickcolor': "darkblue"},
                'bar': {'color': "rgba(0,0,0,0)"},
                'steps': [
                    {'range': [-3, -2], 'color': '#d62728'},
                    {'range': [-2, -1], 'color': '#ff9896'},
                    {'range': [-1, 1], 'color': '#aec7e8'},
                    {'range': [1, 2], 'color': '#98df8a'},
                    {'range': [2, 3], 'color': '#2ca02c'}
                ],
                'threshold': {
                    'line': {'color': flow_gauge_settings.get("threshold_line_color", "white"), 'width': 3},
                    'thickness': 0.8, 'value': float(value)
                }
            }
        ))
        
        # Add invisible scatter point for custom hover
        fig.add_trace(go.Scatter(
            x=[0.5], y=[0.5],
            mode='markers',
            marker=dict(size=1, opacity=0),
            hovertemplate=hover_text,
            showlegend=False,
            name=""
        ))
        fig.update_layout(
            # Remove fixed height to allow responsive behavior
            margin=flow_gauge_settings.get("margin", {'t': 30, 'b': 30, 'l': 15, 'r': 15}),  # Reduced top margin since no main title
            template=PLOTLY_TEMPLATE,
            showlegend=False,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            hovermode='x',        # Use x-axis hover to reduce obstruction
            hoverdistance=20,     # Reduced hover distance to minimize obstruction
            autosize=True         # Enable auto-sizing for responsiveness
        )
        
        # Apply custom dark theme styling
        fig = apply_dark_theme_template(fig)
        
        # Remove grid lines from gauge charts
        fig.update_xaxes(showgrid=False, zeroline=False, visible=False)
        fig.update_yaxes(showgrid=False, zeroline=False, visible=False)

    if timestamp:
        fig = add_timestamp_annotation(fig, timestamp)

    # Create the graph with collapsible about section
    graph_component = dcc.Graph(
        id=component_id,
        figure=fig,
        config={
            'displayModeBar': False,
            'displaylogo': False,
            'responsive': True  # Enable Plotly's responsive mode
        },
        style={"height": "350px"}  # Keep original height but enable responsive
    )
    
    # Create collapsible about section with elite styling
    about_button = dbc.Button(
        "ℹ️ About", 
        id={"type": "about-toggle", "index": component_id}, 
        color="link", 
        size="sm", 
        className="p-0 text-elite-secondary mb-2 elite-focus-visible",
        style={'font-size': '0.75em'}
    )
    
    about_collapse = dbc.Collapse(
        html.Small(blurb_text, className="text-elite-secondary d-block mb-2", style={'font-size': '0.75em'}),
        id={"type": "about-collapse", "index": component_id},
        is_open=False
    )
    
    return html.Div([
        dbc.Card(
            dbc.CardBody([
                about_button,
                about_collapse,
                graph_component
            ], className="elite-card-body", style={"height": "auto"}),
            className="elite-card fade-in-up",
            style={"height": "auto"}
        )
    ], style={"height": "auto"})

def _create_recommendations_table(
    recommendations: List[ActiveRecommendationPayloadV2_5],
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    symbol: str
) -> html.Div:
    """Creates the display for the ATIF recommendations table using dash_table.DataTable."""
    main_dash_settings = _get_dashboard_settings(config)
    table_settings = main_dash_settings.get("recommendations_table", {})
    max_rationale_len = table_settings.get('max_rationale_length', 50)
    table_title = table_settings.get("title", "ATIF Recommendations")

    card_body_children: List[Union[html.H4, dbc.Alert, dash_table.DataTable, html.Small]] = [
        html.H4(table_title, className="elite-card-title")
    ]

    if not recommendations:
        card_body_children.append(dbc.Alert("No active recommendations.", color="info", className="mt-2 fade-in-up"))
    else:
        data_for_table = []
        for reco in recommendations:
            rationale = reco.target_rationale
            if rationale and len(rationale) > max_rationale_len:
                rationale = rationale[:max_rationale_len] + '...'

            data_for_table.append({
                'Strategy': reco.strategy_type,
                'Bias': reco.trade_bias,
                'Conviction': f"{reco.atif_conviction_score_at_issuance:.2f}",
                'Status': reco.status,
                'Entry': f"{reco.entry_price_initial:.2f}" if reco.entry_price_initial is not None else "N/A",
                'Stop': f"{reco.stop_loss_current:.2f}" if reco.stop_loss_current is not None else "N/A",
                'Target 1': f"{reco.target_1_current:.2f}" if reco.target_1_current is not None else "N/A",
                'Rationale': rationale
            })

        table_component = dash_table.DataTable(
            id=f"{ids.ID_RECOMMENDATIONS_TABLE}-{symbol.lower()}",
            columns=[{"name": i, "id": i} for i in data_for_table[0].keys()] if data_for_table else [],
            data=data_for_table,
            style_cell={
                'textAlign': 'left', 
                'padding': '12px', 
                'minWidth': '80px', 
                'width': 'auto', 
                'maxWidth': '200px',
                'backgroundColor': 'var(--elite-surface)',
                'color': 'var(--elite-text-primary)',
                'border': '1px solid var(--elite-border)',
                'fontFamily': 'var(--elite-font-family)'
            },
            style_header={
                'backgroundColor': 'var(--elite-primary)', 
                'fontWeight': 'bold', 
                'color': 'var(--elite-text-on-primary)',
                'border': '1px solid var(--elite-border)',
                'textAlign': 'center'
            },
            style_data={
                'backgroundColor': 'var(--elite-surface)', 
                'color': 'var(--elite-text-primary)',
                'border': '1px solid var(--elite-border)'
            },
            style_data_conditional=[
                {
                    'if': {'row_index': 'odd'},
                    'backgroundColor': 'var(--elite-surface-variant)'
                }
            ],
            style_as_list_view=True,
            page_size=table_settings.get("page_size", 5),
            sort_action="native",
            filter_action="native",
            css=[{
                'selector': '.dash-table-container',
                'rule': 'border-radius: var(--elite-border-radius); overflow: hidden;'
            }]
        )
        card_body_children.append(table_component)

    if timestamp:
        ts_format = config.config.visualization_settings.dashboard.get("timestamp_format", '%Y-%m-%d %H:%M:%S %Z')
        timestamp_text = f"Last updated: {timestamp.strftime(ts_format)}"
        card_body_children.append(html.Small(timestamp_text, className="text-elite-secondary d-block mt-2 text-end"))

    return html.Div([
        dbc.Card(
            dbc.CardBody(card_body_children, className="elite-card-body"),
            className="elite-card fade-in-up"
        )
    ])

def _create_gib_gauge(
    metric_name: str,
    value: Optional[float],
    component_id: str,
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    symbol: str,
    is_dollar_value: bool = False
) -> html.Div:
    """Creates a GIB gauge for gamma imbalance metrics."""
    main_dash_settings = _get_dashboard_settings(config)
    gib_gauge_settings = main_dash_settings.get("gib_gauge", {})
    fig_height = gib_gauge_settings.get("height", 350)
    
    # Chart blurbs for user guidance - ENHANCED WITH TRADING INSIGHTS
    gib_blurbs = {
        "GIB OI-Based": "🎯 **GIB: The Market Stability Predictor** ⚖️\n\n**What it reveals:** Shows whether market makers are positioned to dampen or amplify price moves. This is your volatility crystal ball!\n\n**Trading Applications:**\n• **+20K to +50K+**: 🛡️ **FORTRESS MODE** - MMs are long gamma, expect price stability and range-bound action. Perfect for iron condors, strangles, and theta strategies\n• **+5K to +20K**: 📊 **STABLE ZONE** - Moderate stability, good for neutral strategies and covered calls\n• **-5K to +5K**: ⚡ **NEUTRAL TERRITORY** - Mixed signals, use other indicators for direction\n• **-20K to -5K**: 🌪️ **VOLATILITY BREWING** - MMs short gamma, expect increased movement. Great for long straddles and momentum plays\n• **-50K to -20K**: 🚨 **EXPLOSION ZONE** - High volatility expected! Prime time for volatility plays and breakout strategies\n\n**Pro Tips:** 💡 When GIB is deeply negative and price approaches key levels, expect EXPLOSIVE moves as dealers chase the market. Use this for timing entries into volatility plays. Positive GIB = sell premium, Negative GIB = buy premium!",

        "TD-GIB": "⏰ **TD-GIB: The Expiration Magnet** 🧲\n\n**What it reveals:** Shows how time decay affects dealer positioning, revealing where prices want to 'pin' as expiration approaches. Your secret weapon for OpEx trading!\n\n**Trading Applications:**\n• **High Positive Values**: 📌 **PIN RISK ALERT** - Price likely to gravitate toward strikes with heavy gamma. Fade moves away from pin levels\n• **Moderate Values**: 🎯 **GENTLE PULL** - Some pin effect, but other factors may dominate\n• **Low/Negative Values**: 🆓 **FREE RANGE** - Minimal pin effect, price can move more freely\n\n**Pro Tips:** 💡 TD-GIB is GOLD for OpEx week trading! When TD-GIB is high, price often gets 'pinned' to major strikes. Use this to:\n• Sell premium near pin levels\n• Fade breakouts that move away from pins\n• Time entries for mean reversion plays\n• Avoid directional bets when pin risk is extreme",

        "HP-EOD": "🔚 **HP-EOD: The 3:30 PM Crystal Ball** 🔮\n\n**What it reveals:** Predicts the direction and intensity of dealer hedging flows in the final 30-60 minutes of trading. This is your end-of-day edge!\n\n**Trading Applications:**\n• **Large Positive (+50K+)**: 🔴 **SELLING TSUNAMI** - Dealers need to sell heavily into close. Expect downward pressure, consider puts or short strategies\n• **Moderate Positive (+10K to +50K)**: 📉 **SELLING PRESSURE** - Some downward bias expected\n• **Near Zero (-10K to +10K)**: 😐 **BALANCED** - Minimal directional pressure expected\n• **Moderate Negative (-50K to -10K)**: 📈 **BUYING SUPPORT** - Some upward bias expected\n• **Large Negative (-50K+)**: 🟢 **BUYING AVALANCHE** - Dealers need to buy aggressively. Expect upward pressure, consider calls or long strategies\n\n**Pro Tips:** 💡 HP-EOD is your 'Power Hour' playbook! Use it to:\n• Position for EOD moves 30-60 minutes before close\n• Size your positions based on HP-EOD magnitude\n• Fade early moves that go against HP-EOD direction\n• Combine with volume analysis for maximum edge"
    }
    
    blurb_text = gib_blurbs.get(metric_name, f"{metric_name} gamma analysis")
    gauge_title_text = f"{metric_name}"  # Just the metric name to prevent overlapping
    indicator_title_text = metric_name

    if value is None or pd.isna(value):
        fig = create_empty_figure(title=gauge_title_text, height=fig_height, reason="Data N/A")
    else:
        # Dynamic scaling based on value type
        if is_dollar_value:
            # For HP_EOD (dollar values)
            abs_val = abs(float(value))
            if abs_val > 50000:
                axis_range = [-100000, 100000]
            elif abs_val > 10000:
                axis_range = [-50000, 50000]
            else:
                axis_range = [-10000, 10000]
            
            steps = gib_gauge_settings.get("steps_dollar", [
                {'range': [axis_range[0], axis_range[0]*0.5], 'color': '#d62728'},
                {'range': [axis_range[0]*0.5, axis_range[0]*0.1], 'color': '#ff9896'},
                {'range': [axis_range[0]*0.1, axis_range[1]*0.1], 'color': '#aec7e8'},
                {'range': [axis_range[1]*0.1, axis_range[1]*0.5], 'color': '#98df8a'},
                {'range': [axis_range[1]*0.5, axis_range[1]], 'color': '#2ca02c'}
            ])
        elif metric_name == "GIB OI-Based":
            # For GIB (large values)
            abs_val = abs(float(value))
            if abs_val > 50000:
                axis_range = [-100000, 100000]
            elif abs_val > 10000:
                axis_range = [-50000, 50000]
            else:
                axis_range = [-10000, 10000]
                
            steps = gib_gauge_settings.get("steps_gib", [
                {'range': [axis_range[0], axis_range[0]*0.5], 'color': '#d62728'},
                {'range': [axis_range[0]*0.5, axis_range[0]*0.1], 'color': '#ff9896'},
                {'range': [axis_range[0]*0.1, axis_range[1]*0.1], 'color': '#aec7e8'},
                {'range': [axis_range[1]*0.1, axis_range[1]*0.5], 'color': '#98df8a'},
                {'range': [axis_range[1]*0.5, axis_range[1]], 'color': '#2ca02c'}
            ])
        else:
            axis_range = gib_gauge_settings.get("axis_range", [-1, 1])
            steps = gib_gauge_settings.get("steps", [
                {'range': [-1, -0.5], 'color': '#d62728'},
                {'range': [-0.5, -0.1], 'color': '#ff9896'},
                {'range': [-0.1, 0.1], 'color': '#aec7e8'},
                {'range': [0.1, 0.5], 'color': '#98df8a'},
                {'range': [0.5, 1], 'color': '#2ca02c'}
            ])

        synopsis, interpretation = _get_gauge_interpretation(float(value), metric_name)
        hover_text = f"""
        <b>{symbol} - {metric_name}</b><br>
        Current Value: {float(value):,.0f}<br>
        Range: {axis_range[0]:,} to {axis_range[1]:,}<br>
        <b>Quick Synopsis:</b> {synopsis}<br>
        Interpretation: {interpretation}<br>
        <extra></extra>
        """
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=float(value),
            title={'text': indicator_title_text, 'font': {'size': gib_gauge_settings.get("indicator_font_size", 14)}},
            number={'font': {'size': gib_gauge_settings.get("number_font_size", 20)}},
            gauge={
                'axis': {'range': axis_range, 'tickwidth': 1, 'tickcolor': "darkblue"},
                'bar': {'color': "rgba(0,0,0,0)"},
                'steps': steps,
                'threshold': {
                    'line': {'color': gib_gauge_settings.get("threshold_line_color", "white"), 'width': 3},
                    'thickness': 0.8, 'value': float(value)
                }
            }
        ))
        
        # Add invisible scatter point for custom hover
        fig.add_trace(go.Scatter(
            x=[0.5], y=[0.5],
            mode='markers',
            marker=dict(size=1, opacity=0),
            hovertemplate=hover_text,
            showlegend=False,
            name=""
        ))
        fig.update_layout(
            # Remove fixed height to allow responsive behavior
            margin=gib_gauge_settings.get("margin", {'t': 30, 'b': 30, 'l': 15, 'r': 15}),  # Reduced top margin since no main title
            template=PLOTLY_TEMPLATE,
            showlegend=False,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            autosize=True         # Enable auto-sizing for responsiveness
        )
        
        # Apply custom dark theme styling
        fig = apply_dark_theme_template(fig)
        
        # Remove grid lines from gauge charts
        fig.update_xaxes(showgrid=False, zeroline=False, visible=False)
        fig.update_yaxes(showgrid=False, zeroline=False, visible=False)

    if timestamp:
        fig = add_timestamp_annotation(fig, timestamp)

    # Create the graph with collapsible about section
    graph_component = dcc.Graph(
        id=component_id,
        figure=fig,
        config={
            'displayModeBar': False,
            'displaylogo': False,
            'responsive': True  # Enable Plotly's responsive mode
        },
        style={"height": "350px"}  # Keep original height but enable responsive
    )
    
    # Create collapsible about section with elite styling
    about_button = dbc.Button(
        "ℹ️ About", 
        id={"type": "about-toggle", "index": component_id}, 
        color="link", 
        size="sm", 
        className="p-0 text-elite-secondary mb-2 elite-focus-visible",
        style={'font-size': '0.75em'}
    )
    
    about_collapse = dbc.Collapse(
        html.Small(blurb_text, className="text-elite-secondary d-block mb-2", style={'font-size': '0.75em'}),
        id={"type": "about-collapse", "index": component_id},
        is_open=False
    )
    
    return html.Div([
        dbc.Card(
            dbc.CardBody([
                about_button,
                about_collapse,
                graph_component
            ], className="elite-card-body", style={"height": "auto"}),
            className="elite-card fade-in-up",
            style={"height": "auto"}
        )
    ], style={"height": "auto"})

def _create_mini_heatmap(
    metric_name: str,
    strike_data: List[ProcessedStrikeLevelMetricsV2_5],
    metric_field: str,
    component_id: str,
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    symbol: str,
    current_price: Optional[float]
) -> html.Div:
    """Creates a mini-heatmap for strike-level metrics like SGDHP and UGCH."""
    main_dash_settings = _get_dashboard_settings(config)
    heatmap_settings = main_dash_settings.get("mini_heatmap", {})
    fig_height = 300
    
    # Chart blurbs for user guidance - ENHANCED WITH TRADING INSIGHTS
    chart_blurbs = {
        "SGDHP": "🎯 **SGDHP: The Market Maker Defense Map** 🛡️\n\n**What it reveals:** Shows exactly where market makers will fight hardest to defend price levels. These are your dynamic support and resistance zones that adapt in real-time!\n\n**Trading Applications:**\n• **Bright Cyan (High Positive)**: 🟢 **FORTRESS SUPPORT** - MMs will aggressively defend this level. Perfect for:\n  - Buying dips near these strikes\n  - Selling puts with strikes at these levels\n  - Setting stop losses just below these zones\n• **Bright Magenta (High Negative)**: 🔴 **IRON CEILING** - MMs will cap rallies here. Ideal for:\n  - Selling calls near these strikes\n  - Taking profits on longs\n  - Shorting bounces to these levels\n• **Neutral Colors**: 😐 **No Man's Land** - Minimal MM defense, price can move freely\n\n**Pro Tips:** 💡 SGDHP levels are DYNAMIC - they change as flow evolves! Use them for:\n• Intraday support/resistance that actually works\n• Strike selection for spreads and covered calls\n• Risk management - place stops beyond SGDHP zones\n• Scalping opportunities at strong SGDHP levels",

        "UGCH": "⚡ **UGCH: The Greek Convergence Detector** 🎯\n\n**What it reveals:** Identifies strikes where ALL Greeks align like planets - these are the most structurally significant levels in the entire options chain. When Greeks converge, magic happens!\n\n**Trading Applications:**\n• **High Values (5+)**: 🌟 **GREEK SUPERNOVA** - Maximum structural significance. Use for:\n  - High-conviction directional plays\n  - Strike selection for spreads\n  - Key levels for technical analysis\n  - Maximum impact option strategies\n• **Moderate Values (2-5)**: ⭐ **STRONG CONFLUENCE** - Significant structural importance\n• **Low Values (0-2)**: 💫 **MINOR CONFLUENCE** - Some significance but not critical\n• **Negative Values**: 🌑 **GREEK DISCORD** - Conflicting forces, avoid these strikes\n\n**Pro Tips:** 💡 UGCH is your 'X marks the spot' for options trading! Use it to:\n• Choose the BEST strikes for your strategies\n• Identify where the biggest moves will have maximum impact\n• Find strikes where small price moves create big P&L swings\n• Avoid 'dead zones' where Greeks work against each other\n• Time entries when price approaches high-UGCH strikes"
    }
    
    blurb_text = chart_blurbs.get(metric_name, f"{metric_name} strike-level analysis")
    heatmap_title_text = f"{metric_name} Mini-Heatmap"  # Just the metric name to prevent overlapping

    if not strike_data or current_price is None:
        fig = create_empty_figure(title=heatmap_title_text, height=fig_height, reason="Data N/A")
    else:
        price_range = current_price * 0.05
        relevant_strikes = []
        values = []
        
        for strike_info in strike_data:
            if abs(strike_info.strike - current_price) <= price_range:
                metric_value = getattr(strike_info, metric_field, None)
                if metric_value is not None and pd.notna(metric_value):
                    relevant_strikes.append(strike_info.strike)
                    values.append(metric_value)
        
        if not relevant_strikes:
            fig = create_empty_figure(title=heatmap_title_text, height=fig_height, reason="No ATM/NTM Data")
        else:
            sorted_data = sorted(zip(relevant_strikes, values))
            strikes, vals = zip(*sorted_data)
            
            # Custom color scheme for SGDHP: Cyan-Magenta gradient
            if metric_name == "SGDHP":
                colorscale = [
                    [0.0, '#FF00FF'],    # Magenta for negative (strong resistance)
                    [0.2, '#FF66FF'],    # Light magenta
                    [0.4, '#CCCCCC'],    # Neutral gray
                    [0.6, '#66FFFF'],    # Light cyan  
                    [1.0, '#00FFFF']     # Cyan for positive (strong support)
                ]
            else:
                # Keep default for UGCH
                colorscale = heatmap_settings.get("colorscale", "RdYlGn")
            
            # Create custom hover template for heatmap
            hover_template = (
                f"<b>{symbol} - {metric_name}</b><br>"
                "Strike: $%{x:,.0f}<br>"
                f"Current Price: ${current_price:,.2f}<br>"
                "Distance: $%{customdata[2]:,.2f} (%{customdata[3]:.1f}%)<br>"
                f"{metric_name} Value: %{{z:.2f}}<br>"
                "Interpretation: %{customdata[4]}<br>"
                "<extra></extra>"
            )
            
            # Prepare custom data for hover
            custom_data = []
            for strike, val in zip(strikes, vals):
                distance_from_current = abs(strike - current_price)
                pct_from_current = (distance_from_current / current_price) * 100
                interpretation = _get_heatmap_interpretation(val, metric_name)
                custom_data.append([strike, val, distance_from_current, pct_from_current, interpretation])
            
            fig = go.Figure(data=go.Heatmap(
                z=[vals],
                x=strikes,
                y=[metric_name],
                colorscale=colorscale,
                showscale=True,
                colorbar=dict(len=0.5, thickness=10),
                hovertemplate=hover_template,
                customdata=[custom_data]
            ))
            
            fig.update_layout(
                title={'text': heatmap_title_text, 'y':0.85, 'x':0.5, 'xanchor': 'center', 'yanchor': 'top', 'font': {'size': 12}},
                height=fig_height,  # Normal height without extra space for blurb
                margin=heatmap_settings.get("margin", {'t': 60, 'b': 30, 'l': 40, 'r': 40}),  # Increased top margin for title
                template=PLOTLY_TEMPLATE,
                xaxis_title="Strike",
                yaxis_title="",
                showlegend=False
            )
            
            # Apply custom dark theme styling
            fig = apply_dark_theme_template(fig)

    if timestamp:
        fig = add_timestamp_annotation(fig, timestamp)

    # Create the graph with collapsible about section
    graph_component = dcc.Graph(
        id=component_id, 
        figure=fig,
        config={
            'displayModeBar': False,
            'displaylogo': False
        },
        style={"height": "300px"}
    )
    
    # Create collapsible about section with elite styling
    about_button = dbc.Button(
        "ℹ️ About", 
        id={"type": "about-toggle", "index": component_id}, 
        color="link", 
        size="sm", 
        className="p-0 text-elite-secondary mb-2 elite-focus-visible",
        style={'font-size': '0.75em'}
    )
    
    about_collapse = dbc.Collapse(
        html.Small(blurb_text, className="text-elite-secondary d-block mb-2", style={'font-size': '0.75em'}),
        id={"type": "about-collapse", "index": component_id},
        is_open=False
    )
    
    return html.Div([
        dbc.Card(
            dbc.CardBody([
                about_button,
                about_collapse,
                graph_component
            ], className="elite-card-body", style={"height": "auto"}),
            className="elite-card fade-in-up",
            style={"height": "auto"}
        ),
    ], style={"height": "auto"})

def _create_ticker_context_summary(
    ticker_context_dict: Optional[Any],
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    symbol: str
) -> html.Div:
    """Creates the ticker context summary display."""
    main_dash_settings = _get_dashboard_settings(config)
    context_settings = main_dash_settings.get("ticker_context", {})
    context_title = context_settings.get("title", "Ticker Context")

    card_body_children = []

    if not ticker_context_dict:
        card_body_children.append(dbc.Alert("No context data available.", color="info", className="mt-2"))
    else:
        context_flags = []
        
        if hasattr(ticker_context_dict, 'is_0dte') and ticker_context_dict.is_0dte:
            context_flags.append(f"{symbol}: 0DTE")
        if hasattr(ticker_context_dict, 'is_1dte') and ticker_context_dict.is_1dte:
            context_flags.append(f"{symbol}: 1DTE")
        if hasattr(ticker_context_dict, 'active_intraday_session') and ticker_context_dict.active_intraday_session:
            context_flags.append(f"Session: {ticker_context_dict.active_intraday_session}")
        if hasattr(ticker_context_dict, 'vix_spy_price_divergence_strong_negative') and ticker_context_dict.vix_spy_price_divergence_strong_negative:
            context_flags.append("Pattern: VIX_DIVERGENCE_ACTIVE")
        if hasattr(ticker_context_dict, 'is_fomc_meeting_day') and ticker_context_dict.is_fomc_meeting_day:
            context_flags.append("Event: FOMC_DAY")
        if hasattr(ticker_context_dict, 'earnings_approaching_flag') and ticker_context_dict.earnings_approaching_flag:
            context_flags.append("Event: EARNINGS_APPROACHING")
        
        if context_flags:
            for flag in context_flags:
                card_body_children.append(
                    dbc.Badge(flag, color="primary", className="me-1 mb-1")
                )
        else:
            card_body_children.append(
                html.Small("No significant context flags active.", className="text-muted")
            )

    if timestamp:
        ts_format = config.config.visualization_settings.dashboard.get("timestamp_format", '%Y-%m-%d %H:%M:%S %Z')
        timestamp_text = f"Last updated: {timestamp.strftime(ts_format)}"
        card_body_children.append(html.Small(timestamp_text, className="text-muted d-block mt-2 text-end"))

    return html.Div([
        dbc.Card(
            dbc.CardBody(card_body_children),
            className="elite-card fade-in-up"
        )
    ])

def _create_atif_recommendations_table(
    atif_recommendations: list,  # List of ATIFStrategyDirectivePayloadV2_5 or similar
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    symbol: str,
    db_manager=None  # Add database manager for enhanced functionality
) -> html.Div:
    """Creates a robust display for ATIF recommendations using dash_table.DataTable with database integration."""
    main_dash_settings = _get_dashboard_settings(config)
    table_settings = main_dash_settings.get("atif_recommendations_table", {})
    max_rationale_len = table_settings.get('max_rationale_length', 80)
    table_title = table_settings.get("title", "🎯 ATIF Strategy Recommendations")

    # Collapsible About Section
    about_button = dbc.Button(
        "ℹ️ About ATIF", 
        id={"type": "about-toggle", "index": f"atif-insights-{symbol}"}, 
        color="link", 
        size="sm", 
        className="p-0 text-elite-secondary mb-2 elite-focus-visible",
        style={'font-size': '0.75em'}
    )
    about_collapse = dbc.Collapse(
        html.Small([
            "🎯 ", html.Strong("ATIF Strategy Recommendations"), " - Your AI-powered trading strategist! ",
            html.Br(), html.Br(),
            "📊 ", html.Strong("What you see:"), " Real-time strategy recommendations generated by the Adaptive Trade Idea Framework (ATIF). Each recommendation includes:",
            html.Br(),
            "• ", html.Strong("Strategy Type:"), " The specific options strategy recommended",
            html.Br(),
            "• ", html.Strong("Conviction Score:"), " System confidence (0.0-1.0) - higher = stronger conviction",
            html.Br(),
            "• ", html.Strong("DTE Range:"), " Optimal days-to-expiration for the strategy",
            html.Br(),
            "• ", html.Strong("Delta Targets:"), " Recommended delta ranges for legs",
            html.Br(),
            "• ", html.Strong("Context:"), " Market regime and rationale at time of recommendation",
            html.Br(), html.Br(),
            "💡 ", html.Strong("Pro Tips:"), " Focus on recommendations with conviction > 0.75 for highest probability setups. ATIF considers flow analysis, volatility regime, historical performance, and market structure to generate these insights.",
            html.Br(), html.Br(),
            "⚠️ ", html.Strong("No recommendations?"), " ATIF is waiting for clearer market signals or better risk/reward setups. Patience is often the best strategy!"
        ], className="text-elite-secondary d-block mb-2", style={'font-size': '0.75em'}),
        id={"type": "about-collapse", "index": f"atif-insights-{symbol}"},
        is_open=False
    )

    card_body_children = [
        html.H4(table_title, className="elite-card-title"),
        about_button,
        about_collapse
    ]

    # Get additional data from database if available
    db_recommendations = []
    performance_stats = {}
    if db_manager:
        try:
            db_recommendations = db_manager.get_recent_atif_recommendations(symbol, limit=5)
            performance_stats = db_manager.get_atif_performance_stats(symbol, days=30)
        except Exception as e:
            logger.warning(f"Could not fetch ATIF data from database: {e}")

    # Combine live and database recommendations
    all_recommendations = list(atif_recommendations) + db_recommendations

    if not all_recommendations:
        # Enhanced no-recommendations display with performance context
        alert_content = [
            html.Div([
                html.I(className="fas fa-clock me-2"),
                html.Strong("No Active ATIF Recommendations"),
                html.Br(),
                html.Small([
                    "🔍 ATIF is analyzing market conditions and waiting for optimal setups. ",
                    "This disciplined approach helps avoid low-probability trades."
                ], className="text-muted")
            ])
        ]

        if performance_stats.get('total_trades', 0) > 0:
            win_rate = performance_stats.get('win_rate', 0)
            total_trades = performance_stats.get('total_trades', 0)
            alert_content.append(
                html.Small([
                    html.Br(),
                    f"📊 Recent Performance: {win_rate:.1%} win rate over {total_trades} trades (30 days)"
                ], className="text-info")
            )

        card_body_children.append(
            dbc.Alert(alert_content, color="info", className="mt-2 fade-in-up")
        )
    else:
        data_for_table = []
        # Process recommendations with enhanced data
        for i, reco in enumerate(all_recommendations[:5]):  # Limit to top 5
            # Handle both Pydantic models and dict objects
            if hasattr(reco, 'model_dump'):
                rec = reco.model_dump()
                is_live = i < len(atif_recommendations)  # First items are live recommendations
            else:
                rec = reco
                is_live = False  # Database recommendations
            # Extract rationale
            rationale = rec.get('supportive_rationale_components', {}).get('rationale', '')
            if not rationale:
                rationale = rec.get('rationale_summary', 'Strategy recommended by ATIF analysis')

            if rationale and len(rationale) > max_rationale_len:
                rationale_short = rationale[:max_rationale_len] + '...'
            else:
                rationale_short = rationale
            assessment = rec.get('assessment_profile', {})
            context_flags = []
            if assessment:
                if assessment.get('bullish_assessment_score', 0) > assessment.get('bearish_assessment_score', 0):
                    context_flags.append('Bullish')
                elif assessment.get('bearish_assessment_score', 0) > assessment.get('bullish_assessment_score', 0):
                    context_flags.append('Bearish')
                if assessment.get('vol_expansion_score', 0) > 0:
                    context_flags.append('Vol Expansion')
                if assessment.get('vol_contraction_score', 0) > 0:
                    context_flags.append('Vol Contraction')
                if assessment.get('mean_reversion_likelihood', 0) > 0.5:
                    context_flags.append('Mean Reversion')
            bias = 'Bullish' if assessment.get('bullish_assessment_score', 0) > assessment.get('bearish_assessment_score', 0) else 'Bearish'
            color = '#2ca02c' if bias == 'Bullish' else '#d62728'
            data_for_table.append({
                'Strategy': rec.get('selected_strategy_type', 'N/A'),
                'Bias': bias,
                'Conviction': f"{rec.get('final_conviction_score_from_atif', 0):.2f}",
                'Target DTE': f"{rec.get('target_dte_min', 'N/A')} - {rec.get('target_dte_max', 'N/A')}",
                'Delta (Long)': f"{rec.get('target_delta_long_leg_min', 'N/A')} - {rec.get('target_delta_long_leg_max', 'N/A')}",
                'Delta (Short)': f"{rec.get('target_delta_short_leg_min', 'N/A')} - {rec.get('target_delta_short_leg_max', 'N/A')}",
                'Underlying Price': f"{rec.get('underlying_price_at_decision', 'N/A')}",
                'Rationale': rationale_short,
                'Context': ', '.join(context_flags),
                'Full Rationale': rationale,
                'Assessment': assessment,
                'Color': color
            })
        columns = [
            {"name": "Strategy", "id": "Strategy"},
            {"name": "Bias", "id": "Bias"},
            {"name": "Conviction", "id": "Conviction"},
            {"name": "Target DTE", "id": "Target DTE"},
            {"name": "Delta (Long)", "id": "Delta (Long)"},
            {"name": "Delta (Short)", "id": "Delta (Short)"},
            {"name": "Underlying Price", "id": "Underlying Price"},
            {"name": "Rationale", "id": "Rationale"},
            {"name": "Context", "id": "Context"}
        ]
        table_component = dash_table.DataTable(
            id=f"atif-recommendations-table-{symbol.lower()}",
            columns=columns,
            data=data_for_table,
            style_cell={
                'textAlign': 'left',
                'padding': '10px',
                'minWidth': '80px',
                'width': 'auto',
                'maxWidth': '220px',
                'backgroundColor': 'var(--elite-surface)',
                'color': 'var(--elite-text-primary)',
                'border': '1px solid var(--elite-border)',
                'fontFamily': 'var(--elite-font-family)'
            },
            style_header={
                'backgroundColor': 'var(--elite-primary)',
                'fontWeight': 'bold',
                'color': 'var(--elite-text-on-primary)',
                'border': '1px solid var(--elite-border)',
                'textAlign': 'center'
            },
            style_data={
                'backgroundColor': 'var(--elite-surface)',
                'color': 'var(--elite-text-primary)',
                'border': '1px solid var(--elite-border)'
            },
            style_data_conditional=[
                {
                    'if': {'filter_query': '{Bias} = "Bullish"'},
                    'backgroundColor': '#193c1a',
                },
                {
                    'if': {'filter_query': '{Bias} = "Bearish"'},
                    'backgroundColor': '#3c1a1a',
                },
                {
                    'if': {'row_index': 'odd'},
                    'backgroundColor': 'var(--elite-surface-variant)'
                }
            ],
            style_as_list_view=True,
            page_size=table_settings.get("page_size", 5),
            sort_action="native",
            filter_action="native",
            tooltip_data=[
                {
                    'Rationale': {'value': row['Full Rationale'], 'type': 'markdown'}
                } for row in data_for_table
            ],
            tooltip_duration=None,
            css=[{
                'selector': '.dash-table-container',
                'rule': 'border-radius: var(--elite-border-radius); overflow: hidden;'
            }]
        )
        card_body_children.append(table_component)

    if timestamp:
        ts_format = config.config.visualization_settings.dashboard.get("timestamp_format", '%Y-%m-%d %H:%M:%S %Z')
        timestamp_text = f"Last updated: {timestamp.strftime(ts_format)}"
        card_body_children.append(html.Small(timestamp_text, className="text-elite-secondary d-block mt-2 text-end"))

    return html.Div([
        dbc.Card(
            dbc.CardBody(card_body_children, className="elite-card-body"),
            className="elite-card fade-in-up"
        )
    ])

def _create_atif_insights_panel(
    symbol: str,
    processed_data,
    atif_recommendations: list,
    config: ConfigManagerV2_5,
    timestamp: Optional[datetime],
    db_manager=None
) -> html.Div:
    """Creates an intelligent insights panel powered by ATIF analysis."""

    # Generate insights using the AI dashboard intelligence module (consolidated)
    try:
        from dashboard_application.modes.ai_dashboard.intelligence import generate_unified_ai_insights

        # Create a minimal bundle for insights generation
        from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5

        # Create bundle with available data
        insights_bundle = FinalAnalysisBundleV2_5(
            processed_data_bundle=processed_data,
            atif_recommendations_v2_5=atif_recommendations,
            news_intelligence_v2_5=None,  # Will be populated if available
            system_status_messages=[]
        )

        # Generate insights using consolidated AI intelligence
        insights_list = generate_unified_ai_insights(insights_bundle, symbol)

        # Convert to expected format for compatibility
        insights = []
        for i, insight_text in enumerate(insights_list):
            insights.append({
                'insight_type': 'AI_UNIFIED',
                'insight_title': f"Insight {i+1}",
                'insight_description': insight_text,
                'confidence_score': 0.8,  # Default confidence
                'impact_score': 0.7,      # Default impact
                'time_horizon': 'Current'
            })
    except Exception as e:
        logger.error(f"Failed to generate ATIF insights: {e}")
        insights = []

    # Create about button and collapse
    about_button = dbc.Button(
        [html.I(className="fas fa-info-circle me-2"), "About ATIF Insights"],
        id={"type": "about-button", "index": f"atif-insights-{symbol}"},
        color="link",
        size="sm",
        className="p-0 text-elite-secondary"
    )

    about_collapse = dbc.Collapse(
        html.Small([
            "🧠 ", html.Strong("ATIF Insights"), " - Your AI-powered market intelligence system! ",
            html.Br(), html.Br(),
            "🔍 ", html.Strong("What you see:"), " Real-time market insights generated by analyzing:",
            html.Br(),
            "• ", html.Strong("Flow Patterns:"), " Unusual institutional activity and smart money moves",
            html.Br(),
            "• ", html.Strong("Market Regime:"), " Current volatility environment and structural conditions",
            html.Br(),
            "• ", html.Strong("Risk Factors:"), " Potential threats and opportunities in the current setup",
            html.Br(),
            "• ", html.Strong("Performance Context:"), " Historical success rates and pattern recognition",
            html.Br(), html.Br(),
            "💡 ", html.Strong("How to use:"), " Each insight includes confidence and impact scores. Focus on high-impact insights (0.8+) for actionable intelligence. Time horizons indicate how long the insight remains relevant.",
            html.Br(), html.Br(),
            "🎯 ", html.Strong("Pro tip:"), " Combine insights with ATIF recommendations for maximum edge. When multiple insights align, conviction increases significantly!"
        ], className="text-elite-secondary d-block mb-2", style={'font-size': '0.75em'}),
        id={"type": "about-collapse", "index": f"atif-insights-{symbol}"},
        is_open=False
    )

    card_body_children = [
        html.H4("🧠 ATIF Market Insights", className="elite-card-title"),
        about_button,
        about_collapse
    ]

    if not insights:
        card_body_children.append(
            dbc.Alert([
                html.Div([
                    html.I(className="fas fa-search me-2"),
                    html.Strong("Analyzing Market Conditions"),
                    html.Br(),
                    html.Small([
                        "🔍 ATIF is processing current market data to generate actionable insights. ",
                        "Check back in a few moments for fresh intelligence."
                    ], className="text-muted")
                ])
            ], color="info", className="mt-2 fade-in-up")
        )
    else:
        # Create insights cards
        insights_container = []

        for insight in insights[:6]:  # Show top 6 insights
            # Determine alert color based on insight type
            if insight.insight_type in ["FLOW_EXTREME", "VOLATILITY_EXPLOSION", "HIGH_CONVICTION_SIGNAL"]:
                alert_color = "danger"
                icon = "🚨"
            elif insight.insight_type in ["REGIME_OPPORTUNITY", "TREND_CONFIRMATION", "PERFORMANCE_STRENGTH"]:
                alert_color = "success"
                icon = "✅"
            elif insight.insight_type in ["SMART_MONEY_DIVERGENCE", "SIGNAL_CONFLICT", "PERFORMANCE_WARNING"]:
                alert_color = "warning"
                icon = "⚠️"
            else:
                alert_color = "info"
                icon = "💡"

            # Create insight card
            insight_card = dbc.Alert([
                html.Div([
                    html.Div([
                        html.H6([
                            icon, " ", insight.insight_title
                        ], className="mb-1"),
                        html.P(insight.insight_description, className="mb-2"),
                        html.Div([
                            dbc.Badge([
                                html.I(className="fas fa-bullseye me-1"),
                                f"Impact: {insight.impact_score:.1f}"
                            ], color="primary", className="me-2"),
                            dbc.Badge([
                                html.I(className="fas fa-chart-line me-1"),
                                f"Confidence: {insight.confidence_score:.1f}"
                            ], color="secondary", className="me-2"),
                            dbc.Badge([
                                html.I(className="fas fa-clock me-1"),
                                insight.time_horizon
                            ], color="info", className="me-2"),
                            dbc.Badge([
                                html.I(className="fas fa-tag me-1"),
                                insight.insight_category
                            ], color="light", text_color="dark")
                        ], className="d-flex flex-wrap")
                    ])
                ])
            ], color=alert_color, className="mb-2 fade-in-up")

            insights_container.append(insight_card)

        card_body_children.extend(insights_container)

    if timestamp:
        ts_format = config.config.visualization_settings.dashboard.get("timestamp_format", '%Y-%m-%d %H:%M:%S %Z')
        timestamp_text = f"Last updated: {timestamp.strftime(ts_format)}"
        card_body_children.append(html.Small(timestamp_text, className="text-elite-secondary d-block mt-2 text-end"))

    return html.Div([
        dbc.Card(
            dbc.CardBody(card_body_children, className="elite-card-body"),
            className="elite-card fade-in-up"
        )
    ])

# AI functions will be added back when fully implemented

# --- Main Layout Function ---

def create_layout(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5, db_manager=None) -> html.Div:
    """Creates the complete layout for the Main Dashboard mode. Strict Pydantic-first: validates all data at the UI boundary."""
    try:
        # --- Pydantic-first validation at entry ---
        if not isinstance(bundle, FinalAnalysisBundleV2_5):
            logger.error("Input bundle is not a FinalAnalysisBundleV2_5 instance.")
            return html.Div([dbc.Card(dbc.CardBody([dbc.Alert("Internal error: Invalid analysis bundle type.", color="danger")]))])
        try:
            bundle.model_validate(bundle.model_dump())
        except ValidationError as e:
            logger.error(f"Bundle validation error: {e}")
            return html.Div([dbc.Card(dbc.CardBody([dbc.Alert("Internal error: Analysis bundle failed validation.", color="danger")]))])
        if not bundle or not bundle.processed_data_bundle:
            return html.Div([dbc.Card(dbc.CardBody([dbc.Alert("Analysis data is not available. Cannot render Main Dashboard.", color="danger")]))])

        processed_data = bundle.processed_data_bundle
        und_data = processed_data.underlying_data_enriched
        strike_data = processed_data.strike_level_data_with_metrics
        symbol = bundle.target_symbol or "Unknown"
        bundle_timestamp = bundle.bundle_timestamp
        current_price = und_data.price if und_data else None

        # --- Validate nested Pydantic models/lists ---
        if und_data is not None and not hasattr(und_data, 'model_dump'):
            logger.error("underlying_data_enriched is not a Pydantic model.")
            return html.Div([dbc.Card(dbc.CardBody([dbc.Alert("Internal error: Underlying data is not valid.", color="danger")]))])
        if strike_data is not None:
            if not isinstance(strike_data, list) or not all(hasattr(x, 'model_dump') for x in strike_data):
                logger.error("strike_level_data_with_metrics is not a list of Pydantic models.")
                return html.Div([dbc.Card(dbc.CardBody([dbc.Alert("Internal error: Strike data is not valid.", color="danger")]))])
        if bundle.active_recommendations_v2_5 is not None:
            if not isinstance(bundle.active_recommendations_v2_5, list) or not all(hasattr(x, 'model_dump') for x in bundle.active_recommendations_v2_5):
                logger.error("active_recommendations_v2_5 is not a list of Pydantic models.")
                return html.Div([dbc.Card(dbc.CardBody([dbc.Alert("Internal error: Recommendations data is not valid.", color="danger")]))])

        # --- ATIF Recommendations Table Integration (Temporarily disabled) ---
        # atif_recommendations = []
        # if hasattr(bundle, 'atif_recommendations_v2_5'):
        #     atif_recommendations = getattr(bundle, 'atif_recommendations_v2_5', [])
        # if not atif_recommendations and hasattr(bundle, 'active_recommendations_v2_5'):
        #     atif_recommendations = bundle.active_recommendations_v2_5

        return html.Div(
            id=ids.ID_MAIN_DASHBOARD_CONTAINER,
            children=[
                dbc.Container(
                    fluid=True,
                    children=[
                        # Row 2: Flow Metrics (VAPI-FA, DWFD, TW-LAF)
                        dbc.Row([
                            dbc.Col(_create_flow_gauge("VAPI-FA", und_data.vapi_fa_z_score_und, ids.ID_VAPI_GAUGE, config, bundle_timestamp, symbol), md=12, lg=4, className="mb-4"),
                            dbc.Col(_create_flow_gauge("DWFD", und_data.dwfd_z_score_und, ids.ID_DWFD_GAUGE, config, bundle_timestamp, symbol), md=12, lg=4, className="mb-4"),
                            dbc.Col(_create_flow_gauge("TW-LAF", und_data.tw_laf_z_score_und, ids.ID_TW_LAF_GAUGE, config, bundle_timestamp, symbol), md=12, lg=4, className="mb-4"),
                        ], className="mt-3"),
                        # Row 3: GIB Gauges (GIB OI-Based, TD-GIB, HP-EOD)
                        dbc.Row([
                            dbc.Col(_create_gib_gauge("GIB OI-Based", und_data.gib_oi_based_und, f"{ids.ID_GIB_GAUGE}-oi", config, bundle_timestamp, symbol), md=12, lg=4, className="mb-4"),
                            dbc.Col(_create_gib_gauge("TD-GIB", und_data.td_gib_und, f"{ids.ID_GIB_GAUGE}-td", config, bundle_timestamp, symbol, is_dollar_value=False), md=12, lg=4, className="mb-4"),
                            dbc.Col(_create_gib_gauge("HP-EOD", und_data.hp_eod_und, f"{ids.ID_HP_EOD_GAUGE}", config, bundle_timestamp, symbol, is_dollar_value=True), md=12, lg=4, className="mb-4"),
                        ], className="mt-3"),
                        # Row 4: SGDHP Mini-Heatmap (Full Width)
                        dbc.Row([
                            dbc.Col(_create_mini_heatmap("SGDHP", strike_data, "sgdhp_score_strike", "sgdhp-mini-heatmap", config, bundle_timestamp, symbol, current_price), md=12, lg=12, className="mb-4", style={"height": "100%"}),
                        ], className="mt-3"),
                        # Row 5: UGCH Mini-Heatmap (Full Width)
                        dbc.Row([
                            dbc.Col(_create_mini_heatmap("UGCH", strike_data, "ugch_score_strike", "ugch-mini-heatmap", config, bundle_timestamp, symbol, current_price), md=12, lg=12, className="mb-4", style={"height": "100%"}),
                        ], className="mt-3"),
                        # Row 6: Recommendations Table and Ticker Context
                        dbc.Row([
                            dbc.Col(_create_recommendations_table(bundle.active_recommendations_v2_5, config, bundle_timestamp, symbol), md=12, lg=8, className="mb-4"),
                            dbc.Col(_create_ticker_context_summary(und_data.ticker_context_dict_v2_5, config, bundle_timestamp, symbol), md=12, lg=4, className="mb-4"),
                        ], className="mt-3"),
                        # Row 7: ATIF Recommendations (Temporarily disabled)
                        # dbc.Row([
                        #     dbc.Col(_create_atif_recommendations_table(atif_recommendations, config, bundle_timestamp, symbol, db_manager), md=12, className="mb-4"),
                        # ], className="mt-3"),
                    ]
                )
            ]
        )

    except Exception as e:
        import traceback
        error_msg = f"Main Dashboard Error: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return html.Div([
            dbc.Card(dbc.CardBody([
                dbc.Alert([
                    html.H4("Main Dashboard Error", className="alert-heading"),
                    html.P(f"Error: {str(e)}"),
                    html.Hr(),
                    html.P("Please check the logs for more details.", className="mb-0")
                ], color="danger")
            ]))
        ])