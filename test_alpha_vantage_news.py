#!/usr/bin/env python3
"""
Quick Test Drive of Alpha Vantage News & Sentiment API
=====================================================

Let's see what kind of intelligence we can get from Alpha Vantage!
"""

import requests
import json
from datetime import datetime
import pprint

# Your API key
API_KEY = "9CZXMNC1HO3EI2QR"
BASE_URL = "https://www.alphavantage.co/query"

def test_news_sentiment_api():
    """Test the News & Sentiment API with different configurations."""
    
    print("🧠 ALPHA VANTAGE NEWS & SENTIMENT API TEST DRIVE")
    print("=" * 60)
    
    # Test 1: Basic news sentiment (no tickers)
    print("\n🎯 TEST 1: Basic News Sentiment (No Tickers)")
    print("-" * 40)

    params1 = {
        'function': 'NEWS_SENTIMENT',
        'limit': 5,
        'apikey': API_KEY
    }
    
    try:
        response = requests.get(BASE_URL, params=params1)
        print(f"🔍 Response Status: {response.status_code}")
        print(f"🔍 Response URL: {response.url}")

        data = response.json()

        # Print raw response for debugging
        print(f"🔍 Raw Response Keys: {list(data.keys())}")

        if 'Error Message' in data:
            print(f"❌ Error: {data['Error Message']}")
        elif 'Note' in data:
            print(f"⚠️ Note: {data['Note']}")
        elif 'Information' in data:
            print(f"ℹ️ Information: {data['Information']}")
        else:
            feed = data.get('feed', [])
            print(f"✅ Success! Got {len(feed)} articles")

            # Print first few characters of response for debugging
            if not feed:
                print(f"🔍 Full Response Sample: {str(data)[:500]}...")
            
            # Show first article details
            if data.get('feed'):
                first_article = data['feed'][0]
                print(f"\n📰 Sample Article:")
                print(f"Title: {first_article.get('title', 'N/A')[:100]}...")
                print(f"Time: {first_article.get('time_published', 'N/A')}")
                print(f"Overall Sentiment: {first_article.get('overall_sentiment_label', 'N/A')} ({first_article.get('overall_sentiment_score', 'N/A')})")
                
                # Check ticker-specific sentiment
                ticker_sentiments = first_article.get('ticker_sentiment', [])
                for ts in ticker_sentiments:
                    if ts.get('ticker', '').upper() == 'SPY':
                        print(f"SPY Specific Sentiment: {ts.get('ticker_sentiment_label', 'N/A')} ({ts.get('ticker_sentiment_score', 'N/A')})")
                        print(f"Relevance Score: {ts.get('relevance_score', 'N/A')}")
                        break
                
                # Show topics
                topics = first_article.get('topics', [])
                if topics:
                    topic_names = [t.get('topic', 'Unknown') for t in topics]
                    print(f"Topics: {', '.join(topic_names)}")
    
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    # Test 2: Try with AAPL ticker (from documentation example)
    print("\n\n🎯 TEST 2: AAPL Ticker Test (From Docs)")
    print("-" * 40)

    params2 = {
        'function': 'NEWS_SENTIMENT',
        'tickers': 'AAPL',
        'limit': 5,
        'apikey': API_KEY
    }
    
    try:
        response = requests.get(BASE_URL, params=params2)
        data = response.json()
        
        if 'Error Message' in data:
            print(f"❌ Error: {data['Error Message']}")
        elif 'Note' in data:
            print(f"⚠️ Note: {data['Note']}")
        else:
            articles = data.get('feed', [])
            print(f"✅ Success! Got {len(articles)} filtered articles")
            
            # Calculate aggregate sentiment
            if articles:
                sentiment_scores = []
                relevance_scores = []
                
                for article in articles:
                    ticker_sentiments = article.get('ticker_sentiment', [])
                    for ts in ticker_sentiments:
                        if ts.get('ticker', '').upper() == 'SPY':
                            try:
                                sentiment_scores.append(float(ts.get('ticker_sentiment_score', 0)))
                                relevance_scores.append(float(ts.get('relevance_score', 0)))
                            except:
                                pass
                            break
                
                if sentiment_scores:
                    avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                    avg_relevance = sum(relevance_scores) / len(relevance_scores)
                    
                    print(f"\n📊 AGGREGATE ANALYSIS:")
                    print(f"Average Sentiment Score: {avg_sentiment:.4f}")
                    print(f"Average Relevance Score: {avg_relevance:.4f}")
                    
                    if avg_sentiment >= 0.15:
                        sentiment_label = "BULLISH"
                    elif avg_sentiment <= -0.15:
                        sentiment_label = "BEARISH"
                    else:
                        sentiment_label = "NEUTRAL"
                    
                    print(f"Overall Assessment: {sentiment_label}")
                    print(f"Confidence Level: {min(avg_relevance * 100, 95):.1f}%")
    
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    # Test 3: Multiple tickers
    print("\n\n🎯 TEST 3: Multiple Tickers (SPY, QQQ, IWM)")
    print("-" * 40)
    
    params3 = {
        'function': 'NEWS_SENTIMENT',
        'tickers': 'SPY,QQQ,IWM',
        'limit': 5,
        'apikey': API_KEY
    }
    
    try:
        response = requests.get(BASE_URL, params=params3)
        data = response.json()
        
        if 'Error Message' in data:
            print(f"❌ Error: {data['Error Message']}")
        elif 'Note' in data:
            print(f"⚠️ Note: {data['Note']}")
        else:
            articles = data.get('feed', [])
            print(f"✅ Success! Got {len(articles)} multi-ticker articles")
            
            # Analyze sentiment by ticker
            ticker_analysis = {'SPY': [], 'QQQ': [], 'IWM': []}
            
            for article in articles:
                ticker_sentiments = article.get('ticker_sentiment', [])
                for ts in ticker_sentiments:
                    ticker = ts.get('ticker', '').upper()
                    if ticker in ticker_analysis:
                        try:
                            score = float(ts.get('ticker_sentiment_score', 0))
                            ticker_analysis[ticker].append(score)
                        except:
                            pass
            
            print(f"\n📈 MULTI-TICKER SENTIMENT ANALYSIS:")
            for ticker, scores in ticker_analysis.items():
                if scores:
                    avg_score = sum(scores) / len(scores)
                    print(f"{ticker}: {avg_score:.4f} (from {len(scores)} mentions)")
                else:
                    print(f"{ticker}: No sentiment data")
    
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    print("\n\n🎭 TEST DRIVE COMPLETE!")
    print("=" * 60)

if __name__ == "__main__":
    test_news_sentiment_api()
