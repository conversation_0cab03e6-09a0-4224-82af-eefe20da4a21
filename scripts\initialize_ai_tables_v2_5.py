"""
AI Tables Initialization Script v2.5
====================================

This script creates all missing AI-related database tables for the EOTS v2.5 system.
It ensures that ai_predictions, ai_adaptations, and other AI tables are properly created.

Usage: python scripts/initialize_ai_tables_v2_5.py
"""

import os
import sys
import logging
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.config_manager_v2_5 import ConfigManagerV2_5

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_ai_tables():
    """Create all AI-related database tables."""
    try:
        # Try direct database connection using environment variables
        import psycopg2
        import os

        # Get database connection parameters from environment
        db_user = os.getenv('EOTS_DB_USER')
        db_password = os.getenv('EOTS_DB_PASSWORD')
        db_name = os.getenv('EOTS_DB_NAME')
        db_host = os.getenv('EOTS_DB_HOST', 'localhost')
        db_port = os.getenv('EOTS_DB_PORT', '5432')

        if not all([db_user, db_password, db_name]):
            logger.error("Required database environment variables not found")
            logger.info("Please set EOTS_DB_USER, EOTS_DB_PASSWORD, and EOTS_DB_NAME")
            return False

        # Connect directly to database
        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            database=db_name,
            user=db_user,
            password=db_password
        )
        cursor = conn.cursor()

        logger.info(f"✅ Connected to database: {db_name}@{db_host}:{db_port}")
        
        logger.info("🔧 Creating AI-related database tables...")
        
        # 1. Create ai_predictions table
        ai_predictions_sql = """
        CREATE TABLE IF NOT EXISTS ai_predictions (
            id SERIAL PRIMARY KEY,
            symbol TEXT NOT NULL,
            prediction_type TEXT NOT NULL,
            prediction_value NUMERIC(10, 4),
            prediction_direction TEXT CHECK (prediction_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            confidence_score NUMERIC(5, 4) DEFAULT 0.0000,
            time_horizon TEXT NOT NULL,
            prediction_timestamp TIMESTAMPTZ DEFAULT NOW(),
            target_timestamp TIMESTAMPTZ,
            actual_value NUMERIC(10, 4),
            actual_direction TEXT CHECK (actual_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            prediction_accurate BOOLEAN,
            accuracy_score NUMERIC(5, 4),
            model_version TEXT DEFAULT 'v2.5',
            market_context JSONB DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            
            CONSTRAINT chk_confidence_score CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
            CONSTRAINT chk_accuracy_score CHECK (accuracy_score IS NULL OR (accuracy_score >= 0.0 AND accuracy_score <= 1.0))
        );
        """
        
        # 2. Create ai_adaptations table
        ai_adaptations_sql = """
        CREATE TABLE IF NOT EXISTS ai_adaptations (
            id SERIAL PRIMARY KEY,
            adaptation_type TEXT NOT NULL,
            adaptation_name TEXT NOT NULL,
            adaptation_description TEXT,
            confidence_score NUMERIC(5, 4) DEFAULT 0.0000,
            success_rate NUMERIC(5, 4) DEFAULT 0.0000,
            adaptation_score NUMERIC(5, 4) DEFAULT 0.0000,
            implementation_status TEXT DEFAULT 'PENDING',
            market_context JSONB DEFAULT '{}',
            performance_metrics JSONB DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),

            CONSTRAINT chk_adaptation_confidence CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
            CONSTRAINT chk_adaptation_success_rate CHECK (success_rate >= 0.0 AND success_rate <= 1.0),
            CONSTRAINT chk_adaptation_score CHECK (adaptation_score >= 0.0 AND adaptation_score <= 1.0)
        );
        """
        
        # 3. Create indexes for performance
        indexes_sql = [
            "CREATE INDEX IF NOT EXISTS idx_ai_predictions_symbol ON ai_predictions(symbol);",
            "CREATE INDEX IF NOT EXISTS idx_ai_predictions_created_at ON ai_predictions(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_ai_predictions_target_timestamp ON ai_predictions(target_timestamp);",
            "CREATE INDEX IF NOT EXISTS idx_ai_predictions_prediction_type ON ai_predictions(prediction_type);",
            "CREATE INDEX IF NOT EXISTS idx_ai_adaptations_type ON ai_adaptations(adaptation_type);",
            "CREATE INDEX IF NOT EXISTS idx_ai_adaptations_created_at ON ai_adaptations(created_at);"
        ]
        
        # Execute table creation
        logger.info("📊 Creating ai_predictions table...")
        cursor.execute(ai_predictions_sql)
        
        logger.info("🧠 Creating ai_adaptations table...")
        cursor.execute(ai_adaptations_sql)
        
        # Create indexes
        logger.info("🔍 Creating database indexes...")
        for index_sql in indexes_sql:
            cursor.execute(index_sql)
        
        # Commit changes
        conn.commit()
        
        # Verify tables were created
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('ai_predictions', 'ai_adaptations', 'ai_learning_patterns', 'ai_insights_history')
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        logger.info(f"✅ AI tables verified: {[table[0] for table in tables]}")
        
        # Insert sample data for testing
        logger.info("🌱 Inserting sample AI data for testing...")
        
        # Sample prediction
        sample_prediction_sql = """
        INSERT INTO ai_predictions (
            symbol, prediction_type, prediction_value, prediction_direction,
            confidence_score, time_horizon, target_timestamp, market_context
        ) VALUES (
            'SPY', 'eots_direction', 2.5, 'UP', 0.75, '4H', 
            NOW() + INTERVAL '4 hours', 
            '{"vapi_fa_z": 1.2, "dwfd_z": 0.8, "signal_strength": 2.5}'::jsonb
        ) ON CONFLICT DO NOTHING;
        """
        
        # Sample adaptation
        sample_adaptation_sql = """
        INSERT INTO ai_adaptations (
            adaptation_type, adaptation_name, adaptation_description,
            confidence_score, success_rate, adaptation_score, implementation_status
        ) VALUES (
            'signal_enhancement', 'VAPI-FA Z-Score Calibration',
            'Enhanced VAPI-FA signal strength calculation for improved prediction accuracy',
            0.82, 0.78, 0.85, 'ACTIVE'
        ) ON CONFLICT DO NOTHING;
        """
        
        cursor.execute(sample_prediction_sql)
        cursor.execute(sample_adaptation_sql)
        
        conn.commit()
        
        logger.info("🎉 All AI tables created successfully!")
        logger.info("🔮 AI Predictions system is now ready for production!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating AI tables: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def verify_ai_system():
    """Verify that the AI system is working properly."""
    try:
        logger.info("🔍 Verifying AI system functionality...")

        # Simple database verification
        import psycopg2
        import os

        # Get database connection parameters
        db_user = os.getenv('EOTS_DB_USER')
        db_password = os.getenv('EOTS_DB_PASSWORD')
        db_name = os.getenv('EOTS_DB_NAME')
        db_host = os.getenv('EOTS_DB_HOST', 'localhost')
        db_port = os.getenv('EOTS_DB_PORT', '5432')

        if not all([db_user, db_password, db_name]):
            logger.warning("⚠️ Database environment variables not set, skipping verification")
            return True

        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            database=db_name,
            user=db_user,
            password=db_password
        )
        cursor = conn.cursor()

        # Check if tables exist
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name IN ('ai_predictions', 'ai_adaptations')
            ORDER BY table_name;
        """)

        tables = cursor.fetchall()
        logger.info(f"✅ AI tables verified: {[table[0] for table in tables]}")

        # Check sample data
        cursor.execute("SELECT COUNT(*) FROM ai_predictions;")
        prediction_count = cursor.fetchone()[0]
        logger.info(f"📊 AI predictions in database: {prediction_count}")

        cursor.close()
        conn.close()

        logger.info("🎯 AI system verification complete!")
        return True

    except Exception as e:
        logger.error(f"❌ AI system verification failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting AI Tables Initialization for EOTS v2.5...")
    
    # Create tables
    if create_ai_tables():
        # Verify system
        if verify_ai_system():
            logger.info("✨ AI Tables initialization completed successfully!")
            logger.info("🎉 EOTS v2.5 AI Predictions system is ready!")
        else:
            logger.error("⚠️ AI system verification failed")
            sys.exit(1)
    else:
        logger.error("❌ Failed to create AI tables")
        sys.exit(1)
