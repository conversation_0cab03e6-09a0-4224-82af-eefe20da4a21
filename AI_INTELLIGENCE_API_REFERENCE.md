# 🔌 AI Intelligence System API Reference

## 📋 **COMPREHENSIVE API DOCUMENTATION**

This document provides complete API reference for the Elite AI Intelligence System v2.5, including all functions, parameters, return values, and usage examples.

---

## 🧠 **UNIFIED AI ECOSYSTEM API**

### **Core Functions**

#### `get_unified_ai_ecosystem() -> UnifiedAIEcosystem`
**Purpose:** Get or create the global unified AI ecosystem instance.
```python
ecosystem = await get_unified_ai_ecosystem()
```

#### `generate_ecosystem_analysis(data_bundle: Any) -> Dict[str, Any]`
**Purpose:** Generate comprehensive AI ecosystem analysis.
```python
analysis = await generate_ecosystem_analysis(data_bundle)
# Returns: {
#   "ecosystem_intelligence": {...},
#   "confidence_score": 0.85,
#   "regime_analysis": {...},
#   "recommendations": [...]
# }
```

#### `breed_specialized_ai_agent(agent_id: str, parent_agents: List[str]) -> Dict[str, Any]`
**Purpose:** Create new AI agent through breeding process.
```python
result = await breed_specialized_ai_agent("new_agent", ["parent1", "parent2"])
# Returns: {
#   "status": "success",
#   "agent_id": "new_agent",
#   "breeding_potential": 0.85,
#   "capabilities": [...]
# }
```

#### `force_ai_ecosystem_evolution() -> Dict[str, Any]`
**Purpose:** Force evolution of the entire AI ecosystem.
```python
evolution = await force_ai_ecosystem_evolution()
# Returns: {
#   "status": "completed",
#   "agent_evolution": {...},
#   "system_improvements": {...}
# }
```

---

## 🤝 **ENSEMBLE INTELLIGENCE API**

### **Core Functions**

#### `generate_ensemble_prediction(data_bundle: Any, method: EnsembleMethod = None) -> EnsemblePrediction`
**Purpose:** Generate ensemble prediction combining multiple AI agents.
```python
from dashboard_application.modes.ai_dashboard.ensemble_intelligence_system import EnsembleMethod

prediction = await generate_ensemble_prediction(data_bundle, EnsembleMethod.ADAPTIVE_WEIGHTED)
# Returns: EnsemblePrediction object with:
# - ensemble_prediction: Dict[str, Any]
# - ensemble_confidence: float
# - consensus_score: float
# - diversity_score: float
# - individual_predictions: List[AgentPrediction]
```

#### `perform_ensemble_cross_validation(data: List[Dict], cv_type: CrossValidationType, k_folds: int = 5) -> CrossValidationResult`
**Purpose:** Perform cross-validation on ensemble predictions.
```python
from dashboard_application.modes.ai_dashboard.ensemble_intelligence_system import CrossValidationType

cv_result = await perform_ensemble_cross_validation(
    validation_data, 
    CrossValidationType.K_FOLD, 
    k_folds=5
)
# Returns: CrossValidationResult with accuracy, calibration, and improvement metrics
```

#### `get_ensemble_status() -> Dict[str, Any]`
**Purpose:** Get current status of ensemble intelligence system.
```python
status = await get_ensemble_status()
# Returns: {
#   "registered_agents": 10,
#   "active_agents": 10,
#   "ensemble_history_size": 50,
#   "agent_weights": {...},
#   "recent_performance": {...}
# }
```

#### `optimize_ensemble_weights() -> Dict[str, Any]`
**Purpose:** Force optimization of ensemble weights.
```python
optimization = await optimize_ensemble_weights()
# Returns: {
#   "status": "completed",
#   "weight_changes": {...},
#   "total_agents": 10
# }
```

---

## 📊 **PERFORMANCE TRACKING API**

### **Core Functions**

#### `track_ai_prediction(prediction_id: str, prediction_data: Dict, agent_id: str) -> bool`
**Purpose:** Track AI prediction for performance monitoring.
```python
success = await track_ai_prediction(
    "pred_001",
    {
        "confidence": 0.85,
        "regime": "BULLISH_MOMENTUM",
        "insights": ["Strong upward momentum detected"]
    },
    "ensemble_agent"
)
```

#### `validate_ai_prediction(prediction_id: str, actual_outcome: Dict) -> ValidationResult`
**Purpose:** Validate AI prediction against actual market outcome.
```python
result = await validate_ai_prediction(
    "pred_001",
    {
        "regime": "BULLISH_MOMENTUM",
        "success_rate": 0.9,
        "market_events": ["Confirmed upward movement"]
    }
)
# Returns: ValidationResult with accuracy_score, confidence_error, timing_accuracy
```

#### `get_performance_report(days_back: int = 30) -> PerformanceReport`
**Purpose:** Get comprehensive performance report.
```python
report = await get_performance_report(30)
# Returns: PerformanceReport with:
# - overall_performance_score: float
# - individual_metrics: Dict[str, float]
# - performance_trends: Dict[str, List[float]]
# - improvement_recommendations: List[str]
```

#### `get_real_time_metrics() -> Dict[str, Any]`
**Purpose:** Get real-time performance metrics.
```python
metrics = await get_real_time_metrics()
# Returns: {
#   "status": "active",
#   "active_predictions": 5,
#   "recent_validations": 3,
#   "metrics": {...}
# }
```

---

## ⚖️ **ADAPTIVE THRESHOLD MANAGEMENT API**

### **Core Functions**

#### `get_adaptive_threshold(threshold_id: str) -> Optional[float]`
**Purpose:** Get current value of an adaptive threshold.
```python
threshold = await get_adaptive_threshold("vapi_fa_strong")
# Returns: 2.0 (current optimized threshold value)
```

#### `update_threshold_performance(threshold_id: str, performance_score: float, threshold_value_used: float = None) -> bool`
**Purpose:** Update performance data for a threshold.
```python
success = await update_threshold_performance("confidence_threshold", 0.85, 0.65)
```

#### `optimize_thresholds(threshold_id: str = None) -> List[ThresholdOptimizationResult]`
**Purpose:** Force optimization of specific or all thresholds.
```python
results = await optimize_thresholds("accuracy_alert_threshold")
# Returns: List of ThresholdOptimizationResult objects with:
# - threshold_id: str
# - old_value: float
# - new_value: float
# - improvement_score: float
# - optimization_method: str
```

#### `get_threshold_status(threshold_id: str = None) -> Dict[str, Any]`
**Purpose:** Get status information for thresholds.
```python
status = await get_threshold_status()
# Returns: {
#   "total_thresholds": 23,
#   "threshold_groups": 9,
#   "average_effectiveness": 0.75,
#   "market_conditions": {...}
# }
```

---

## 🧠 **SELF-LEARNING ENGINE API**

### **Core Functions**

#### `record_ai_prediction(prediction_data: Dict, context_data: Dict) -> str`
**Purpose:** Record AI prediction for learning.
```python
pred_id = await record_ai_prediction(
    {
        "confidence": 0.82,
        "insights": ["Pattern recognition indicates bullish momentum"],
        "regime": "BULLISH_MOMENTUM"
    },
    {
        "market_regime": "BULLISH_MOMENTUM",
        "volatility_level": "NORMAL",
        "signal_strength": 3.2
    }
)
# Returns: prediction_id string
```

#### `validate_ai_prediction(prediction_id: str, outcome: Dict) -> bool`
**Purpose:** Validate prediction and trigger learning.
```python
success = await validate_ai_prediction(
    pred_id,
    {
        "regime": "BULLISH_MOMENTUM",
        "success_rate": 0.9,
        "accuracy_confirmed": True
    }
)
```

#### `get_ai_learning_summary() -> Dict[str, Any]`
**Purpose:** Get comprehensive learning summary.
```python
summary = await get_ai_learning_summary()
# Returns: {
#   "total_predictions": 150,
#   "average_accuracy": 0.85,
#   "learning_velocity": 0.75,
#   "adaptation_score": 8.2,
#   "pattern_recognition_improvement": 0.15
# }
```

---

## 🗄️ **DATABASE INTEGRATION API**

### **Core Functions**

#### `get_ai_database_integration() -> AIIntelligenceDatabaseIntegration`
**Purpose:** Get database integration instance.
```python
db_integration = await get_ai_database_integration()
```

#### Database Operations
```python
# Store AI prediction
await db_integration.store_ai_prediction(prediction_data)

# Store validation result
await db_integration.store_validation_result(validation_data)

# Store performance metrics
await db_integration.store_performance_metrics(metrics_data)

# Get historical data
historical_data = await db_integration.get_historical_predictions(days_back=30)
```

---

## 🎯 **COMMON USAGE PATTERNS**

### **Complete AI Intelligence Workflow**
```python
# 1. Generate ensemble prediction
prediction = await generate_ensemble_prediction(data_bundle)

# 2. Track prediction for monitoring
await track_ai_prediction(
    prediction.ensemble_id,
    prediction.ensemble_prediction,
    "ensemble_system"
)

# 3. Record for learning
pred_id = await record_ai_prediction(
    prediction.ensemble_prediction,
    {"market_context": "current_analysis"}
)

# 4. Later: validate against actual outcome
await validate_ai_prediction(pred_id, actual_market_outcome)
await validate_ai_prediction(prediction.ensemble_id, actual_market_outcome)

# 5. Get performance insights
report = await get_performance_report(30)
```

### **System Monitoring and Optimization**
```python
# Check system status
ecosystem_status = await get_unified_ai_ecosystem().get_ecosystem_status()
ensemble_status = await get_ensemble_status()
performance_metrics = await get_real_time_metrics()

# Optimize system performance
await optimize_ensemble_weights()
await optimize_thresholds()
await force_ai_ecosystem_evolution()
```

### **Cross-Validation and Testing**
```python
# Prepare validation data
validation_data = [
    {"actual_regime": "BULLISH", "prediction_confidence": 0.8},
    {"actual_regime": "BEARISH", "prediction_confidence": 0.7},
    # ... more data
]

# Perform cross-validation
cv_result = await perform_ensemble_cross_validation(
    validation_data,
    CrossValidationType.K_FOLD,
    k_folds=5
)

# Analyze results
print(f"Mean Accuracy: {cv_result.mean_accuracy:.3f}")
print(f"Confidence Calibration: {cv_result.confidence_calibration:.3f}")
```

---

## 🔧 **ERROR HANDLING**

### **Common Error Patterns**
```python
try:
    prediction = await generate_ensemble_prediction(data_bundle)
except Exception as e:
    logger.error(f"Ensemble prediction failed: {e}")
    # Fallback to individual agent prediction
    
try:
    threshold = await get_adaptive_threshold("invalid_threshold")
    if threshold is None:
        # Handle missing threshold
        threshold = default_value
except Exception as e:
    logger.error(f"Threshold access failed: {e}")
```

### **Graceful Degradation**
The AI intelligence system is designed with graceful degradation:
- **Ensemble failures** fall back to best individual agent
- **Database failures** fall back to in-memory storage
- **Optimization failures** maintain current settings
- **Validation failures** continue with reduced confidence

---

## 📈 **PERFORMANCE CONSIDERATIONS**

### **Optimization Tips**
- **Batch operations** when possible for better performance
- **Cache frequently accessed** thresholds and configurations
- **Use async/await** properly for non-blocking operations
- **Monitor memory usage** for large ensemble operations
- **Implement timeouts** for external API calls

### **Scaling Recommendations**
- **Horizontal scaling** for ensemble agents
- **Database connection pooling** for high-throughput scenarios
- **Caching layers** for frequently accessed data
- **Load balancing** for multiple AI instances
- **Resource monitoring** for optimal performance

---

## 🎯 **CONCLUSION**

This API reference provides comprehensive coverage of the Elite AI Intelligence System v2.5. The system is designed for:
- **Ease of use** with intuitive function names and parameters
- **Robust error handling** with graceful degradation
- **High performance** with async operations and optimization
- **Scalability** for production deployment
- **Comprehensive monitoring** with detailed metrics and logging

**🚀 Ready for elite-level AI intelligence integration!**
