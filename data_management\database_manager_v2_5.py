# data_management/database_manager_v2_5.py
# EOTS v2.5 - SENTRY-APPROVED

import logging
from typing import Dict, Any, Optional, List
from datetime import date, datetime
import pandas as pd
import os
import sqlite3
import typing

# PostgreSQL imports (optional)
try:
    import psycopg
    from psycopg.rows import dict_row
    from psycopg import sql
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False

# Note: psycopg and related imports are removed to ensure no live connection can be made.

logger = logging.getLogger(__name__)

try:
    from typing import LiteralString
except ImportError:
    LiteralString = str

class DatabaseManagerV2_5:
    """
    Manages database interactions for the EOTS v2.5 system.
    Auto-detects and supports both SQLite and PostgreSQL backends.
    """

    def __init__(self, db_config: Dict[str, Any]):
        self.logger = logger.getChild(self.__class__.__name__)
        self._db_config = db_config
        self._conn = None
        self.db_type = None
        self.connection_status = "DISCONNECTED"
        self._connect()

    def _connect(self):
        """Connect to database - auto-detect SQLite vs PostgreSQL."""
        # First, try SQLite (local database)
        sqlite_path = self._db_config.get('sqlite_path', 'data/elite_options.db')
        if os.path.exists(sqlite_path) or not POSTGRES_AVAILABLE:
            self._connect_sqlite(sqlite_path)
        else:
            # Try PostgreSQL connection
            self._connect_postgres()

    def _connect_sqlite(self, db_path: str):
        """Connect to SQLite database."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(db_path), exist_ok=True)

            self._conn = sqlite3.connect(db_path, check_same_thread=False)
            self._conn.row_factory = sqlite3.Row  # Enable dict-like access
            self._conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign keys
            self.db_type = "sqlite"
            self.connection_status = "CONNECTED"
            self.logger.info(f"DatabaseManagerV2_5 connected to SQLite: {db_path}")
        except Exception as e:
            self.connection_status = "FAILED"
            self.logger.error(f"Failed to connect to SQLite database: {e}")
            raise

    def _connect_postgres(self):
        """Connect to PostgreSQL database."""
        if not POSTGRES_AVAILABLE:
            raise ImportError("PostgreSQL dependencies not available")

        # Handle both dict and ConfigManager objects
        if hasattr(self._db_config, 'get'):
            # It's a dictionary
            db_url = self._db_config.get('db_url') or os.getenv('EOTS_DB_URL')
            db_user = self._db_config.get('db_user') or os.getenv('EOTS_DB_USER')
            db_pass = self._db_config.get('db_password') or os.getenv('EOTS_DB_PASSWORD')
            db_name = self._db_config.get('db_name') or os.getenv('EOTS_DB_NAME')
            db_host = self._db_config.get('db_host') or os.getenv('EOTS_DB_HOST', 'localhost')
            db_port = self._db_config.get('db_port') or os.getenv('EOTS_DB_PORT', 5432)
        else:
            # It's a ConfigManager object, get database settings
            try:
                db_settings = getattr(self._db_config.config, 'database_settings', None)
                if db_settings:
                    db_url = getattr(db_settings, 'db_url', None) or os.getenv('EOTS_DB_URL')
                    db_user = getattr(db_settings, 'user', None) or os.getenv('EOTS_DB_USER')
                    db_pass = getattr(db_settings, 'password', None) or os.getenv('EOTS_DB_PASSWORD')
                    db_name = getattr(db_settings, 'database', None) or os.getenv('EOTS_DB_NAME')
                    db_host = getattr(db_settings, 'host', None) or os.getenv('EOTS_DB_HOST', 'localhost')
                    db_port = getattr(db_settings, 'port', None) or os.getenv('EOTS_DB_PORT', 5432)
                else:
                    # Fall back to environment variables only
                    db_url = os.getenv('EOTS_DB_URL')
                    db_user = os.getenv('EOTS_DB_USER')
                    db_pass = os.getenv('EOTS_DB_PASSWORD')
                    db_name = os.getenv('EOTS_DB_NAME')
                    db_host = os.getenv('EOTS_DB_HOST', 'localhost')
                    db_port = os.getenv('EOTS_DB_PORT', 5432)
            except Exception:
                # Fall back to environment variables only
                db_url = os.getenv('EOTS_DB_URL')
                db_user = os.getenv('EOTS_DB_USER')
                db_pass = os.getenv('EOTS_DB_PASSWORD')
                db_name = os.getenv('EOTS_DB_NAME')
                db_host = os.getenv('EOTS_DB_HOST', 'localhost')
                db_port = os.getenv('EOTS_DB_PORT', 5432)
        try:
            self._conn = psycopg.connect(
                dbname=db_name,
                user=db_user,
                password=db_pass,
                host=db_host,
                port=db_port,
                autocommit=True,
                row_factory=dict_row
            )
            self.db_type = "postgresql"
            self.connection_status = "CONNECTED"
            self.logger.info("DatabaseManagerV2_5 connected to Postgres/Supabase backend.")
        except Exception as e:
            self.connection_status = "FAILED"
            self.logger.critical(f"Failed to connect to PostgreSQL database: {e}")
            raise

    def get_connection(self) -> Any:
        return self._conn

    def close_connection(self):
        if self._conn:
            self._conn.close()
            self.logger.info("Database connection closed.")
            self.connection_status = "CLOSED"

    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database."""
        try:
            if self.db_type == "sqlite":
                cursor = self._conn.cursor()
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table_name,))
                return cursor.fetchone() is not None
            else:  # PostgreSQL
                cursor = self._conn.cursor()
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = %s
                    )
                """, (table_name,))
                result = cursor.fetchone()
                return result[0] if result else False
        except Exception as e:
            self.logger.error(f"Failed to check table existence: {e}")
            return False

    def column_exists(self, table_name: str, column_name: str) -> bool:
        """Check if a column exists in a table."""
        try:
            if self.db_type == "sqlite":
                cursor = self._conn.cursor()
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in cursor.fetchall()]
                return column_name in columns
            else:  # PostgreSQL
                cursor = self._conn.cursor()
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = %s AND column_name = %s
                    )
                """, (table_name, column_name))
                result = cursor.fetchone()
                return result[0] if result else False
        except Exception as e:
            self.logger.error(f"Failed to check column existence: {e}")
            return False

    def get_datetime_sql(self, days_ago: int = 0) -> str:
        """Get database-specific datetime SQL."""
        if self.db_type == "sqlite":
            if days_ago == 0:
                return "datetime('now')"
            else:
                return f"datetime('now', '-{days_ago} days')"
        else:  # PostgreSQL
            if days_ago == 0:
                return "NOW()"
            else:
                return f"NOW() - INTERVAL '{days_ago} days'"

    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a raw SQL query and return results."""
        try:
            if self.db_type == "sqlite":
                cursor = self._conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                # Check if this is a SELECT query
                if query.strip().upper().startswith('SELECT'):
                    rows = cursor.fetchall()
                    return [dict(row) for row in rows]
                else:
                    self._conn.commit()
                    return []
            else:  # PostgreSQL
                with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                    if params:
                        cur.execute(query, params)
                    else:
                        cur.execute(query)

                    # Check if this is a SELECT query
                    if query.strip().upper().startswith('SELECT'):
                        return cur.fetchall()
                    else:
                        return []
        except Exception as e:
            self.logger.error(f"Failed to execute query: {e}")
            raise

    def initialize_database_schema(self) -> None:
        """Initialize database schema with appropriate syntax for the database type."""
        if self.db_type == "sqlite":
            self._initialize_sqlite_schema()
        else:
            self._initialize_postgres_schema()

    def _initialize_sqlite_schema(self) -> None:
        """Initialize SQLite-specific schema."""
        sql_create_daily_ohlcv = """
        CREATE TABLE IF NOT EXISTS daily_ohlcv (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            date DATE NOT NULL,
            open REAL NOT NULL,
            high REAL NOT NULL,
            low REAL NOT NULL,
            close REAL NOT NULL,
            volume INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, date)
        );"""
        sql_create_daily_eots_metrics = """
        CREATE TABLE IF NOT EXISTS daily_eots_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            date DATE NOT NULL,
            gib_oi_based_und REAL,
            ivsdh_und_avg REAL,
            vapi_fa_z_score_und REAL,
            dwfd_z_score_und REAL,
            tw_laf_z_score_und REAL,
            market_regime_summary TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, date)
        );"""
        # AI Predictions Table (SQLite)
        sql_create_ai_predictions = """
        CREATE TABLE IF NOT EXISTS ai_predictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            prediction_type TEXT NOT NULL,
            prediction_value REAL,
            prediction_direction TEXT CHECK (prediction_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            confidence_score REAL DEFAULT 0.0000,
            time_horizon TEXT NOT NULL,
            prediction_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            target_timestamp DATETIME,
            actual_value REAL,
            actual_direction TEXT CHECK (actual_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            prediction_accurate BOOLEAN,
            accuracy_score REAL,
            model_version TEXT DEFAULT 'v2.5',
            market_context TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        # AI Adaptations Table (SQLite)
        sql_create_ai_adaptations = """
        CREATE TABLE IF NOT EXISTS ai_adaptations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            adaptation_type TEXT NOT NULL,
            adaptation_description TEXT,
            success_rate REAL DEFAULT 0.0000,
            adaptation_score REAL DEFAULT 0.0000,
            market_context TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        # AI Insights History Table (SQLite)
        sql_create_ai_insights_history = """
        CREATE TABLE IF NOT EXISTS ai_insights_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            insight_type TEXT NOT NULL,
            insight_content TEXT NOT NULL,
            confidence_score REAL DEFAULT 0.0000,
            impact_score REAL DEFAULT 0.0000,
            market_context TEXT DEFAULT '{}',
            outcome_verified BOOLEAN DEFAULT FALSE,
            outcome_accuracy REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        # AI Learning Patterns Table (SQLite)
        sql_create_ai_learning_patterns = """
        CREATE TABLE IF NOT EXISTS ai_learning_patterns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            pattern_name TEXT NOT NULL,
            pattern_description TEXT,
            market_conditions TEXT DEFAULT '{}',
            success_rate REAL DEFAULT 0.0000,
            confidence_score REAL DEFAULT 0.0000,
            sample_size INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        );"""

        # Memory Entities Table (SQLite)
        sql_create_memory_entities = """
        CREATE TABLE IF NOT EXISTS memory_entities (
            entity_id TEXT PRIMARY KEY,
            entity_type TEXT NOT NULL,
            entity_name TEXT NOT NULL,
            description TEXT,
            symbol TEXT NOT NULL,
            confidence_score REAL DEFAULT 0.0000,
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
            access_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE
        );"""

        # Memory Relations Table (SQLite)
        sql_create_memory_relations = """
        CREATE TABLE IF NOT EXISTS memory_relations (
            relation_id TEXT PRIMARY KEY,
            source_entity_id TEXT NOT NULL,
            target_entity_id TEXT NOT NULL,
            relation_type TEXT NOT NULL,
            relation_strength REAL DEFAULT 0.5000,
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_validated DATETIME DEFAULT CURRENT_TIMESTAMP,
            validation_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE
        );"""

        commands = [
            sql_create_daily_ohlcv,
            sql_create_daily_eots_metrics,
            sql_create_ai_predictions,
            sql_create_ai_adaptations,
            sql_create_ai_insights_history,
            sql_create_ai_learning_patterns,
            sql_create_memory_entities,
            sql_create_memory_relations
        ]

        try:
            cursor = self._conn.cursor()
            for cmd in commands:
                cursor.execute(cmd)
            self._conn.commit()
            self.logger.info("SQLite database schema initialized successfully.")
        except Exception as e:
            self.logger.error(f"Failed to initialize SQLite schema: {e}")
            raise

    def _initialize_postgres_schema(self) -> None:
        """Initialize PostgreSQL-specific schema (original implementation)."""
        sql_create_daily_ohlcv = """
        CREATE TABLE IF NOT EXISTS daily_ohlcv (
            id SERIAL PRIMARY KEY, symbol TEXT NOT NULL, date DATE NOT NULL,
            open NUMERIC(12, 4) NOT NULL, high NUMERIC(12, 4) NOT NULL,
            low NUMERIC(12, 4) NOT NULL, close NUMERIC(12, 4) NOT NULL,
            volume BIGINT NOT NULL, created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(symbol, date)
        );"""

        # ATIF Performance Insights Table
        sql_create_atif_insights = """
        CREATE TABLE IF NOT EXISTS atif_insights (
            id SERIAL PRIMARY KEY,
            symbol TEXT NOT NULL,
            insight_type TEXT NOT NULL,
            insight_category TEXT NOT NULL,
            insight_title TEXT NOT NULL,
            insight_description TEXT NOT NULL,
            confidence_score NUMERIC(5, 3),
            impact_score NUMERIC(5, 3),
            time_horizon TEXT,
            supporting_metrics JSONB,
            related_recommendations TEXT[],
            timestamp TIMESTAMPTZ DEFAULT NOW(),
            expires_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );"""

        # ATIF Performance Tracking Table
        sql_create_atif_performance = """
        CREATE TABLE IF NOT EXISTS atif_performance (
            id SERIAL PRIMARY KEY,
            symbol TEXT NOT NULL,
            strategy_type TEXT NOT NULL,
            market_regime TEXT NOT NULL,
            conviction_score NUMERIC(5, 3),
            outcome TEXT NOT NULL,
            pnl_percentage NUMERIC(8, 4),
            hold_duration_hours INTEGER,
            entry_timestamp TIMESTAMPTZ NOT NULL,
            exit_timestamp TIMESTAMPTZ,
            exit_reason TEXT,
            performance_metrics JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );"""

        # AI Learning Patterns Table
        sql_create_ai_learning_patterns = """
        CREATE TABLE IF NOT EXISTS ai_learning_patterns (
            id SERIAL PRIMARY KEY,
            pattern_name TEXT NOT NULL,
            pattern_description TEXT,
            market_conditions JSONB DEFAULT '{}',
            success_rate NUMERIC(5, 4) DEFAULT 0.0000,
            confidence_score NUMERIC(5, 4) DEFAULT 0.0000,
            sample_size INTEGER DEFAULT 0,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            last_updated TIMESTAMPTZ DEFAULT NOW()
        );"""

        # AI Insights History Table
        sql_create_ai_insights_history = """
        CREATE TABLE IF NOT EXISTS ai_insights_history (
            id SERIAL PRIMARY KEY,
            symbol TEXT NOT NULL,
            insight_type TEXT NOT NULL,
            insight_content TEXT NOT NULL,
            confidence_score NUMERIC(5, 4) DEFAULT 0.0000,
            impact_score NUMERIC(5, 4) DEFAULT 0.0000,
            market_context JSONB DEFAULT '{}',
            outcome_verified BOOLEAN DEFAULT FALSE,
            outcome_accuracy NUMERIC(5, 4),
            created_at TIMESTAMPTZ DEFAULT NOW()
        );"""

        # AI Predictions Table
        sql_create_ai_predictions = """
        CREATE TABLE IF NOT EXISTS ai_predictions (
            id SERIAL PRIMARY KEY,
            symbol TEXT NOT NULL,
            prediction_type TEXT NOT NULL,
            prediction_value NUMERIC(10, 4),
            prediction_direction TEXT CHECK (prediction_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            confidence_score NUMERIC(5, 4) DEFAULT 0.0000,
            time_horizon TEXT NOT NULL,
            prediction_timestamp TIMESTAMPTZ DEFAULT NOW(),
            target_timestamp TIMESTAMPTZ,
            actual_value NUMERIC(10, 4),
            actual_direction TEXT CHECK (actual_direction IN ('UP', 'DOWN', 'NEUTRAL')),
            prediction_accurate BOOLEAN,
            accuracy_score NUMERIC(5, 4),
            model_version TEXT DEFAULT 'v2.5',
            market_context JSONB DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),

            CONSTRAINT chk_confidence_score CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
            CONSTRAINT chk_accuracy_score CHECK (accuracy_score IS NULL OR (accuracy_score >= 0.0 AND accuracy_score <= 1.0))
        );"""

        # AI Model Performance Table
        sql_create_ai_model_performance = """
        CREATE TABLE IF NOT EXISTS ai_model_performance (
            id SERIAL PRIMARY KEY,
            model_type TEXT NOT NULL,
            prediction_type TEXT NOT NULL,
            accuracy_score NUMERIC(5, 4) DEFAULT 0.0000,
            precision_score NUMERIC(5, 4) DEFAULT 0.0000,
            recall_score NUMERIC(5, 4) DEFAULT 0.0000,
            f1_score NUMERIC(5, 4) DEFAULT 0.0000,
            sample_period_start TIMESTAMPTZ,
            sample_period_end TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );"""

        # Memory Intelligence Tables
        sql_create_memory_entities = """
        CREATE TABLE IF NOT EXISTS memory_entities (
            entity_id VARCHAR(36) PRIMARY KEY,
            entity_type VARCHAR(50) NOT NULL,
            entity_name VARCHAR(255) NOT NULL,
            description TEXT,
            symbol VARCHAR(20) NOT NULL,
            confidence_score DECIMAL(5,4) DEFAULT 0.0000,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            last_accessed TIMESTAMPTZ DEFAULT NOW(),
            access_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            CONSTRAINT chk_confidence_score CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0)
        );"""

        sql_create_memory_relations = """
        CREATE TABLE IF NOT EXISTS memory_relations (
            relation_id VARCHAR(36) PRIMARY KEY,
            source_entity_id VARCHAR(36) NOT NULL,
            target_entity_id VARCHAR(36) NOT NULL,
            relation_type VARCHAR(50) NOT NULL,
            relation_strength DECIMAL(5,4) DEFAULT 0.5000,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            last_validated TIMESTAMPTZ DEFAULT NOW(),
            validation_count INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            CONSTRAINT chk_relation_strength CHECK (relation_strength >= 0.0 AND relation_strength <= 1.0)
        );"""

        sql_create_memory_observations = """
        CREATE TABLE IF NOT EXISTS memory_observations (
            observation_id VARCHAR(36) PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            observation_type VARCHAR(50) NOT NULL,
            description TEXT NOT NULL,
            timestamp TIMESTAMPTZ DEFAULT NOW(),
            success_rate DECIMAL(5,4) DEFAULT 0.0000,
            metadata JSONB DEFAULT '{}',
            related_entity_id VARCHAR(36),
            validation_status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            CONSTRAINT chk_success_rate CHECK (success_rate >= 0.0 AND success_rate <= 1.0)
        );"""

        commands = [sql_create_daily_ohlcv, sql_create_daily_eots_metrics, sql_create_trade_outcomes,
                   sql_create_atif_recommendations, sql_create_atif_insights, sql_create_atif_performance,
                   sql_create_ai_learning_patterns, sql_create_ai_insights_history, sql_create_ai_predictions,
                   sql_create_ai_model_performance, sql_create_memory_entities, sql_create_memory_relations,
                   sql_create_memory_observations]
        # Create indexes for memory tables
        memory_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_memory_entities_symbol ON memory_entities(symbol);",
            "CREATE INDEX IF NOT EXISTS idx_memory_entities_type ON memory_entities(entity_type);",
            "CREATE INDEX IF NOT EXISTS idx_memory_entities_active ON memory_entities(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_memory_entities_created ON memory_entities(created_at DESC);",
            "CREATE INDEX IF NOT EXISTS idx_memory_relations_source ON memory_relations(source_entity_id);",
            "CREATE INDEX IF NOT EXISTS idx_memory_relations_target ON memory_relations(target_entity_id);",
            "CREATE INDEX IF NOT EXISTS idx_memory_relations_active ON memory_relations(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_memory_observations_symbol ON memory_observations(symbol);",
            "CREATE INDEX IF NOT EXISTS idx_memory_observations_type ON memory_observations(observation_type);"
        ]

        # Add missing columns to existing tables if needed
        alter_commands = [
            "ALTER TABLE memory_entities ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;",
            "ALTER TABLE memory_relations ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;",
            "ALTER TABLE ai_insights_history ADD COLUMN IF NOT EXISTS symbol VARCHAR(20);"
        ]

        try:
            with self._conn.cursor() as cur:  # type: ignore
                # Create tables
                for cmd in commands:
                    cur.execute(cmd)  # type: ignore
                # Add missing columns to existing tables
                for alter_cmd in alter_commands:
                    try:
                        cur.execute(alter_cmd)  # type: ignore
                    except Exception as alter_e:
                        # Column might already exist or table might not exist yet
                        self.logger.debug(f"Alter command skipped: {alter_e}")
                # Create indexes
                for idx in memory_indexes:
                    try:
                        cur.execute(idx)  # type: ignore
                    except Exception as idx_e:
                        # Index might already exist
                        self.logger.debug(f"Index creation skipped: {idx_e}")
            self.logger.info("Database schema initialized with memory intelligence tables.")
        except Exception as e:
            self.logger.error(f"Failed to initialize schema: {e}")
            raise

    def query_metric(self, table_name: str, metric_name: str, start_date: date, end_date: date) -> Optional[pd.Series]:
        try:
            query = sql.SQL("SELECT date, {metric} FROM {table} WHERE date >= %s AND date <= %s ORDER BY date;").format(
                metric=sql.Identifier(metric_name),
                table=sql.Identifier(table_name)
            )
            with self._conn.cursor() as cur:  # type: ignore
                cur.execute(query, (start_date, end_date))
                rows = cur.fetchall()
            if not rows:
                return None
            df = pd.DataFrame(rows)
            return df.set_index('date')[metric_name]
        except Exception as e:
            self.logger.error(f"Failed to query metric: {e}")
            return None

    def query_ohlcv(self, table_name: str, start_date: date, end_date: date) -> Optional[pd.DataFrame]:
        try:
            if self.db_type == "sqlite":
                query = f"SELECT * FROM {table_name} WHERE date >= ? AND date <= ? ORDER BY date;"
                cursor = self._conn.cursor()
                cursor.execute(query, (start_date, end_date))
                rows = cursor.fetchall()
                if not rows:
                    return None
                # Convert SQLite rows to DataFrame
                columns = [description[0] for description in cursor.description]
                df = pd.DataFrame(rows, columns=columns)
                return df
            else:  # PostgreSQL
                query = sql.SQL("SELECT * FROM {table} WHERE date >= %s AND date <= %s ORDER BY date;").format(
                    table=sql.Identifier(table_name)
                )
                with self._conn.cursor() as cur:  # type: ignore
                    cur.execute(query, (start_date, end_date))
                    rows = cur.fetchall()
                if not rows:
                    return None
                df = pd.DataFrame(rows)
                return df
        except Exception as e:
            self.logger.error(f"Failed to query OHLCV: {e}")
            return None

    def insert_record(self, table_name: str, data: Dict[str, Any]) -> None:
        try:
            columns = list(data.keys())
            values = list(data.values())

            if self.db_type == "sqlite":
                placeholders = ', '.join(['?' for _ in columns])
                query = f"INSERT OR IGNORE INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                cursor = self._conn.cursor()
                cursor.execute(query, values)
                self._conn.commit()
            else:  # PostgreSQL
                query = sql.SQL("INSERT INTO {table} ({fields}) VALUES ({placeholders}) ON CONFLICT DO NOTHING;").format(
                    table=sql.Identifier(table_name),
                    fields=sql.SQL(', ').join(map(sql.Identifier, columns)),
                    placeholders=sql.SQL(', ').join(sql.Placeholder() * len(columns))
                )
                with self._conn.cursor() as cur:  # type: ignore
                    cur.execute(query, values)
            self.logger.debug(f"Inserted record into {table_name}.")
        except Exception as e:
            self.logger.error(f"Failed to insert record: {e}")
            raise

    # ATIF-specific database methods
    def store_atif_recommendation(self, recommendation_data: Dict[str, Any]) -> None:
        """Store ATIF recommendation to database."""
        try:
            self.insert_record("atif_recommendations", recommendation_data)
            self.logger.info(f"Stored ATIF recommendation for {recommendation_data.get('symbol')}")
        except Exception as e:
            self.logger.error(f"Failed to store ATIF recommendation: {e}")
            raise

    def store_atif_insight(self, insight_data: Dict[str, Any]) -> None:
        """Store ATIF insight to database."""
        try:
            self.insert_record("atif_insights", insight_data)
            self.logger.info(f"Stored ATIF insight: {insight_data.get('insight_title')}")
        except Exception as e:
            self.logger.error(f"Failed to store ATIF insight: {e}")
            raise

    def store_atif_performance(self, performance_data: Dict[str, Any]) -> None:
        """Store ATIF performance data to database."""
        try:
            self.insert_record("atif_performance", performance_data)
            self.logger.info(f"Stored ATIF performance for {performance_data.get('symbol')}")
        except Exception as e:
            self.logger.error(f"Failed to store ATIF performance: {e}")
            raise

    def get_recent_atif_recommendations(self, symbol: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent ATIF recommendations for a symbol."""
        try:
            query = sql.SQL("""
                SELECT * FROM atif_recommendations
                WHERE symbol = %s AND status = 'ACTIVE'
                ORDER BY timestamp DESC
                LIMIT %s
            """)
            with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                cur.execute(query, (symbol, limit))
                rows = cur.fetchall()
            return rows
        except Exception as e:
            self.logger.error(f"Failed to get ATIF recommendations: {e}")
            return []

    def get_recent_atif_insights(self, symbol: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent ATIF insights for a symbol."""
        try:
            query = sql.SQL("""
                SELECT * FROM atif_insights
                WHERE symbol = %s AND (expires_at IS NULL OR expires_at > NOW())
                ORDER BY timestamp DESC
                LIMIT %s
            """)
            with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                cur.execute(query, (symbol, limit))
                rows = cur.fetchall()
            return rows
        except Exception as e:
            self.logger.error(f"Failed to get ATIF insights: {e}")
            return []

    def get_atif_performance_stats(self, symbol: str, days: int = 30) -> Dict[str, Any]:
        """Get ATIF performance statistics for a symbol."""
        try:
            query = sql.SQL("""
                SELECT
                    COUNT(*) as total_trades,
                    AVG(CASE WHEN outcome = 'WIN' THEN 1.0 ELSE 0.0 END) as win_rate,
                    AVG(pnl_percentage) as avg_pnl,
                    AVG(conviction_score) as avg_conviction,
                    AVG(hold_duration_hours) as avg_hold_hours
                FROM atif_performance
                WHERE symbol = %s AND entry_timestamp >= NOW() - INTERVAL '%s days'
            """)
            with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                cur.execute(query, (symbol, days))
                result = cur.fetchone()
            return result or {}
        except Exception as e:
            self.logger.error(f"Failed to get ATIF performance stats: {e}")
            return {}

    def insert_batch_data(self, table_name: str, data: List[Dict[str, Any]]) -> None:
        if not data:
            return
        try:
            columns = list(data[0].keys())
            values = [list(d.values()) for d in data]
            query = sql.SQL("INSERT INTO {table} ({fields}) VALUES ({placeholders}) ON CONFLICT DO NOTHING;").format(
                table=sql.Identifier(table_name),
                fields=sql.SQL(', ').join(map(sql.Identifier, columns)),
                placeholders=sql.SQL(', ').join(sql.Placeholder() * len(columns))
            )
            with self._conn.cursor() as cur:  # type: ignore
                cur.executemany(query, values)
            self.logger.info(f"Inserted {len(data)} records into {table_name}.")
        except Exception as e:
            self.logger.error(f"Failed to insert batch data: {e}")
            raise

    # === MEMORY INTELLIGENCE ENGINE DATABASE METHODS ===

    def store_memory_entity(self, entity_data: Dict[str, Any]) -> None:
        """Store memory entity (pattern, outcome, insight) to database."""
        try:
            # Ensure required fields
            entity_data.setdefault('created_at', datetime.now().isoformat())
            entity_data.setdefault('entity_type', 'pattern')
            entity_data.setdefault('confidence_score', 0.0)

            self.insert_record("memory_entities", entity_data)
            self.logger.info(f"Memory entity stored: {entity_data.get('entity_name', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"Error storing memory entity: {str(e)}")
            raise

    def store_memory_relation(self, relation_data: Dict[str, Any]) -> None:
        """Store memory relation (connections between entities) to database."""
        try:
            # Ensure required fields
            relation_data.setdefault('created_at', datetime.now().isoformat())
            relation_data.setdefault('relation_strength', 0.5)

            self.insert_record("memory_relations", relation_data)
            self.logger.info(f"Memory relation stored: {relation_data.get('relation_type', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"Error storing memory relation: {str(e)}")
            raise

    def store_memory_observation(self, observation_data: Dict[str, Any]) -> None:
        """Store memory observation (market event, outcome) to database."""
        try:
            # Ensure required fields
            observation_data.setdefault('timestamp', datetime.now().isoformat())
            observation_data.setdefault('observation_type', 'market_event')
            observation_data.setdefault('success_rate', 0.0)

            self.insert_record("memory_observations", observation_data)
            self.logger.info(f"Memory observation stored: {observation_data.get('symbol', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"Error storing memory observation: {str(e)}")
            raise

    def get_memory_patterns(self, symbol: str = None, pattern_type: str = None,
                           limit: int = 50) -> List[Dict[str, Any]]:
        """Retrieve memory patterns from database."""
        try:
            query_parts = ["SELECT * FROM memory_entities WHERE 1=1"]
            params = []

            if symbol:
                query_parts.append("AND symbol = %s")
                params.append(symbol)

            if pattern_type:
                query_parts.append("AND entity_type = %s")
                params.append(pattern_type)

            query_parts.append("ORDER BY confidence_score DESC, created_at DESC LIMIT %s")
            params.append(limit)

            query = sql.SQL(" ".join(query_parts))
            with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                cur.execute(query, params)
                return cur.fetchall()
        except Exception as e:
            self.logger.error(f"Error retrieving memory patterns: {str(e)}")
            return []

    def search_memory_intelligence(self, search_query: str, symbol: str = None,
                                 limit: int = 20) -> Dict[str, Any]:
        """Search memory intelligence using text search."""
        try:
            # Search entities
            entity_query_parts = [
                "SELECT * FROM memory_entities",
                "WHERE (entity_name ILIKE %s OR description ILIKE %s OR metadata::text ILIKE %s)"
            ]
            params = [f"%{search_query}%", f"%{search_query}%", f"%{search_query}%"]

            if symbol:
                entity_query_parts.append("AND symbol = %s")
                params.append(symbol)

            entity_query_parts.append("ORDER BY confidence_score DESC LIMIT %s")
            params.append(limit)

            entity_query = sql.SQL(" ".join(entity_query_parts))

            with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                cur.execute(entity_query, params)
                entities = cur.fetchall()

            # Search observations
            obs_query_parts = [
                "SELECT * FROM memory_observations",
                "WHERE (description ILIKE %s OR metadata::text ILIKE %s)"
            ]
            obs_params = [f"%{search_query}%", f"%{search_query}%"]

            if symbol:
                obs_query_parts.append("AND symbol = %s")
                obs_params.append(symbol)

            obs_query_parts.append("ORDER BY success_rate DESC LIMIT %s")
            obs_params.append(limit)

            obs_query = sql.SQL(" ".join(obs_query_parts))

            with self._conn.cursor(row_factory=dict_row) as cur:  # type: ignore
                cur.execute(obs_query, obs_params)
                observations = cur.fetchall()

            return {
                "entities": entities,
                "observations": observations,
                "search_query": search_query,
                "total_results": len(entities) + len(observations)
            }
        except Exception as e:
            self.logger.error(f"Error searching memory intelligence: {str(e)}")
            return {"entities": [], "observations": [], "search_query": search_query, "total_results": 0}