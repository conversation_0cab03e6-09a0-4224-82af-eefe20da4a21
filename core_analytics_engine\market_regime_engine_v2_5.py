# core_analytics_engine/market_regime_engine_v2_5.py
# EOTS v2.5 - S-GRADE PRODUCTION HARDENED & OPTIMIZED ARTIFACT

import logging
import re
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, time

import numpy as np
import pandas as pd
from pydantic import BaseModel, Field, field_validator

from data_models.eots_schemas_v2_5 import (
    ProcessedUnderlyingAggregatesV2_5,
    MarketRegimeEngineSettings,
    RegimeRule,
    ProcessedStrikeLevelMetricsV2_5,
    ProcessedDataBundleV2_5
)
from utils.config_manager_v2_5 import ConfigManagerV2_5

logger = logging.getLogger(__name__)

class ParsedRule(BaseModel):
    """Pydantic model for parsed regime rules."""
    regime_name: str = Field(..., description="Name of the regime this rule belongs to")
    metric: str = Field(..., description="Metric name to evaluate")
    operator: str = Field(..., description="Comparison operator")
    value: Union[str, float, int, bool] = Field(..., description="Target value for comparison")
    aggregator: Optional[str] = Field(None, description="Aggregation method if applicable")
    selector: Optional[str] = Field(None, description="Selector for metric (e.g., @ATM)")

    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v):
        """Validate that the operator is supported."""
        valid_operators = ["_lt", "_gt", "_lte", "_gte", "_eq", "_neq", "_abs_gt", "_abs_lt", "_in_list", "_contains"]
        if v not in valid_operators:
            raise ValueError(f"Unsupported operator: {v}. Valid operators: {valid_operators}")
        return v

    @field_validator('aggregator')
    @classmethod
    def validate_aggregator(cls, v):
        """Validate that the aggregator is supported."""
        if v is not None:
            valid_aggregators = ["mean", "sum", "max", "min", "std", "count"]
            if v not in valid_aggregators:
                raise ValueError(f"Unsupported aggregator: {v}. Valid aggregators: {valid_aggregators}")
        return v

    @field_validator('selector')
    @classmethod
    def validate_selector(cls, v):
        """Validate that the selector format is correct."""
        if v is not None:
            if v.startswith('@'):
                # ATM selector
                if v not in ["@ATM"]:
                    raise ValueError(f"Unsupported @ selector: {v}. Valid selectors: @ATM")
            elif v.startswith('[PERCENTILE=') and v.endswith(']'):
                # Percentile selector
                try:
                    percentile_str = v[12:-1]
                    percentile = float(percentile_str)
                    if not 0 <= percentile <= 100:
                        raise ValueError(f"Percentile must be between 0 and 100, got {percentile}")
                except ValueError as e:
                    raise ValueError(f"Invalid percentile format in selector {v}: {e}")
            else:
                raise ValueError(f"Invalid selector format: {v}")
        return v

class MarketRegimeEngineV2_5:
    """
    Pydantic-first market regime engine that determines market regime based on
    configuration-driven rules and validated data models.
    """

    def __init__(self, config_manager: ConfigManagerV2_5):
        """
        Initialize the MarketRegimeEngineV2_5.

        Args:
            config_manager: The system's configuration manager
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager

        # Get settings from config manager
        settings_data = self.config_manager.get_setting("market_regime_engine_settings")
        if not settings_data:
            raise ValueError("FATAL: market_regime_engine_settings not found in configuration.")

        try:
            # Check if it's already a Pydantic model or needs to be created
            if isinstance(settings_data, MarketRegimeEngineSettings):
                self.settings = settings_data
            else:
                # It's a dictionary, create the Pydantic model
                self.settings = MarketRegimeEngineSettings(**settings_data)
        except Exception as e:
            self.logger.error(f"Failed to validate market regime engine settings: {e}")
            raise ValueError(f"Invalid market regime engine configuration: {e}")

        # Access regime rules from the validated Pydantic model
        self.regime_rules = self.settings.regime_rules
        self.evaluation_order = self.settings.regime_evaluation_order
        self.default_regime = self.settings.default_regime

        # Parse and validate all rules at initialization
        try:
            self._parsed_rules = self._parse_all_rules()
            self.logger.info(f"Successfully parsed {sum(len(rules) for rules in self._parsed_rules.values())} rules across {len(self._parsed_rules)} regimes.")
        except Exception as e:
            self.logger.error(f"Failed to parse regime rules: {e}")
            self._parsed_rules = {}

        # Validate that we have at least one valid regime
        if not self._parsed_rules:
            self.logger.warning("No valid regime rules found. System will always return default regime.")

        self.logger.info("MarketRegimeEngineV2_5 initialized with Pydantic validation.")

    def _parse_all_rules(self) -> Dict[str, List[ParsedRule]]:
        """Parse and validate all regime rules at initialization."""
        parsed_rules = {}

        for regime_name, rule_list in self.regime_rules.items():
            parsed_rules[regime_name] = []

            # Handle both new list format and legacy dict format
            if isinstance(rule_list, list):
                # New format: list of rule dictionaries or RegimeRule objects
                for rule_data in rule_list:
                    try:
                        if isinstance(rule_data, dict):
                            # Dictionary format
                            parsed_rule = ParsedRule(
                                regime_name=regime_name,
                                metric=rule_data['metric'],
                                operator=rule_data['operator'],
                                value=rule_data['value'],
                                selector=rule_data.get('selector'),
                                aggregator=rule_data.get('aggregator')
                            )
                            parsed_rules[regime_name].append(parsed_rule)
                        elif hasattr(rule_data, 'metric'):
                            # RegimeRule object (from Pydantic validation)
                            parsed_rule = ParsedRule(
                                regime_name=regime_name,
                                metric=rule_data.metric,
                                operator=rule_data.operator,
                                value=rule_data.value,
                                selector=rule_data.selector,
                                aggregator=rule_data.aggregator
                            )
                            parsed_rules[regime_name].append(parsed_rule)
                        else:
                            self.logger.warning(f"Invalid rule format in regime {regime_name}: expected dict or RegimeRule, got {type(rule_data)}")
                    except Exception as e:
                        self.logger.error(f"Failed to parse rule in regime {regime_name}: {e}")
                        continue

            elif isinstance(rule_list, dict):
                # Legacy format: dict of rule_key: rule_value
                self.logger.warning(f"Using legacy rule format for regime {regime_name}. Consider upgrading to list format.")
                for rule_key, rule_data in rule_list.items():
                    try:
                        if isinstance(rule_data, dict) and 'metric' in rule_data:
                            # New format within legacy structure
                            parsed_rule = ParsedRule(
                                regime_name=regime_name,
                                metric=rule_data['metric'],
                                operator=rule_data['operator'],
                                value=rule_data['value'],
                                selector=rule_data.get('selector'),
                                aggregator=rule_data.get('aggregator')
                            )
                        else:
                            # Legacy format: parse from rule key
                            parsed_rule = self._parse_legacy_rule_key(regime_name, rule_key, rule_data)

                        parsed_rules[regime_name].append(parsed_rule)

                    except Exception as e:
                        self.logger.error(f"Failed to parse rule {rule_key} for regime {regime_name}: {e}")
                        continue
            else:
                self.logger.warning(f"Invalid rule format for regime {regime_name}: expected list or dict, got {type(rule_list)}")

        return parsed_rules

    def _parse_legacy_rule_key(self, regime_name: str, rule_key: str, rule_value: Any) -> ParsedRule:
        """Parse legacy rule format for backward compatibility."""
        # Extract metric, selector, and operator from rule key
        metric = rule_key
        selector = None
        aggregator = None

        # Handle selectors like @ATM
        if '@' in rule_key:
            parts = rule_key.split('@', 1)
            metric = parts[0]
            selector = '@' + parts[1]

        # Handle aggregators like [AGG=mean]
        if '[AGG=' in rule_key:
            agg_start = rule_key.find('[AGG=')
            agg_end = rule_key.find(']', agg_start)
            if agg_end != -1:
                aggregator = rule_key[agg_start+5:agg_end]
                metric = rule_key[:agg_start]

        # Extract operator from metric name
        operator = "_eq"  # default
        for op in ["_gt", "_lt", "_gte", "_lte", "_eq", "_neq", "_abs_gt", "_abs_lt"]:
            if metric.endswith(op):
                operator = op
                metric = metric[:-len(op)]
                break

        return ParsedRule(
            regime_name=regime_name,
            metric=metric,
            operator=operator,
            value=rule_value,
            aggregator=aggregator,
            selector=selector
        )

    def determine_market_regime(self,
                                data_bundle: ProcessedDataBundleV2_5) -> str:
        """
        Determines the current market regime by evaluating rules in the specified order.

        Args:
            data_bundle: Validated Pydantic data bundle containing all processed metrics

        Returns:
            str: The determined market regime name
        """
        # Validate input using Pydantic
        if not isinstance(data_bundle, ProcessedDataBundleV2_5):
            self.logger.error("Invalid input type to determine_market_regime. Expected ProcessedDataBundleV2_5.")
            return self.default_regime

        # Validate that we have parsed rules
        if not self._parsed_rules:
            self.logger.warning("No parsed rules available. Returning default regime.")
            return self.default_regime

        und_data = data_bundle.underlying_data_enriched
        if not und_data or not hasattr(und_data, 'symbol'):
            self.logger.error("Invalid underlying data in data bundle.")
            return self.default_regime

        self.logger.debug(f"Determining market regime for {und_data.symbol}...")

        # Create rule condition evaluator with error handling
        try:
            evaluator = self._RuleConditionEvaluator(data_bundle, self)
        except Exception as e:
            self.logger.error(f"Failed to create rule evaluator: {e}")
            return self.default_regime

        # Evaluate regimes in order
        for regime_name in self.evaluation_order:
            parsed_rules = self._parsed_rules.get(regime_name, [])
            if not parsed_rules:
                self.logger.debug(f"No parsed rules found for regime '{regime_name}'. Skipping.")
                continue

            try:
                if self._evaluate_regime_rules(parsed_rules, evaluator):
                    self.logger.info(f"Market regime matched: {regime_name}")
                    return regime_name
            except Exception as e:
                self.logger.error(f"Unhandled exception during evaluation of regime '{regime_name}': {e}", exc_info=True)
                continue

        self.logger.info(f"No regime rules matched. Falling back to default: {self.default_regime}")
        return self.default_regime

    def _evaluate_regime_rules(self, rules: List[ParsedRule], evaluator: '_RuleConditionEvaluator') -> bool:
        """
        Evaluate a list of rules for a regime (AND logic - all must be true).

        Args:
            rules: List of parsed rules to evaluate
            evaluator: Rule condition evaluator instance

        Returns:
            bool: True if all rules pass, False otherwise
        """
        for rule in rules:
            try:
                if not evaluator.evaluate_rule(rule):
                    return False
            except Exception as e:
                self.logger.warning(f"Rule evaluation failed for {rule.metric}: {e}")
                return False

        return True

    class _RuleConditionEvaluator:
        """Internal class for evaluating rule conditions with proper Pydantic data handling."""

        def __init__(self, data_bundle: ProcessedDataBundleV2_5, engine: 'MarketRegimeEngineV2_5'):
            self.data_bundle = data_bundle
            self.und_data = data_bundle.underlying_data_enriched
            self.df_strike = pd.DataFrame([item.model_dump() for item in data_bundle.strike_level_data_with_metrics])
            self.df_chain = pd.DataFrame([item.model_dump() for item in data_bundle.options_data_with_metrics])
            self.engine = engine
            self.logger = engine.logger

            # Set strike as index if available
            if not self.df_strike.empty and 'strike' in self.df_strike.columns:
                self.df_strike.set_index('strike', inplace=True)

        def evaluate_rule(self, rule: ParsedRule) -> bool:
            """Evaluate a single parsed rule."""
            try:
                # Resolve the metric value
                actual_value = self._resolve_metric_value(rule)
                if actual_value is None:
                    self.logger.debug(f"Metric '{rule.metric}' resolved to None for rule evaluation.")
                    return False

                # Resolve the target value (handle dynamic thresholds)
                target_value = self._resolve_target_value(rule.value)

                # Perform the comparison
                return self._perform_comparison(actual_value, rule.operator, target_value)

            except Exception as e:
                self.logger.warning(f"Failed to evaluate rule for metric '{rule.metric}': {e}")
                return False

        def _resolve_metric_value(self, rule: ParsedRule) -> Any:
            """Resolve the actual value of a metric from the data bundle."""
            try:
                # Check if it's a ticker context metric first
                if hasattr(self.und_data, 'ticker_context_dict_v2_5') and self.und_data.ticker_context_dict_v2_5:
                    context_dict = self.und_data.ticker_context_dict_v2_5.model_dump() if hasattr(self.und_data.ticker_context_dict_v2_5, 'model_dump') else self.und_data.ticker_context_dict_v2_5
                    if rule.metric in context_dict:
                        return context_dict[rule.metric]

                # Handle aggregated metrics
                if rule.aggregator and not self.df_strike.empty:
                    if rule.metric in self.df_strike.columns:
                        series = pd.to_numeric(self.df_strike[rule.metric], errors='coerce')
                        if rule.aggregator == 'mean':
                            return series.mean()
                        elif rule.aggregator == 'sum':
                            return series.sum()
                        elif rule.aggregator == 'max':
                            return series.max()
                        elif rule.aggregator == 'min':
                            return series.min()

                # Handle selector-based metrics
                if rule.selector:
                    return self._resolve_selector_metric(rule)

                # Default: try to get from underlying data
                return getattr(self.und_data, rule.metric, None)

            except Exception as e:
                self.logger.warning(f"Error resolving metric value for '{rule.metric}': {e}")
                return None

        def _resolve_selector_metric(self, rule: ParsedRule) -> Any:
            """Resolve metrics with selectors like @ATM."""
            try:
                if rule.selector == '@ATM' and not self.df_strike.empty:
                    # Find the strike closest to current price
                    if self.und_data.price is None:
                        return None

                    if not pd.api.types.is_numeric_dtype(self.df_strike.index):
                        return None

                    # Calculate absolute differences
                    strikes = self.df_strike.index.values
                    price_diffs = np.abs(strikes - self.und_data.price)
                    min_idx = np.argmin(price_diffs)
                    target_strike = strikes[min_idx]

                    if rule.metric in self.df_strike.columns:
                        return self.df_strike.loc[target_strike, rule.metric]
                    else:
                        return None

                # Handle percentile selectors
                if rule.selector and rule.selector.startswith('[PERCENTILE=') and rule.selector.endswith(']'):
                    percentile_str = rule.selector[12:-1]
                    try:
                        percentile = float(percentile_str)
                        if rule.metric in self.df_strike.columns:
                            return self.df_strike[rule.metric].quantile(percentile / 100.0)
                    except ValueError:
                        pass

                return None

            except Exception as e:
                self.logger.warning(f"Error resolving selector metric '{rule.metric}{rule.selector}': {e}")
                return None

        def _resolve_target_value(self, value: Union[str, float, int, bool]) -> Any:
            """Resolve target values, handling dynamic thresholds."""
            if isinstance(value, str) and value.startswith("dynamic_threshold:"):
                threshold_key = value.split(":", 1)[1]
                dynamic_thresholds = getattr(self.und_data, 'dynamic_thresholds', {})
                return dynamic_thresholds.get(threshold_key, value)

            return value

        def _perform_comparison(self, actual: Any, operator: str, target: Any) -> bool:
            """Perform the actual comparison between values."""
            try:
                if operator == "_lt":
                    return actual < target
                elif operator == "_gt":
                    return actual > target
                elif operator == "_lte":
                    return actual <= target
                elif operator == "_gte":
                    return actual >= target
                elif operator == "_eq":
                    return actual == target
                elif operator == "_neq":
                    return actual != target
                elif operator == "_abs_gt":
                    return abs(actual) > target
                elif operator == "_abs_lt":
                    return abs(actual) < target
                elif operator == "_in_list":
                    return actual in target if isinstance(target, (list, tuple)) else False
                elif operator == "_contains":
                    return isinstance(actual, str) and str(target) in actual
                else:
                    self.logger.warning(f"Unknown operator: {operator}")
                    return False
            except (TypeError, ValueError) as e:
                self.logger.warning(f"Comparison failed: {actual} {operator} {target} - {e}")
                return False

    # Legacy methods for backward compatibility
    def determine_regime(self, metrics: Dict[str, float]) -> str:
        """
        Legacy method for backward compatibility.

        Args:
            metrics: Dictionary of metric values

        Returns:
            str: Determined regime name
        """
        self.logger.warning("Using legacy determine_regime method. Consider upgrading to determine_market_regime with ProcessedDataBundleV2_5.")

        try:
            # Evaluate each regime in order using legacy logic
            for regime in self.evaluation_order:
                if self._evaluate_legacy_regime(regime, metrics):
                    return regime

            return self.default_regime

        except Exception as e:
            self.logger.error(f"Error determining market regime: {e}", exc_info=True)
            return self.default_regime

    def _evaluate_legacy_regime(self, regime: str, metrics: Dict[str, float]) -> bool:
        """Legacy regime evaluation for backward compatibility."""
        try:
            rules = self.regime_rules.get(regime, {})
            if not rules:
                return False

            # Check each rule using legacy logic
            for rule_key, threshold in rules.items():
                if isinstance(threshold, dict):
                    # Skip new format rules in legacy mode
                    continue

                # Extract metric name and operator from rule key
                metric = rule_key
                operator = "_eq"  # default

                for op in ["_gt", "_lt", "_gte", "_lte", "_eq", "_neq", "_abs_gt", "_abs_lt"]:
                    if rule_key.endswith(op):
                        operator = op
                        metric = rule_key[:-len(op)]
                        break

                if metric not in metrics:
                    return False

                value = metrics[metric]

                # Perform comparison
                if not self._legacy_comparison(value, operator, threshold):
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error evaluating legacy regime {regime}: {e}", exc_info=True)
            return False

    def _legacy_comparison(self, actual: Any, operator: str, target: Any) -> bool:
        """Legacy comparison logic."""
        try:
            if operator == "_lt":
                return actual < target
            elif operator == "_gt":
                return actual > target
            elif operator == "_lte":
                return actual <= target
            elif operator == "_gte":
                return actual >= target
            elif operator == "_eq":
                return actual == target
            elif operator == "_neq":
                return actual != target
            elif operator == "_abs_gt":
                return abs(actual) > target
            elif operator == "_abs_lt":
                return abs(actual) < target
            else:
                return False
        except (TypeError, ValueError):
            return False