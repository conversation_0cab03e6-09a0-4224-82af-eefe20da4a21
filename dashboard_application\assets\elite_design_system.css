/* ===== PREMIUM TRADING DASHBOARD DESIGN SYSTEM ===== */
/* Elite Options System v2.5 - Custom Dashboard Integration */

/* ===== CSS CUSTOM PROPERTIES (Design Tokens) ===== */
:root {
  /* Core Background Palette - Layered depths for visual hierarchy */
  --bg-primary: #0A0A0A;        /* Deepest canvas */
  --bg-secondary: #121212;      /* Primary panels */
  --bg-tertiary: #1A1A1A;       /* Elevated surfaces */
  --bg-elevated: #1E1E1E;       /* Tooltips, modals */
  --bg-hover: #242424;          /* Interactive hover states */
  
  /* Typography Hierarchy - Refined contrast without harshness */
  --text-primary: #E8E8E8;      /* Primary headings, key data */
  --text-secondary: #B8B8B8;    /* Secondary labels, descriptions */
  --text-muted: #888888;        /* Subtle annotations, gridlines */
  --text-accent: #A0A0A0;       /* Emphasized but not primary */
  
  /* Accent Color System - Sophisticated highlights */
  --accent-primary: #4A9EFF;    /* Soft blue - primary highlights */
  --accent-primary-hover: #5BADFF;
  --accent-primary-dim: #3A7ACC;
  
  --accent-secondary: #FFB84A;  /* Muted amber - secondary emphasis */
  --accent-secondary-hover: #FFC866;
  --accent-secondary-dim: #CC9238;
  
  --accent-tertiary: #8B5CF6;   /* Soft violet - rare special indicators */
  --accent-tertiary-hover: #A78BFA;
  --accent-tertiary-dim: #7C3AED;
  
  /* Financial Data Colors - Sophisticated, not garish */
  --positive: #10B981;          /* Emerald - gains */
  --positive-dim: #059669;
  --negative: #EF4444;          /* Refined red - losses */
  --negative-dim: #DC2626;
  --neutral: #6B7280;           /* Gray - no change */
  
  /* Borders & Dividers - Extremely subtle definition */
  --border-primary: #2A2A2A;
  --border-secondary: #222222;
  --border-accent: #333333;
  
  /* Shadows - Depth without drama */
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-glow: 0 0 20px rgba(74, 158, 255, 0.2);
  --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
  
  /* Animation Easing - Luxury feel */
  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Spacing Scale - Generous, harmonious proportions */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  
  /* Typography Scale */
  --text-xs: 0.75rem;     /* 12px - small annotations */
  --text-sm: 0.875rem;    /* 14px - secondary text */
  --text-base: 1rem;      /* 16px - primary text */
  --text-lg: 1.25rem;     /* 20px - section headers */
  --text-xl: 1.5rem;      /* 24px - page titles */
  
  /* Border Radius - Consistent, modern */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
}

/* ===== GLOBAL STYLES ===== */
body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* ===== COMPONENT CLASSES ===== */

/* Panel Base - Standard panel styling */
.panel-base {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-soft);
  padding: var(--space-lg);
  transition: all 150ms var(--ease-out);
}

.panel-elevated {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-elevated);
  padding: var(--space-lg);
}

/* Interactive States - Luxurious micro-interactions */
.interactive-base {
  transition: all 150ms var(--ease-out);
  cursor: pointer;
}

.interactive-base:hover {
  background: var(--bg-hover);
  border-color: var(--border-accent);
  transform: translateY(-1px);
}

/* Typography Components */
.heading-primary {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.025em;
  margin: 0 0 var(--space-md) 0;
}

.heading-secondary {
  font-size: var(--text-lg);
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: -0.01em;
  margin: 0 0 var(--space-sm) 0;
}

.text-mono {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-feature-settings: 'zero', 'ss01';
  font-variant-numeric: tabular-nums;
}

/* Status Indicators */
.status-positive {
  color: var(--positive);
}

.status-negative {
  color: var(--negative);
}

.status-neutral {
  color: var(--neutral);
}

/* Accent Highlights */
.accent-primary {
  color: var(--accent-primary);
}

.accent-secondary {
  color: var(--accent-secondary);
}

/* ===== LAYOUT SYSTEM ===== */

/* Grid System - Harmonious proportions */
.grid-dashboard {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-lg);
  min-height: 100vh;
  background: var(--bg-primary);
}

.grid-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-md);
}

/* ===== ELITE CONTROL PANEL ===== */
.elite-control-panel {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  margin-bottom: var(--space-xl);
  overflow: hidden;
}

.elite-control-panel .card-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-lg);
}

.elite-control-panel .card-body {
  padding: var(--space-lg);
}

/* Control Input Groups */
.control-input-group {
  margin-bottom: var(--space-md);
}

.control-input-group .form-control {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--text-sm);
  padding: var(--space-sm) var(--space-md);
  transition: all 150ms var(--ease-out);
}

.control-input-group .form-control:focus {
  background: var(--bg-elevated);
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
  outline: none;
}

.control-input-group .form-control::placeholder {
  color: var(--text-muted);
}

/* Form Labels */
.form-label {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  margin-bottom: var(--space-xs);
}

/* ===== BUTTON SYSTEM ===== */
.btn-elite-primary {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-primary-dim) 100%);
  border: 1px solid var(--accent-primary);
  border-radius: var(--radius-md);
  color: #ffffff;
  font-weight: 600;
  padding: var(--space-md) var(--space-lg);
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-soft);
  position: relative;
  overflow: hidden;
}

.btn-elite-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow), var(--shadow-elevated);
  background: linear-gradient(135deg, var(--accent-primary-hover) 0%, var(--accent-primary) 100%);
  border-color: var(--accent-primary-hover);
  color: #ffffff;
}

.btn-elite-primary:active {
  transform: translateY(0);
}

.btn-elite-secondary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-weight: 500;
  padding: var(--space-md) var(--space-lg);
  transition: all 150ms var(--ease-out);
  box-shadow: var(--shadow-soft);
}

.btn-elite-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-elevated);
}

.btn-elite-outline {
  background: transparent;
  border: 1px solid var(--accent-primary);
  border-radius: var(--radius-md);
  color: var(--accent-primary);
  font-weight: 500;
  padding: var(--space-md) var(--space-lg);
  transition: all 150ms var(--ease-out);
}

.btn-elite-outline:hover {
  background: rgba(74, 158, 255, 0.1);
  border-color: var(--accent-primary-hover);
  color: var(--accent-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-glow);
}

/* ===== CARD SYSTEM ===== */
.elite-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  transition: all 200ms var(--ease-out);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-card);
}

.elite-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: rgba(74, 158, 255, 0.3);
  background: var(--bg-tertiary);
}

.elite-card-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-lg);
  position: relative;
}

.elite-card-body {
  padding: var(--space-lg);
}

.elite-card-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* ===== GAUGE AND HEATMAP ENHANCEMENTS ===== */
.elite-gauge-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-card);
}

.elite-gauge-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--accent-primary);
}

.gauge-title {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.gauge-value {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  font-family: 'JetBrains Mono', monospace;
  margin-bottom: var(--space-sm);
}

.elite-heatmap-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-card);
}

.elite-heatmap-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--accent-primary);
}

.heatmap-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-lg);
}

.heatmap-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* ===== NAVIGATION COMPONENTS ===== */
.elite-sidebar {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  height: 100vh;
  width: 280px;
  padding: var(--space-lg);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-elevated);
}

.elite-sidebar-brand {
  margin-bottom: var(--space-2xl);
}

.elite-sidebar-brand h1 {
  font-size: var(--text-xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.elite-sidebar-brand p {
  color: var(--text-muted);
  font-size: var(--text-sm);
  margin: var(--space-xs) 0 0 0;
}

.elite-nav-section {
  margin-bottom: var(--space-lg);
}

.elite-nav-section h3 {
  color: var(--text-muted);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  margin: 0 0 var(--space-md) 0;
}

.elite-nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: all 150ms var(--ease-out);
  margin-bottom: var(--space-xs);
  border: 1px solid transparent;
  position: relative;
}

.elite-nav-item:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
  text-decoration: none;
}

.elite-nav-item.active {
  background: rgba(74, 158, 255, 0.1);
  color: var(--accent-primary);
  border-color: rgba(74, 158, 255, 0.2);
}

.elite-nav-item.active::after {
  content: '';
  position: absolute;
  right: var(--space-md);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--accent-primary);
}

.elite-nav-icon {
  width: 18px;
  height: 18px;
  margin-right: var(--space-md);
  transition: color 150ms var(--ease-out);
}

.elite-nav-item:hover .elite-nav-icon,
.elite-nav-item.active .elite-nav-icon {
  color: var(--accent-primary);
}

.elite-user-profile {
  margin-top: auto;
  padding: var(--space-lg);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
}

.elite-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: var(--space-md);
}

.elite-user-info h4 {
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 600;
  margin: 0;
}

.elite-user-info p {
  color: var(--text-muted);
  font-size: var(--text-xs);
  margin: 0;
}

/* ===== HEADER COMPONENTS ===== */
.elite-header {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  margin: var(--space-lg);
  margin-left: 320px; /* Account for sidebar width + gap */
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-soft);
  animation: slideUp 400ms var(--ease-out);
}

.elite-header-info h1 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.elite-header-info p {
  color: var(--text-muted);
  font-size: var(--text-sm);
  margin: var(--space-xs) 0 0 0;
}

.elite-header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.elite-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.elite-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--positive);
  animation: pulse 2s ease-in-out infinite;
}

.elite-action-button {
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  border: 1px solid transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 150ms var(--ease-out);
  position: relative;
}

.elite-action-button:hover {
  color: var(--accent-primary);
  background: var(--bg-hover);
  transform: translateY(-1px);
}

.elite-notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent-secondary);
  color: white;
  font-size: 10px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(16px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse-update {
  0% { box-shadow: var(--shadow-card); }
  50% { box-shadow: var(--shadow-glow), var(--shadow-elevated); }
  100% { box-shadow: var(--shadow-card); }
}

@keyframes glow-pulse {
  0%, 100% { box-shadow: 0 0 5px rgba(74, 158, 255, 0.3); }
  50% { box-shadow: 0 0 20px rgba(74, 158, 255, 0.6); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 300ms var(--ease-out);
}

.animate-slide-up {
  animation: slideUp 400ms var(--ease-out);
}

.elite-glow {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* ===== ENHANCED GRID SYSTEMS ===== */
.elite-main-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background: var(--bg-primary);
}

.elite-content-area {
  padding: var(--space-lg);
  overflow-x: hidden;
}

.elite-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-lg);
}

.elite-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.elite-chart-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

/* ===== ENHANCED METRIC COMPONENTS ===== */
.elite-metric-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-card);
  position: relative;
  overflow: hidden;
}

.elite-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0;
  transition: opacity 200ms var(--ease-out);
}

.elite-metric-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--accent-primary);
}

.elite-metric-card:hover::before {
  opacity: 1;
}

.elite-metric-large {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  font-family: 'JetBrains Mono', monospace;
  margin-bottom: var(--space-sm);
  font-variant-numeric: tabular-nums;
}

.elite-metric-medium {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
  font-family: 'JetBrains Mono', monospace;
  font-variant-numeric: tabular-nums;
}

.elite-metric-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.elite-metric-change {
  font-size: var(--text-sm);
  font-weight: 600;
  margin-top: var(--space-xs);
}

/* ===== ENHANCED CHART CONTAINERS ===== */
.elite-chart-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  min-height: 400px;
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-card);
  position: relative;
}

.elite-chart-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: rgba(74, 158, 255, 0.3);
}

.elite-chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-primary);
}

.elite-chart-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.elite-chart-controls {
  display: flex;
  gap: var(--space-sm);
}

.elite-chart-control-btn {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: all 150ms var(--ease-out);
}

.elite-chart-control-btn:hover,
.elite-chart-control-btn.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* ===== UTILITY CLASSES ===== */

/* Text Utilities */
.text-elite-primary { color: var(--text-primary); }
.text-elite-secondary { color: var(--text-secondary); }
.text-elite-muted { color: var(--text-muted); }
.text-elite-accent { color: var(--accent-primary); }

/* Background Utilities */
.bg-elite-primary { background-color: var(--bg-primary); }
.bg-elite-secondary { background-color: var(--bg-secondary); }
.bg-elite-tertiary { background-color: var(--bg-tertiary); }
.bg-elite-card { background-color: var(--bg-secondary); }

/* Border Utilities */
.border-elite-primary { border-color: var(--border-primary); }
.border-elite-secondary { border-color: var(--border-secondary); }
.border-elite-accent { border-color: var(--accent-primary); }
.border-elite-glow { border: 1px solid var(--accent-primary); box-shadow: var(--shadow-glow); }

/* Shadow Utilities */
.shadow-elite-soft { box-shadow: var(--shadow-soft); }
.shadow-elite-card { box-shadow: var(--shadow-card); }
.shadow-elite-elevated { box-shadow: var(--shadow-elevated); }
.shadow-elite-glow { box-shadow: var(--shadow-glow); }

/* Border Radius Utilities */
.rounded-elite-sm { border-radius: var(--radius-sm); }
.rounded-elite-md { border-radius: var(--radius-md); }
.rounded-elite-lg { border-radius: var(--radius-lg); }

/* Spacing Utilities */
.p-elite-xs { padding: var(--space-xs); }
.p-elite-sm { padding: var(--space-sm); }
.p-elite-md { padding: var(--space-md); }
.p-elite-lg { padding: var(--space-lg); }
.p-elite-xl { padding: var(--space-xl); }

.m-elite-xs { margin: var(--space-xs); }
.m-elite-sm { margin: var(--space-sm); }
.m-elite-md { margin: var(--space-md); }
.m-elite-lg { margin: var(--space-lg); }
.m-elite-xl { margin: var(--space-xl); }

/* Flexbox Utilities */
.flex-elite-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-elite-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-elite-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-elite-column {
  display: flex;
  flex-direction: column;
}

/* Interactive Utilities */
.hover-elite-lift:hover {
  transform: translateY(-2px);
  transition: transform 150ms var(--ease-out);
}

.hover-elite-glow:hover {
  box-shadow: var(--shadow-glow);
  transition: box-shadow 150ms var(--ease-out);
}

.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }

/* Status Utilities */
.status-elite-positive { color: var(--positive); }
.status-elite-negative { color: var(--negative); }
.status-elite-neutral { color: var(--neutral); }

.bg-status-positive { background-color: rgba(16, 185, 129, 0.1); }
.bg-status-negative { background-color: rgba(239, 68, 68, 0.1); }
.bg-status-neutral { background-color: rgba(107, 114, 128, 0.1); }

/* ===== BOOTSTRAP COMPONENT OVERRIDES ===== */

/* Dark theme overrides for Bootstrap components */
.dash-bootstrap .card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
}

.dash-bootstrap .card-header {
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.dash-bootstrap .card-body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.dash-bootstrap .form-control {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: var(--radius-sm);
}

.dash-bootstrap .form-control:focus {
  background-color: var(--bg-elevated);
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

/* ===== CUSTOM DASHBOARD COMPONENTS ===== */

/* Metric Cards */
.metric-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-card);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--accent-primary);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  font-family: 'JetBrains Mono', monospace;
  margin-bottom: var(--space-sm);
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-positive { color: var(--positive); }
.metric-negative { color: var(--negative); }
.metric-neutral { color: var(--neutral); }

/* ===== PLOTLY CHART THEMING ===== */

/* Chart container styling */
.chart-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  min-height: 300px;
  transition: all 200ms var(--ease-out);
  box-shadow: var(--shadow-card);
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--accent-primary);
}

/* Enhanced Plotly chart styling - matching dark theme dashboard */
.js-plotly-plot .plotly {
  background: transparent !important;
}

.js-plotly-plot .main-svg {
  background: transparent !important;
}

/* Chart background and paper */
.js-plotly-plot .bg {
  fill: transparent !important;
}

.js-plotly-plot .paper {
  fill: transparent !important;
}

/* Chart text styling - enhanced typography */
.js-plotly-plot .gtitle {
  fill: var(--text-primary) !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
  font-weight: 600 !important;
}

.js-plotly-plot .xtick text,
.js-plotly-plot .ytick text {
  fill: var(--text-muted) !important;
  font-family: 'JetBrains Mono', 'Consolas', monospace !important;
  font-size: 12px !important;
}

/* Grid lines - subtle and refined */
.js-plotly-plot .xgrid,
.js-plotly-plot .ygrid {
  stroke: var(--border-primary) !important;
  stroke-width: 0.5 !important;
  stroke-dasharray: 3,3 !important;
}

/* Axis lines */
.js-plotly-plot .xaxis .axisline,
.js-plotly-plot .yaxis .axisline {
  stroke: var(--text-muted) !important;
  stroke-width: 1 !important;
}

/* Tick lines */
.js-plotly-plot .xtick line,
.js-plotly-plot .ytick line {
  stroke: var(--text-muted) !important;
  stroke-width: 1 !important;
}

/* Trace styling - enhanced colors */
.js-plotly-plot .trace {
  stroke: var(--accent-primary) !important;
}

.js-plotly-plot .trace.scatter .point {
  fill: var(--accent-primary) !important;
  stroke: var(--bg-secondary) !important;
  stroke-width: 2 !important;
}

/* Legend styling */
.js-plotly-plot .legendtext {
  fill: var(--text-secondary) !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 12px !important;
}

.js-plotly-plot .legend {
  fill: transparent !important;
}

/* Hover and tooltip styling */
.js-plotly-plot .hoverlayer .hovertext {
  fill: var(--text-primary) !important;
  stroke: var(--border-primary) !important;
  font-family: 'Inter', sans-serif !important;
}

.js-plotly-plot .hoverlayer .hovertext .nums {
  fill: var(--text-primary) !important;
}

/* Crossfilter brush styling */
.js-plotly-plot .select-outline {
  stroke: var(--accent-primary) !important;
  stroke-width: 1 !important;
  fill: rgba(74, 158, 255, 0.1) !important;
}

/* Rangeslider styling */
.js-plotly-plot .rangeslider-bg {
  fill: var(--bg-tertiary) !important;
}

.js-plotly-plot .rangeslider-mask {
  fill: rgba(0, 0, 0, 0.2) !important;
}

/* Modebar (toolbar) styling */
.js-plotly-plot .modebar {
  background: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-sm) !important;
}

.js-plotly-plot .modebar-btn {
  fill: var(--text-muted) !important;
}

.js-plotly-plot .modebar-btn:hover {
  fill: var(--text-primary) !important;
  background: var(--bg-hover) !important;
}

.js-plotly-plot .modebar-btn.active {
  fill: var(--accent-primary) !important;
  background: rgba(74, 158, 255, 0.1) !important;
}

/* ===== ACCESSIBILITY ===== */
.elite-focus-visible:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .grid-dashboard {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .elite-control-panel .card-body {
    padding: var(--space-md);
  }
  
  .grid-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  :root {
    --space-lg: 1rem;
    --space-xl: 1.5rem;
  }
  
  .metric-value {
    font-size: 2rem;
  }
  
  .heading-primary {
    font-size: var(--text-lg);
  }
}