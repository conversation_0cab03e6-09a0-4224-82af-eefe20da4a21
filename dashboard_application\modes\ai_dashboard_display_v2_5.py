"""
AI Intelligence Hub Dashboard for EOTS v2.5 - Compatibility Layer
================================================================

This is a compatibility layer that imports from the new modular AI dashboard structure.
The actual implementation has been refactored into separate modules for better maintainability:

- ai_dashboard/components.py: UI components and styling
- ai_dashboard/visualizations.py: Charts and graphs
- ai_dashboard/intelligence.py: AI analysis and insights
- ai_dashboard/layouts.py: Panel assembly and layout management
- ai_dashboard/utils.py: Utility functions and helpers
- ai_dashboard/ai_dashboard_display_v2_5.py: Main entry point

This file maintains backward compatibility with existing imports while using the new modular structure.

Author: EOTS v2.5 Development Team
Version: 2.5.0 (Refactored with Compatibility Layer)
"""

# Import the main create_layout function from the modular structure
from .ai_dashboard.ai_dashboard_display_v2_5 import create_layout

# Import all the individual component functions for backward compatibility
from .ai_dashboard.layouts import (
    create_unified_ai_intelligence_hub,
    create_ai_recommendations_panel,
    create_ai_regime_context_panel
)

from .ai_dashboard.ai_dashboard_display_v2_5 import (
    create_ai_performance_panel,
    create_apex_predator_brain,
    create_ai_metrics_dashboard,
    create_ai_learning_center,
    create_ai_system_status_bar
)

from .ai_dashboard.components import (
    create_placeholder_card,
    create_enhanced_confidence_meter,
    create_recommendation_confidence_bar,
    create_enhanced_recommendation_item,
    create_enhanced_insight_item,
    create_quick_action_buttons,
    create_regime_transition_indicator,
    get_unified_badge_style,
    get_unified_card_style,
    get_unified_text_style,
    AI_COLORS,
    AI_TYPOGRAPHY,
    AI_SPACING,
    AI_EFFECTS
)

from .ai_dashboard.visualizations import (
    create_market_state_visualization,
    create_enhanced_market_state_visualization,
    create_confidence_meter,
    create_confluence_gauge,
    create_ai_performance_chart,
    create_pure_metrics_visualization,
    create_comprehensive_metrics_chart,
    get_unified_chart_layout,
    get_unified_bar_trace_config,
    get_unified_line_trace_config
)

from .ai_dashboard.intelligence import (
    generate_unified_ai_insights,
    generate_ai_market_insights,
    generate_enhanced_ai_market_insights,
    calculate_ai_confidence,
    calculate_enhanced_ai_confidence,
    calculate_system_health_score,
    calculate_recommendation_confidence,
    generate_enhanced_ai_recommendations,
    analyze_enhanced_regime_with_ai,
    calculate_enhanced_regime_confidence,
    calculate_regime_transition_probability,
    get_real_system_health_status
)

from .ai_dashboard.utils import (
    generate_ai_performance_data,
    get_real_historical_performance,
    generate_realistic_performance_data,
    get_fallback_performance_data,
    get_real_ai_learning_stats,
    get_database_learning_stats,
    generate_realistic_learning_stats,
    get_fallback_learning_stats,
    get_consolidated_intelligence_data,
    get_real_mcp_status,
    calculate_overall_intelligence_score,
    calculate_learning_velocity,
    calculate_pattern_diversity
)

# Additional utility functions for backward compatibility
def calculate_metric_confluence_score(metrics):
    """Backward compatibility wrapper for metric confluence calculation."""
    from .ai_dashboard.layouts import calculate_metric_confluence_score
    return calculate_metric_confluence_score(metrics)

def assess_signal_strength(metrics):
    """Backward compatibility wrapper for signal strength assessment."""
    from .ai_dashboard.layouts import assess_signal_strength
    return assess_signal_strength(metrics)

# Module information
__version__ = "2.5.0"
__author__ = "EOTS v2.5 Development Team"
__description__ = "AI Intelligence Hub Dashboard - Refactored Modular Architecture"

# Logging setup
import logging
logger = logging.getLogger(__name__)
logger.info("🧠 AI Dashboard v2.5 - Modular architecture loaded successfully")
logger.info(f"📦 Modules: components, visualizations, intelligence, layouts, utils")
logger.info(f"✅ Pydantic-first architecture: ENABLED")
logger.info(f"🔧 Backward compatibility: MAINTAINED")