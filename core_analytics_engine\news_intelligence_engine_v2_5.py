"""
News Intelligence Engine v2.5 for EOTS "Apex Predator"
======================================================

The diabolical AI-powered news analysis engine that transforms raw sentiment data
into actionable market intelligence. This engine cross-references Alpha Vantage
sentiment analysis with EOTS v2.5 metrics to generate sophisticated market narratives.

Key Features:
- Pydantic AI-powered sentiment interpretation
- Cross-correlation with EOTS v2.5 flow metrics
- Intelligent narrative generation
- Market regime contextualization
- Contrarian signal detection
- Confluence analysis

Author: EOTS v2.5 Development Team - "Apex Predator" Division
Version: 2.5.0 - "Diabolical Intelligence"
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
from pydantic import BaseModel, Field

# Import EOTS components
from data_management.alpha_vantage_fetcher_v2_5 import AlphaVantageDataFetcherV2_5, SentimentData
from data_models.eots_schemas_v2_5 import ProcessedDataBundleV2_5

logger = logging.getLogger(__name__)

class MarketSentimentRegime(Enum):
    """Market sentiment regime classifications."""
    EXTREME_BULLISH = "EXTREME_BULLISH"
    BULLISH = "BULLISH"
    NEUTRAL_POSITIVE = "NEUTRAL_POSITIVE"
    NEUTRAL = "NEUTRAL"
    NEUTRAL_NEGATIVE = "NEUTRAL_NEGATIVE"
    BEARISH = "BEARISH"
    EXTREME_BEARISH = "EXTREME_BEARISH"

class NewsSignalType(Enum):
    """Types of news-driven signals."""
    CONTRARIAN_SETUP = "CONTRARIAN_SETUP"
    CONFLUENCE_CRUSHER = "CONFLUENCE_CRUSHER"
    TRAP_DETECTOR = "TRAP_DETECTOR"
    MOMENTUM_ACCELERATOR = "MOMENTUM_ACCELERATOR"
    VOLATILITY_CATALYST = "VOLATILITY_CATALYST"
    REGIME_SHIFT = "REGIME_SHIFT"

@dataclass
class NewsIntelligenceSignal:
    """Structured news intelligence signal."""
    signal_type: NewsSignalType
    confidence: float
    narrative: str
    supporting_evidence: List[str]
    eots_confluence: Dict[str, float]
    sentiment_context: Dict[str, Any]
    timestamp: datetime

class NewsIntelligenceEngineV2_5:
    """
    The Diabolical News Intelligence Engine
    
    This AI-powered engine analyzes market sentiment and cross-references it with
    EOTS v2.5 metrics to generate sophisticated market intelligence and trading narratives.
    """
    
    def __init__(self, config_manager, alpha_vantage_fetcher: Optional[AlphaVantageDataFetcherV2_5] = None):
        """
        Initialize the News Intelligence Engine.
        
        Args:
            config_manager: EOTS v2.5 configuration manager
            alpha_vantage_fetcher: Alpha Vantage data fetcher instance
        """
        self.config_manager = config_manager
        self.alpha_vantage_fetcher = alpha_vantage_fetcher
        
        # Intelligence thresholds
        self.sentiment_thresholds = {
            'extreme_bullish': 0.4,
            'bullish': 0.15,
            'neutral_positive': 0.05,
            'neutral_negative': -0.05,
            'bearish': -0.15,
            'extreme_bearish': -0.4
        }
        
        # EOTS metric significance thresholds
        self.eots_thresholds = {
            'vapi_fa_extreme': 2.5,
            'dwfd_extreme': 2.0,
            'tw_laf_extreme': 1.8,
            'a_dag_extreme': 100000,
            'vri_2_0_extreme': 10000
        }
        
        logger.info("🧠 News Intelligence Engine v2.5 'Diabolical Intelligence' initialized")
    
    def analyze_market_intelligence(self, 
                                  processed_data: ProcessedDataBundleV2_5,
                                  symbol: str = "SPY") -> Dict[str, Any]:
        """
        Generate comprehensive market intelligence by analyzing sentiment and EOTS metrics.
        
        Args:
            processed_data: EOTS v2.5 processed data bundle
            symbol: Ticker symbol to analyze
            
        Returns:
            Dict containing comprehensive market intelligence analysis
        """
        try:
            logger.info(f"🎭 Generating diabolical market intelligence for {symbol}...")
            
            # Get Alpha Vantage sentiment data
            sentiment_intelligence = self._fetch_sentiment_intelligence(symbol)
            
            # Extract EOTS metrics
            eots_metrics = self._extract_eots_metrics(processed_data)
            
            # Classify sentiment regime
            sentiment_regime = self._classify_sentiment_regime(sentiment_intelligence)
            
            # Detect market signals
            intelligence_signals = self._detect_intelligence_signals(
                sentiment_intelligence, eots_metrics, sentiment_regime
            )
            
            # Generate market narratives
            market_narratives = self._generate_market_narratives(
                intelligence_signals, sentiment_intelligence, eots_metrics
            )
            
            # Calculate overall intelligence score
            intelligence_score = self._calculate_intelligence_score(
                sentiment_intelligence, eots_metrics, intelligence_signals
            )
            
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'intelligence_score': intelligence_score,
                'sentiment_regime': sentiment_regime.value,
                'sentiment_data': sentiment_intelligence,
                'eots_metrics_summary': eots_metrics,
                'intelligence_signals': [
                    {
                        'type': signal.signal_type.value,
                        'confidence': signal.confidence,
                        'narrative': signal.narrative,
                        'evidence': signal.supporting_evidence
                    }
                    for signal in intelligence_signals
                ],
                'market_narratives': market_narratives,
                'diabolical_insights': self._generate_diabolical_insights(
                    intelligence_signals, sentiment_regime, eots_metrics
                )
            }
            
        except Exception as e:
            logger.error(f"Error in market intelligence analysis: {str(e)}")
            return self._get_fallback_intelligence(symbol)
    
    def _fetch_sentiment_intelligence(self, symbol: str) -> Dict[str, Any]:
        """Fetch and process sentiment intelligence from Alpha Vantage."""
        try:
            if not self.alpha_vantage_fetcher:
                return {}
            
            # Get comprehensive sentiment data
            comprehensive_intel = self.alpha_vantage_fetcher.get_comprehensive_intelligence_summary(symbol)
            
            if comprehensive_intel and comprehensive_intel.get('alpha_intelligence_active'):
                return comprehensive_intel.get('sentiment', {})
            
            return {}
            
        except Exception as e:
            logger.error(f"Error fetching sentiment intelligence: {str(e)}")
            return {}
    
    def _extract_eots_metrics(self, processed_data: ProcessedDataBundleV2_5) -> Dict[str, float]:
        """Extract key EOTS v2.5 metrics for intelligence analysis."""
        try:
            metrics = processed_data.metrics_v2_5 if hasattr(processed_data, 'metrics_v2_5') else {}
            
            # Extract Enhanced Flow Metrics (Tier 3)
            eots_summary = {
                'vapi_fa_z': metrics.get('vapi_fa_z_score_und', 0.0),
                'vapi_fa_raw': metrics.get('vapi_fa_raw_und', 0.0),
                'dwfd_z': metrics.get('dwfd_z_score_und', 0.0),
                'dwfd_raw': metrics.get('dwfd_raw_und', 0.0),
                'tw_laf_z': metrics.get('tw_laf_z_score_und', 0.0),
                'tw_laf_raw': metrics.get('tw_laf_raw_und', 0.0)
            }
            
            # Extract Adaptive Metrics (Tier 2) from strike data if available
            strike_data = getattr(processed_data, 'strike_data_v2_5', {})
            if isinstance(strike_data, dict) and 'data' in strike_data:
                strike_df = strike_data['data']
                if not strike_df.empty:
                    eots_summary.update({
                        'a_dag_total': abs(strike_df.get('a_dag_strike', pd.Series([0])).sum()),
                        'vri_2_0_avg': abs(strike_df.get('vri_2_0_strike', pd.Series([0])).mean())
                    })
            
            return eots_summary
            
        except Exception as e:
            logger.error(f"Error extracting EOTS metrics: {str(e)}")
            return {}
    
    def _classify_sentiment_regime(self, sentiment_data: Dict[str, Any]) -> MarketSentimentRegime:
        """Classify the current sentiment regime."""
        try:
            sentiment_score = sentiment_data.get('score', 0.0)
            
            if sentiment_score >= self.sentiment_thresholds['extreme_bullish']:
                return MarketSentimentRegime.EXTREME_BULLISH
            elif sentiment_score >= self.sentiment_thresholds['bullish']:
                return MarketSentimentRegime.BULLISH
            elif sentiment_score >= self.sentiment_thresholds['neutral_positive']:
                return MarketSentimentRegime.NEUTRAL_POSITIVE
            elif sentiment_score >= self.sentiment_thresholds['neutral_negative']:
                return MarketSentimentRegime.NEUTRAL
            elif sentiment_score >= self.sentiment_thresholds['bearish']:
                return MarketSentimentRegime.NEUTRAL_NEGATIVE
            elif sentiment_score >= self.sentiment_thresholds['extreme_bearish']:
                return MarketSentimentRegime.BEARISH
            else:
                return MarketSentimentRegime.EXTREME_BEARISH
                
        except Exception as e:
            logger.error(f"Error classifying sentiment regime: {str(e)}")
            return MarketSentimentRegime.NEUTRAL
    
    def _detect_intelligence_signals(self, 
                                   sentiment_data: Dict[str, Any],
                                   eots_metrics: Dict[str, float],
                                   sentiment_regime: MarketSentimentRegime) -> List[NewsIntelligenceSignal]:
        """Detect sophisticated intelligence signals by cross-referencing sentiment and EOTS data."""
        signals = []
        
        try:
            sentiment_score = sentiment_data.get('score', 0.0)
            vapi_fa_z = eots_metrics.get('vapi_fa_z', 0.0)
            dwfd_z = eots_metrics.get('dwfd_z', 0.0)
            tw_laf_z = eots_metrics.get('tw_laf_z', 0.0)
            
            # CONTRARIAN SETUP: Negative sentiment + Positive EOTS flow
            if (sentiment_score < -0.2 and dwfd_z > 1.5) or (sentiment_score > 0.2 and dwfd_z < -1.5):
                confidence = min(abs(sentiment_score) * 2 + abs(dwfd_z) * 0.3, 0.95)
                direction = "bullish" if dwfd_z > 0 else "bearish"
                
                signals.append(NewsIntelligenceSignal(
                    signal_type=NewsSignalType.CONTRARIAN_SETUP,
                    confidence=confidence,
                    narrative=f"🎯 CONTRARIAN SETUP: {direction.title()} smart money flow ({dwfd_z:.2f}σ) "
                             f"against {sentiment_regime.value.lower()} sentiment ({sentiment_score:.3f})",
                    supporting_evidence=[
                        f"DWFD Z-score: {dwfd_z:.2f}σ indicates smart money {direction} positioning",
                        f"Sentiment score: {sentiment_score:.3f} shows market {sentiment_regime.value.lower()}",
                        f"Contrarian opportunity: Smart money vs crowd sentiment divergence"
                    ],
                    eots_confluence={'dwfd_z': dwfd_z, 'sentiment_divergence': abs(sentiment_score)},
                    sentiment_context=sentiment_data,
                    timestamp=datetime.now()
                ))
            
            # CONFLUENCE CRUSHER: Strong sentiment + Strong EOTS flow in same direction
            if (sentiment_score > 0.3 and vapi_fa_z > 2.0) or (sentiment_score < -0.3 and vapi_fa_z < -2.0):
                confidence = min(abs(sentiment_score) * 1.5 + abs(vapi_fa_z) * 0.4, 0.98)
                direction = "bullish" if sentiment_score > 0 else "bearish"
                
                signals.append(NewsIntelligenceSignal(
                    signal_type=NewsSignalType.CONFLUENCE_CRUSHER,
                    confidence=confidence,
                    narrative=f"🔥 CONFLUENCE CRUSHER: Extreme {direction} sentiment + VAPI-FA acceleration "
                             f"({vapi_fa_z:.2f}σ) = EXPLOSIVE MOVE POTENTIAL!",
                    supporting_evidence=[
                        f"Sentiment alignment: {sentiment_score:.3f} shows strong {direction} bias",
                        f"VAPI-FA extreme: {vapi_fa_z:.2f}σ indicates premium flow acceleration",
                        f"Perfect confluence: News sentiment + options flow = high conviction setup"
                    ],
                    eots_confluence={'vapi_fa_z': vapi_fa_z, 'sentiment_alignment': sentiment_score},
                    sentiment_context=sentiment_data,
                    timestamp=datetime.now()
                ))
            
            # TRAP DETECTOR: Extreme sentiment without EOTS confirmation
            if abs(sentiment_score) > 0.4 and max(abs(vapi_fa_z), abs(dwfd_z), abs(tw_laf_z)) < 1.0:
                confidence = abs(sentiment_score) * 0.8
                trap_type = "BULL" if sentiment_score > 0 else "BEAR"

                signals.append(NewsIntelligenceSignal(
                    signal_type=NewsSignalType.TRAP_DETECTOR,
                    confidence=confidence,
                    narrative=f"⚠️ {trap_type} TRAP ALERT: Extreme sentiment ({sentiment_score:.3f}) "
                             f"without EOTS flow confirmation - potential fake move!",
                    supporting_evidence=[
                        f"Extreme sentiment: {sentiment_score:.3f} shows {trap_type.lower()} euphoria/panic",
                        f"Weak EOTS signals: No significant flow confirmation",
                        f"Trap potential: News-driven moves without smart money participation often reverse"
                    ],
                    eots_confluence={'sentiment_extreme': abs(sentiment_score), 'eots_weakness': max(abs(vapi_fa_z), abs(dwfd_z))},
                    sentiment_context=sentiment_data,
                    timestamp=datetime.now()
                ))

            return signals

        except Exception as e:
            logger.error(f"Error detecting intelligence signals: {str(e)}")
            return []

    def _generate_market_narratives(self,
                                  signals: List[NewsIntelligenceSignal],
                                  sentiment_data: Dict[str, Any],
                                  eots_metrics: Dict[str, float]) -> List[str]:
        """Generate sophisticated market narratives based on intelligence analysis."""
        narratives = []

        try:
            # Primary narrative based on strongest signal
            if signals:
                strongest_signal = max(signals, key=lambda s: s.confidence)
                narratives.append(f"🎭 PRIMARY INTELLIGENCE: {strongest_signal.narrative}")

            # Secondary narratives based on metric confluence
            strong_eots_signals = sum([
                abs(eots_metrics.get('vapi_fa_z', 0)) > 1.5,
                abs(eots_metrics.get('dwfd_z', 0)) > 1.5,
                abs(eots_metrics.get('tw_laf_z', 0)) > 1.2
            ])

            if strong_eots_signals >= 2:
                narratives.append(f"⚡ EOTS CONFLUENCE: {strong_eots_signals}/3 advanced metrics showing extreme readings")

            # Sentiment context narrative
            sentiment_score = sentiment_data.get('score', 0.0)
            article_count = sentiment_data.get('article_count', 0)

            if abs(sentiment_score) > 0.2 and article_count > 20:
                sentiment_intensity = "EXTREME" if abs(sentiment_score) > 0.4 else "STRONG"
                direction = "BULLISH" if sentiment_score > 0 else "BEARISH"
                narratives.append(f"📰 NEWS INTELLIGENCE: {sentiment_intensity} {direction} sentiment "
                                f"across {article_count} articles driving market attention")

            return narratives[:4]  # Limit to 4 narratives

        except Exception as e:
            logger.error(f"Error generating market narratives: {str(e)}")
            return ["🤖 Market narrative generation temporarily unavailable"]

    def _calculate_intelligence_score(self,
                                    sentiment_data: Dict[str, Any],
                                    eots_metrics: Dict[str, float],
                                    signals: List[NewsIntelligenceSignal]) -> float:
        """Calculate overall intelligence confidence score."""
        try:
            score_components = []

            # Sentiment confidence component
            sentiment_confidence = sentiment_data.get('confidence', 50.0) / 100.0
            score_components.append(sentiment_confidence * 0.3)

            # EOTS signal strength component
            max_eots_z = max([
                abs(eots_metrics.get('vapi_fa_z', 0)),
                abs(eots_metrics.get('dwfd_z', 0)),
                abs(eots_metrics.get('tw_laf_z', 0))
            ])
            eots_strength = min(max_eots_z / 3.0, 1.0)
            score_components.append(eots_strength * 0.4)

            # Signal confluence component
            if signals:
                avg_signal_confidence = sum(s.confidence for s in signals) / len(signals)
                score_components.append(avg_signal_confidence * 0.3)
            else:
                score_components.append(0.5 * 0.3)  # Neutral if no signals

            return min(sum(score_components), 0.95)

        except Exception as e:
            logger.error(f"Error calculating intelligence score: {str(e)}")
            return 0.5

    def _generate_diabolical_insights(self,
                                    signals: List[NewsIntelligenceSignal],
                                    sentiment_regime: MarketSentimentRegime,
                                    eots_metrics: Dict[str, float]) -> List[str]:
        """Generate the most diabolical and sophisticated market insights."""
        insights = []

        try:
            # Diabolical signal insights
            for signal in signals[:2]:  # Top 2 signals
                if signal.signal_type == NewsSignalType.CONTRARIAN_SETUP:
                    insights.append(f"😈 DIABOLICAL CONTRARIAN: While the crowd panics/celebrates, "
                                  f"smart money is quietly positioning for the opposite move "
                                  f"(Confidence: {signal.confidence:.1%})")

                elif signal.signal_type == NewsSignalType.CONFLUENCE_CRUSHER:
                    insights.append(f"🔥 DIABOLICAL CONFLUENCE: Perfect storm detected - "
                                  f"news sentiment and options flow aligned for devastating move "
                                  f"(Confidence: {signal.confidence:.1%})")

                elif signal.signal_type == NewsSignalType.TRAP_DETECTOR:
                    insights.append(f"🕷️ DIABOLICAL TRAP: Extreme sentiment without smart money confirmation - "
                                  f"the crowd is being led to slaughter (Confidence: {signal.confidence:.1%})")

            # Regime-based diabolical insights
            if sentiment_regime in [MarketSentimentRegime.EXTREME_BULLISH, MarketSentimentRegime.EXTREME_BEARISH]:
                insights.append(f"⚠️ DIABOLICAL WARNING: Extreme sentiment ({sentiment_regime.value}) "
                              f"often marks reversals - watch for smart money divergence")

            # EOTS-based diabolical insights
            vapi_fa_z = abs(eots_metrics.get('vapi_fa_z', 0))
            dwfd_z = abs(eots_metrics.get('dwfd_z', 0))

            if vapi_fa_z > 2.5 and dwfd_z > 2.0:
                insights.append(f"💀 DIABOLICAL SETUP: VAPI-FA ({vapi_fa_z:.1f}σ) + DWFD ({dwfd_z:.1f}σ) "
                              f"extreme readings suggest institutional positioning for major move")

            # Market timing diabolical insight
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 10 and signals:
                insights.append(f"🌅 DIABOLICAL TIMING: Opening hour + intelligence signals = "
                              f"Perfect execution window for apex predators")
            elif 15 <= current_hour <= 16 and signals:
                insights.append(f"🌆 DIABOLICAL TIMING: Power hour + intelligence confluence = "
                              f"Institutional positioning for overnight/next day moves")

            return insights[:4]  # Limit to 4 diabolical insights

        except Exception as e:
            logger.error(f"Error generating diabolical insights: {str(e)}")
            return ["😈 Diabolical insights temporarily unavailable - but the predator still hunts..."]

    def _get_fallback_intelligence(self, symbol: str) -> Dict[str, Any]:
        """Fallback intelligence when systems are unavailable."""
        return {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'intelligence_score': 0.5,
            'sentiment_regime': MarketSentimentRegime.NEUTRAL.value,
            'sentiment_data': {},
            'eots_metrics_summary': {},
            'intelligence_signals': [],
            'market_narratives': [f"🔍 Intelligence systems temporarily offline for {symbol}"],
            'diabolical_insights': ["😈 The apex predator rests, but will return stronger..."]
        }

    def get_real_time_intelligence_summary(self,
                                         processed_data: ProcessedDataBundleV2_5,
                                         symbol: str = "SPY") -> Dict[str, Any]:
        """
        Get real-time intelligence summary for dashboard display.

        This is the main interface for the dashboard to get processed intelligence.
        """
        try:
            logger.info(f"🎭 Generating real-time intelligence summary for {symbol}")

            # Get full intelligence analysis
            full_intelligence = self.analyze_market_intelligence(processed_data, symbol)

            # Extract key components for dashboard
            return {
                'intelligence_active': True,
                'intelligence_score': full_intelligence['intelligence_score'],
                'sentiment_regime': full_intelligence['sentiment_regime'],
                'primary_narrative': full_intelligence['market_narratives'][0] if full_intelligence['market_narratives'] else "No primary narrative",
                'top_signal': full_intelligence['intelligence_signals'][0] if full_intelligence['intelligence_signals'] else None,
                'diabolical_insight': full_intelligence['diabolical_insights'][0] if full_intelligence['diabolical_insights'] else "😈 Apex predator analyzing...",
                'confidence_level': f"{full_intelligence['intelligence_score']:.1%}",
                'timestamp': full_intelligence['timestamp']
            }

        except Exception as e:
            logger.error(f"Error generating real-time intelligence summary: {str(e)}")
            return {
                'intelligence_active': False,
                'intelligence_score': 0.5,
                'sentiment_regime': 'NEUTRAL',
                'primary_narrative': "Intelligence systems temporarily offline",
                'top_signal': None,
                'diabolical_insight': "😈 The predator rests...",
                'confidence_level': "50.0%",
                'timestamp': datetime.now().isoformat()
            }
