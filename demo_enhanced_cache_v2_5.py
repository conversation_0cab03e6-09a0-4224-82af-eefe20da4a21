#!/usr/bin/env python3
"""
Enhanced Cache System Demo v2.5
===============================

Demonstrates the capabilities of the new enhanced cache system:
- Multi-level caching (memory, disk, compressed)
- Dynamic TTL and cache analytics
- Performance improvements over legacy system
- Automatic migration from legacy cache
"""

import time
import random
import logging
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5, CacheLevel


def setup_logging():
    """Setup logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    return logging.getLogger(__name__)


def generate_sample_data(size: str = "small") -> list:
    """Generate sample metric data of different sizes."""
    if size == "small":
        return [random.uniform(-100, 100) for _ in range(10)]
    elif size == "medium":
        return [random.uniform(-1000, 1000) for _ in range(100)]
    elif size == "large":
        return [random.uniform(-10000, 10000) for _ in range(1000)]
    else:
        return [random.uniform(-100000, 100000) for _ in range(10000)]


def demo_basic_operations(cache_manager: EnhancedCacheManagerV2_5, logger):
    """Demonstrate basic cache operations."""
    logger.info("🔧 Demo: Basic Cache Operations")
    
    # Store data at different cache levels
    symbols = ["SPY", "QQQ", "NVDA", "/ES:XCME"]
    metrics = ["vapi_fa", "dwfd", "tw_laf", "rolling_flows"]
    
    for symbol in symbols:
        for metric in metrics:
            data = generate_sample_data("medium")
            
            # Determine cache level based on symbol (demo purposes)
            if symbol.startswith("/"):
                cache_level = CacheLevel.COMPRESSED  # Futures data compressed
            elif symbol in ["SPY", "QQQ"]:
                cache_level = CacheLevel.MEMORY  # High-frequency symbols in memory
            else:
                cache_level = CacheLevel.DISK  # Regular symbols on disk
            
            success = cache_manager.put(
                symbol=symbol,
                metric_name=metric,
                data=data,
                cache_level=cache_level,
                tags=[f"demo_{symbol}", "sample_data"]
            )
            
            if success:
                logger.info(f"   ✅ Stored {symbol}:{metric} at level {cache_level}")
            else:
                logger.error(f"   ❌ Failed to store {symbol}:{metric}")
    
    logger.info("   📊 Cache populated with sample data")


def demo_retrieval_performance(cache_manager: EnhancedCacheManagerV2_5, logger):
    """Demonstrate cache retrieval performance."""
    logger.info("⚡ Demo: Cache Retrieval Performance")
    
    symbols = ["SPY", "QQQ", "NVDA"]
    metrics = ["vapi_fa", "dwfd", "tw_laf"]
    
    # Measure retrieval times
    start_time = time.time()
    retrieved_count = 0
    
    for _ in range(100):  # 100 retrievals
        symbol = random.choice(symbols)
        metric = random.choice(metrics)
        
        data = cache_manager.get(symbol, metric)
        if data is not None:
            retrieved_count += 1
    
    end_time = time.time()
    avg_time_ms = ((end_time - start_time) / 100) * 1000
    
    logger.info(f"   📈 Retrieved {retrieved_count}/100 items")
    logger.info(f"   ⏱️  Average retrieval time: {avg_time_ms:.2f}ms")
    
    # Show cache statistics
    stats = cache_manager.get_cache_stats()
    logger.info(f"   📊 Hit ratio: {stats['hit_ratio']:.2%}")
    logger.info(f"   💾 Memory usage: {stats['memory_size_mb']:.2f}MB")


def demo_compression_benefits(cache_manager: EnhancedCacheManagerV2_5, logger):
    """Demonstrate compression benefits."""
    logger.info("🗜️  Demo: Compression Benefits")
    
    # Store large datasets with compression
    large_symbols = ["LARGE_DATASET_1", "LARGE_DATASET_2", "LARGE_DATASET_3"]
    
    for symbol in large_symbols:
        large_data = generate_sample_data("xlarge")
        
        # Store with compression
        success = cache_manager.put(
            symbol=symbol,
            metric_name="large_metric",
            data=large_data,
            cache_level=CacheLevel.COMPRESSED,
            tags=["compression_demo", "large_data"]
        )
        
        if success:
            logger.info(f"   ✅ Compressed and stored {symbol} ({len(large_data)} data points)")
    
    # Show compression statistics
    info = cache_manager.get_cache_info()
    logger.info(f"   📦 Total cache entries: {info['total_entries']}")


def demo_cache_analytics(cache_manager: EnhancedCacheManagerV2_5, logger):
    """Demonstrate cache analytics and monitoring."""
    logger.info("📊 Demo: Cache Analytics")
    
    # Get comprehensive cache statistics
    stats = cache_manager.get_cache_stats()
    
    logger.info("   Cache Performance Metrics:")
    logger.info(f"   • Cache Hits: {stats['hits']}")
    logger.info(f"   • Cache Misses: {stats['misses']}")
    logger.info(f"   • Hit Ratio: {stats['hit_ratio']:.2%}")
    logger.info(f"   • Memory Entries: {stats['memory_entries']}")
    logger.info(f"   • Memory Usage: {stats['memory_size_mb']:.2f}MB")
    logger.info(f"   • Total Entries: {stats['total_entries']}")
    logger.info(f"   • Evictions: {stats['evictions']}")
    
    # Symbol-specific analytics
    for symbol in ["SPY", "QQQ", "NVDA"]:
        symbol_info = cache_manager.get_cache_info(symbol=symbol)
        logger.info(f"   • {symbol}: {symbol_info['symbol_entries']} entries")


def demo_cache_management(cache_manager: EnhancedCacheManagerV2_5, logger):
    """Demonstrate cache management features."""
    logger.info("🧹 Demo: Cache Management")
    
    # Clear expired entries
    expired_count = cache_manager.clear_expired()
    logger.info(f"   🗑️  Cleared {expired_count} expired entries")
    
    # Show cache directory structure
    cache_root = cache_manager.cache_root
    if cache_root.exists():
        logger.info("   📁 Cache Directory Structure:")
        for item in cache_root.rglob("*"):
            if item.is_file():
                size_kb = item.stat().st_size / 1024
                logger.info(f"      • {item.relative_to(cache_root)}: {size_kb:.1f}KB")


def demo_futures_symbol_handling(cache_manager: EnhancedCacheManagerV2_5, logger):
    """Demonstrate proper handling of futures symbols like /ES:XCME."""
    logger.info("🔮 Demo: Futures Symbol Handling")
    
    futures_symbols = ["/ES:XCME", "/NQ:XCME", "/YM:XCME", "/RTY:XCME"]
    
    for symbol in futures_symbols:
        # Test symbol sanitization
        safe_symbol = cache_manager.sanitize_symbol_for_cache(symbol)
        logger.info(f"   🔧 {symbol} → {safe_symbol}")
        
        # Store and retrieve data
        test_data = generate_sample_data("small")
        success = cache_manager.put(
            symbol=symbol,
            metric_name="futures_test",
            data=test_data,
            cache_level=CacheLevel.COMPRESSED
        )
        
        if success:
            retrieved_data = cache_manager.get(symbol, "futures_test")
            if retrieved_data == test_data:
                logger.info(f"   ✅ {symbol}: Store/retrieve successful")
            else:
                logger.error(f"   ❌ {symbol}: Data integrity check failed")
        else:
            logger.error(f"   ❌ {symbol}: Storage failed")


def main():
    """Main demo function."""
    logger = setup_logging()
    logger.info("🚀 Enhanced Cache System Demo v2.5")
    logger.info("=" * 50)
    
    # Initialize enhanced cache manager
    cache_manager = EnhancedCacheManagerV2_5(
        cache_root="cache/demo_enhanced_v2_5",
        memory_limit_mb=50,
        disk_limit_mb=200,
        default_ttl_seconds=3600
    )
    
    try:
        # Run demonstrations
        demo_basic_operations(cache_manager, logger)
        print()
        
        demo_retrieval_performance(cache_manager, logger)
        print()
        
        demo_compression_benefits(cache_manager, logger)
        print()
        
        demo_cache_analytics(cache_manager, logger)
        print()
        
        demo_futures_symbol_handling(cache_manager, logger)
        print()
        
        demo_cache_management(cache_manager, logger)
        
        logger.info("=" * 50)
        logger.info("🎉 Enhanced Cache System Demo completed successfully!")
        logger.info("💡 Key Benefits:")
        logger.info("   • Multi-level caching with automatic promotion")
        logger.info("   • Compression for large datasets")
        logger.info("   • Thread-safe operations")
        logger.info("   • Dynamic TTL management")
        logger.info("   • Comprehensive analytics")
        logger.info("   • Proper futures symbol handling")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}", exc_info=True)


if __name__ == "__main__":
    main()
