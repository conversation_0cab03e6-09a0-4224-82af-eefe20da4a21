"""
Signal Generation Issues Fix v2.5
=================================

This script addresses the signal generation and ATIF strategy issues identified
in the EOTS v2.5 system logs:

1. Zero directional/volatility signals being generated
2. Signal classification defaulting to 'Complex'
3. ATIF high conviction but no strategy rules matched
4. Zero trade directives generated despite bullish bias

Author: EOTS v2.5 Development Team - "Signal Generation Fix Division"
Version: 2.5.0 - "SIGNAL GENERATION RESTORATION"
"""

import os
import sys
import logging
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_signal_generation_issues():
    """Analyze the signal generation configuration and identify issues."""
    logger.info("🔍 Analyzing signal generation issues...")
    
    issues_found = []
    
    # Check signal generator configuration
    try:
        from core_analytics_engine.signal_generator_v2_5 import SignalGeneratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config_manager = ConfigManagerV2_5()
        signal_generator = SignalGeneratorV2_5(config_manager)
        
        # Check signal thresholds
        signal_config = config_manager.config.signal_generation
        
        logger.info(f"📊 Current signal thresholds:")
        logger.info(f"   - Directional threshold: {getattr(signal_config, 'directional_threshold', 'NOT SET')}")
        logger.info(f"   - Volatility threshold: {getattr(signal_config, 'volatility_threshold', 'NOT SET')}")
        logger.info(f"   - Complex threshold: {getattr(signal_config, 'complex_threshold', 'NOT SET')}")
        
        # Check if thresholds are too high
        directional_threshold = getattr(signal_config, 'directional_threshold', 2.0)
        volatility_threshold = getattr(signal_config, 'volatility_threshold', 1.5)
        
        if directional_threshold > 1.5:
            issues_found.append(f"Directional threshold too high: {directional_threshold}")
        
        if volatility_threshold > 1.2:
            issues_found.append(f"Volatility threshold too high: {volatility_threshold}")
        
    except Exception as e:
        issues_found.append(f"Signal generator configuration error: {str(e)}")
    
    # Check ATIF strategy rules
    try:
        from core_analytics_engine.adaptive_trade_idea_framework_v2_5 import AdaptiveTradeIdeaFrameworkV2_5
        
        atif = AdaptiveTradeIdeaFrameworkV2_5(config_manager)
        
        # Check if strategy rules exist
        if not hasattr(atif, 'strategy_rules') or not atif.strategy_rules:
            issues_found.append("ATIF strategy rules not configured")
        
    except Exception as e:
        issues_found.append(f"ATIF configuration error: {str(e)}")
    
    return issues_found

def fix_signal_thresholds():
    """Fix signal generation thresholds to be more sensitive."""
    logger.info("🔧 Fixing signal generation thresholds...")

    try:
        # Read current config
        config_path = "config/config_v2_5.json"

        if not os.path.exists(config_path):
            logger.warning(f"Config file not found: {config_path}")
            return False
        
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Update signal generation settings in system_settings
        if 'signal_generator_settings_v2_5' not in config:
            config['signal_generator_settings_v2_5'] = {}

        # Set more sensitive thresholds
        config['signal_generator_settings_v2_5'].update({
            'directional_threshold': 1.0,  # Reduced from likely higher value
            'volatility_threshold': 0.8,   # Reduced from likely higher value
            'complex_threshold': 0.6,      # Reduced from likely higher value
            'min_confidence': 0.5,         # Minimum confidence for signals
            'enable_weak_signals': True,   # Enable weaker signals
            'signal_sensitivity': 'HIGH'   # Increase overall sensitivity
        })
        
        # Update signal classification keywords
        config['signal_generator_settings_v2_5']['signal_keywords'] = {
            'directional': [
                'bullish', 'bearish', 'uptrend', 'downtrend', 'momentum',
                'breakout', 'breakdown', 'reversal', 'continuation'
            ],
            'volatility': [
                'volatility', 'vol', 'expansion', 'compression', 'squeeze',
                'vix', 'implied_vol', 'realized_vol', 'vol_surface'
            ],
            'complex': [
                'structure', 'regime', 'correlation', 'divergence', 'warning',
                'unusual', 'anomaly', 'pattern', 'confluence'
            ]
        }
        
        # Write updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info("✅ Signal generation thresholds updated successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing signal thresholds: {str(e)}")
        return False

def fix_atif_strategy_rules():
    """Fix ATIF strategy rules to handle high conviction scenarios."""
    logger.info("🔧 Fixing ATIF strategy rules...")

    try:
        # Read current config
        config_path = "config/config_v2_5.json"

        if not os.path.exists(config_path):
            logger.warning(f"Config file not found: {config_path}")
            return False
        
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Add ATIF strategy rules to existing adaptive_trade_idea_framework_settings
        if 'adaptive_trade_idea_framework_settings' not in config:
            config['adaptive_trade_idea_framework_settings'] = {}

        # Define comprehensive strategy rules
        config['adaptive_trade_idea_framework_settings']['strategy_rules'] = {
            'high_conviction_bullish': {
                'conditions': {
                    'min_conviction': 4.0,
                    'bias': 'Bullish',
                    'vapi_fa_z': {'min': 0.5},
                    'market_regime': ['BULLISH_MOMENTUM', 'BULLISH_REVERSAL', 'NEUTRAL_BULLISH']
                },
                'strategies': [
                    {
                        'name': 'Long Call Spread',
                        'type': 'directional',
                        'risk_level': 'medium',
                        'expected_return': 0.15,
                        'max_risk': 0.05
                    },
                    {
                        'name': 'Bull Put Spread',
                        'type': 'income',
                        'risk_level': 'low',
                        'expected_return': 0.08,
                        'max_risk': 0.03
                    }
                ]
            },
            'high_conviction_bearish': {
                'conditions': {
                    'min_conviction': 4.0,
                    'bias': 'Bearish',
                    'vapi_fa_z': {'max': -0.5},
                    'market_regime': ['BEARISH_MOMENTUM', 'BEARISH_REVERSAL', 'NEUTRAL_BEARISH']
                },
                'strategies': [
                    {
                        'name': 'Long Put Spread',
                        'type': 'directional',
                        'risk_level': 'medium',
                        'expected_return': 0.15,
                        'max_risk': 0.05
                    },
                    {
                        'name': 'Bear Call Spread',
                        'type': 'income',
                        'risk_level': 'low',
                        'expected_return': 0.08,
                        'max_risk': 0.03
                    }
                ]
            },
            'medium_conviction': {
                'conditions': {
                    'min_conviction': 2.5,
                    'max_conviction': 3.9
                },
                'strategies': [
                    {
                        'name': 'Iron Condor',
                        'type': 'neutral',
                        'risk_level': 'low',
                        'expected_return': 0.06,
                        'max_risk': 0.04
                    },
                    {
                        'name': 'Straddle',
                        'type': 'volatility',
                        'risk_level': 'medium',
                        'expected_return': 0.12,
                        'max_risk': 0.08
                    }
                ]
            },
            'volatility_expansion': {
                'conditions': {
                    'min_conviction': 3.0,
                    'vri_2_0': {'min': 1.0},
                    'tw_laf_z': {'min': 1.5}
                },
                'strategies': [
                    {
                        'name': 'Long Straddle',
                        'type': 'volatility',
                        'risk_level': 'high',
                        'expected_return': 0.20,
                        'max_risk': 0.10
                    },
                    {
                        'name': 'Long Strangle',
                        'type': 'volatility',
                        'risk_level': 'medium',
                        'expected_return': 0.15,
                        'max_risk': 0.08
                    }
                ]
            }
        }
        
        # Add fallback strategy for unmatched high conviction
        config['adaptive_trade_idea_framework_settings']['fallback_strategy'] = {
            'name': 'Conservative Directional',
            'type': 'directional',
            'risk_level': 'low',
            'expected_return': 0.10,
            'max_risk': 0.03,
            'description': 'Default strategy for high conviction scenarios'
        }
        
        # Write updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info("✅ ATIF strategy rules updated successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing ATIF strategy rules: {str(e)}")
        return False

def test_signal_generation():
    """Test signal generation with sample data."""
    logger.info("🧪 Testing signal generation...")
    
    try:
        from core_analytics_engine.signal_generator_v2_5 import SignalGeneratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from data_models.eots_schemas_v2_5 import ProcessedUnderlyingAggregatesV2_5
        from datetime import datetime
        
        # Initialize components
        config_manager = ConfigManagerV2_5()
        signal_generator = SignalGeneratorV2_5(config_manager)
        
        # Create sample data that should generate signals
        sample_data = ProcessedUnderlyingAggregatesV2_5(
            symbol="SPY",
            timestamp=datetime.now(),
            vapi_fa_z_score_und=1.2,  # Above threshold
            dwfd_z_score_und=-0.8,
            tw_laf_z_score_und=1.8,   # Above threshold
            gib_oi_based_und=0.3,
            a_dag_und=0.7,
            vri_2_0_und=1.1,          # Above threshold
            current_market_regime_v2_5="BULLISH_MOMENTUM"
        )
        
        # Generate signals
        signals = signal_generator.generate_signals(sample_data)
        
        logger.info(f"✅ Signal generation test completed")
        logger.info(f"   - Total signals generated: {len(signals)}")
        
        for signal in signals:
            logger.info(f"   - {signal.signal_type}: {signal.signal_name} (confidence: {signal.confidence:.2f})")
        
        return len(signals) > 0
        
    except Exception as e:
        logger.error(f"❌ Signal generation test failed: {str(e)}")
        return False

def main():
    """Main function to fix signal generation issues."""
    logger.info("🚀 Starting Signal Generation Issues Fix for EOTS v2.5...")
    
    try:
        # Analyze current issues
        issues = analyze_signal_generation_issues()
        
        if issues:
            logger.warning("⚠️ Issues found:")
            for issue in issues:
                logger.warning(f"   - {issue}")
        else:
            logger.info("✅ No obvious configuration issues found")
        
        # Fix signal thresholds
        if not fix_signal_thresholds():
            logger.error("❌ Failed to fix signal thresholds")
            return False
        
        # Fix ATIF strategy rules
        if not fix_atif_strategy_rules():
            logger.error("❌ Failed to fix ATIF strategy rules")
            return False
        
        # Test signal generation
        if test_signal_generation():
            logger.info("✅ Signal generation test passed")
        else:
            logger.warning("⚠️ Signal generation test failed - may need manual review")
        
        logger.info("🎉 Signal generation issues fix completed!")
        logger.info("✅ The system should now generate signals and trade directives")
        logger.info("🔄 Please restart the EOTS system to apply changes")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Signal generation fix failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("✨ Signal generation fix completed successfully!")
    else:
        logger.error("❌ Signal generation fix failed")
    sys.exit(0 if success else 1)
