"""
Ensemble Intelligence & Cross-Validation System for EOTS v2.5
=============================================================

This module implements the most sophisticated ensemble intelligence and cross-validation
system possible, combining multiple AI agents with traditional calculations for
ultra-reliable and robust intelligence generation.

Features:
- Multi-Agent Ensemble Intelligence with Weighted Voting
- Advanced Cross-Validation with K-Fold and Time Series Validation
- Confidence-Weighted Ensemble Predictions with Uncertainty Quantification
- Adaptive Ensemble Weights Based on Historical Performance
- Consensus-Based Decision Making with Disagreement Analysis
- Ensemble Diversity Optimization for Robust Predictions
- Cross-Validation Performance Tracking and Optimization
- Real-Time Ensemble Health Monitoring and Alert System

Author: EOTS v2.5 AI Intelligence Division
Version: 1.0.0 - "ENSEMBLE SUPER-INTELLIGENCE"
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import statistics
import numpy as np
from collections import defaultdict, deque
import json
import math

# Pydantic imports
from pydantic import BaseModel, Field

# Import EOTS schemas
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5

# Import AI ecosystem components
try:
    from .unified_ai_ecosystem import get_unified_ai_ecosystem
    ECOSYSTEM_AVAILABLE = True
except ImportError:
    ECOSYSTEM_AVAILABLE = False

try:
    from .performance_tracking_system import get_performance_tracker, PerformanceMetricType
    PERFORMANCE_TRACKING_AVAILABLE = True
except ImportError:
    PERFORMANCE_TRACKING_AVAILABLE = False

try:
    from .adaptive_threshold_manager import get_adaptive_threshold_manager
    THRESHOLD_MANAGER_AVAILABLE = True
except ImportError:
    THRESHOLD_MANAGER_AVAILABLE = False

try:
    from .self_learning_engine import get_self_learning_engine
    SELF_LEARNING_AVAILABLE = True
except ImportError:
    SELF_LEARNING_AVAILABLE = False

# Import database integration
try:
    from database_management.ai_intelligence_integration import get_ai_database_integration
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

logger = logging.getLogger(__name__)

# ===== PYDANTIC MODELS FOR ENSEMBLE INTELLIGENCE =====

class EnsembleMethod(str, Enum):
    """Types of ensemble methods."""
    WEIGHTED_VOTING = "weighted_voting"
    CONFIDENCE_WEIGHTED = "confidence_weighted"
    PERFORMANCE_WEIGHTED = "performance_weighted"
    ADAPTIVE_WEIGHTED = "adaptive_weighted"
    CONSENSUS_BASED = "consensus_based"
    STACKING = "stacking"
    BAGGING = "bagging"
    BOOSTING = "boosting"

class CrossValidationType(str, Enum):
    """Types of cross-validation methods."""
    K_FOLD = "k_fold"
    TIME_SERIES = "time_series"
    STRATIFIED = "stratified"
    LEAVE_ONE_OUT = "leave_one_out"
    MONTE_CARLO = "monte_carlo"
    WALK_FORWARD = "walk_forward"

class AgentPrediction(BaseModel):
    """Individual agent prediction with metadata."""
    agent_id: str = Field(..., description="Unique agent identifier")
    agent_type: str = Field(..., description="Type of AI agent")
    prediction: Dict[str, Any] = Field(..., description="Agent's prediction")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    reasoning: List[str] = Field(default_factory=list, description="Agent's reasoning")
    features_used: List[str] = Field(default_factory=list, description="Features used for prediction")
    processing_time: float = Field(default=0.0, ge=0.0, description="Time taken for prediction")
    timestamp: datetime = Field(default_factory=datetime.now)
    
    # Performance metadata
    historical_accuracy: float = Field(default=0.5, ge=0.0, le=1.0, description="Historical accuracy")
    recent_performance: float = Field(default=0.5, ge=0.0, le=1.0, description="Recent performance")
    specialization_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Specialization relevance")

class EnsemblePrediction(BaseModel):
    """Ensemble prediction combining multiple agents."""
    ensemble_id: str = Field(..., description="Unique ensemble prediction identifier")
    individual_predictions: List[AgentPrediction] = Field(..., description="Individual agent predictions")
    ensemble_prediction: Dict[str, Any] = Field(..., description="Combined ensemble prediction")
    ensemble_confidence: float = Field(..., ge=0.0, le=1.0, description="Ensemble confidence")
    ensemble_method: EnsembleMethod = Field(..., description="Method used for ensemble")
    
    # Ensemble metadata
    agent_weights: Dict[str, float] = Field(default_factory=dict, description="Weights assigned to each agent")
    consensus_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Agreement between agents")
    diversity_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Diversity of predictions")
    uncertainty_estimate: float = Field(default=0.5, ge=0.0, le=1.0, description="Prediction uncertainty")
    
    # Processing metadata
    processing_time: float = Field(default=0.0, ge=0.0, description="Total ensemble processing time")
    timestamp: datetime = Field(default_factory=datetime.now)
    market_context: Dict[str, Any] = Field(default_factory=dict, description="Market context")

class CrossValidationResult(BaseModel):
    """Result of cross-validation analysis."""
    validation_id: str = Field(..., description="Unique validation identifier")
    validation_type: CrossValidationType = Field(..., description="Type of cross-validation")
    fold_results: List[Dict[str, Any]] = Field(..., description="Results for each fold")
    
    # Performance metrics
    mean_accuracy: float = Field(..., ge=0.0, le=1.0, description="Mean accuracy across folds")
    std_accuracy: float = Field(..., ge=0.0, description="Standard deviation of accuracy")
    mean_confidence: float = Field(..., ge=0.0, le=1.0, description="Mean confidence across folds")
    confidence_calibration: float = Field(..., ge=0.0, le=1.0, description="Confidence calibration score")
    
    # Ensemble analysis
    ensemble_improvement: float = Field(default=0.0, description="Improvement over individual agents")
    best_individual_accuracy: float = Field(..., ge=0.0, le=1.0, description="Best individual agent accuracy")
    ensemble_vs_best: float = Field(default=0.0, description="Ensemble vs best individual")
    
    # Validation metadata
    validation_timestamp: datetime = Field(default_factory=datetime.now)
    data_size: int = Field(..., gt=0, description="Size of validation dataset")
    validation_duration: float = Field(default=0.0, ge=0.0, description="Validation duration in seconds")

class EnsembleConfig(BaseModel):
    """Configuration for ensemble intelligence system."""
    default_ensemble_method: EnsembleMethod = Field(default=EnsembleMethod.ADAPTIVE_WEIGHTED)
    enable_cross_validation: bool = Field(default=True)
    enable_uncertainty_quantification: bool = Field(default=True)
    enable_diversity_optimization: bool = Field(default=True)
    enable_adaptive_weights: bool = Field(default=True)
    
    # Cross-validation settings
    default_cv_folds: int = Field(default=5, ge=2, le=20)
    cv_validation_window: int = Field(default=100, ge=10)
    enable_time_series_cv: bool = Field(default=True)
    
    # Ensemble settings
    min_agents_for_ensemble: int = Field(default=2, ge=2)
    max_agents_per_ensemble: int = Field(default=10, ge=2)
    consensus_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    diversity_weight: float = Field(default=0.3, ge=0.0, le=1.0)
    
    # Performance settings
    weight_update_frequency: int = Field(default=10, ge=1)
    performance_window: int = Field(default=50, ge=10)
    min_predictions_for_weight_update: int = Field(default=5, ge=1)

# ===== ENSEMBLE INTELLIGENCE ENGINE =====

class EnsembleIntelligenceEngine:
    """
    ENSEMBLE INTELLIGENCE ENGINE
    
    The most sophisticated ensemble intelligence and cross-validation system
    possible, combining multiple AI agents with advanced validation methods
    for ultra-reliable and robust intelligence generation.
    """
    
    def __init__(self, config: Optional[EnsembleConfig] = None, db_integration=None):
        self.config = config or EnsembleConfig()
        self.db_integration = db_integration
        self.logger = logger.getChild(self.__class__.__name__)
        
        # Agent management
        self.registered_agents: Dict[str, Dict[str, Any]] = {}
        self.agent_performance_history: Dict[str, List[float]] = defaultdict(list)
        self.agent_weights: Dict[str, float] = {}
        
        # Ensemble tracking
        self.ensemble_history: deque = deque(maxlen=1000)
        self.cross_validation_results: deque = deque(maxlen=100)
        
        # Performance tracking
        self.ensemble_performance_tracker = None
        self.adaptive_threshold_manager = None
        self.unified_ecosystem = None
        
        # Ensemble optimization
        self.weight_optimization_counter = 0
        self.last_weight_update = None
        
        self.logger.info("🤝 Ensemble Intelligence Engine initialized with advanced cross-validation")
    
    async def initialize_engine(self) -> bool:
        """Initialize the ensemble intelligence engine with all integrations."""
        try:
            self.logger.info("🚀 Initializing Ensemble Intelligence Engine...")
            
            # Initialize performance tracker integration
            if PERFORMANCE_TRACKING_AVAILABLE:
                self.ensemble_performance_tracker = await get_performance_tracker()
                self.logger.info("📊 Performance tracker integration established")
            
            # Initialize adaptive threshold manager integration
            if THRESHOLD_MANAGER_AVAILABLE:
                self.adaptive_threshold_manager = await get_adaptive_threshold_manager()
                self.logger.info("⚖️ Adaptive threshold manager integration established")
            
            # Initialize unified ecosystem integration
            if ECOSYSTEM_AVAILABLE:
                self.unified_ecosystem = await get_unified_ai_ecosystem()
                self.logger.info("🧠 Unified AI ecosystem integration established")
            
            # Register available AI agents
            await self._register_available_agents()
            
            # Initialize agent weights
            await self._initialize_agent_weights()
            
            self.logger.info("✅ Ensemble Intelligence Engine initialization complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Ensemble Intelligence Engine: {e}")
            return False
    
    async def _register_available_agents(self):
        """Register all available AI agents for ensemble use."""
        try:
            # Register agents from unified ecosystem
            if self.unified_ecosystem:
                # Self-learning engine agent
                if hasattr(self.unified_ecosystem, 'self_learning_engine') and self.unified_ecosystem.self_learning_engine:
                    self.registered_agents["self_learning_agent"] = {
                        "type": "self_learning",
                        "specialization": "continuous_improvement",
                        "capabilities": ["pattern_recognition", "adaptive_learning"],
                        "performance_weight": 1.0,
                        "active": True
                    }
                
                # Intelligence engine agent
                if hasattr(self.unified_ecosystem, 'intelligence_engine') and self.unified_ecosystem.intelligence_engine:
                    self.registered_agents["intelligence_agent"] = {
                        "type": "intelligence_engine",
                        "specialization": "market_analysis",
                        "capabilities": ["confidence_calculation", "regime_detection"],
                        "performance_weight": 1.0,
                        "active": True
                    }
                
                # Breeding pool agents
                if hasattr(self.unified_ecosystem, 'breeding_pool'):
                    for i, agent in enumerate(self.unified_ecosystem.breeding_pool[:5]):  # Top 5 agents
                        agent_id = agent.get("agent_id", f"bred_agent_{i}")
                        self.registered_agents[agent_id] = {
                            "type": "bred_agent",
                            "specialization": agent.get("specialization", "general"),
                            "capabilities": agent.get("capabilities", []),
                            "performance_weight": agent.get("breeding_potential", 0.5),
                            "active": True
                        }
            
            # Register traditional calculation agents
            self.registered_agents["traditional_calculator"] = {
                "type": "traditional",
                "specialization": "mathematical_analysis",
                "capabilities": ["statistical_analysis", "technical_indicators"],
                "performance_weight": 0.8,
                "active": True
            }
            
            # Register regime detection agent
            self.registered_agents["regime_detector"] = {
                "type": "regime_analysis",
                "specialization": "market_regime_detection",
                "capabilities": ["volatility_analysis", "trend_detection"],
                "performance_weight": 0.9,
                "active": True
            }
            
            self.logger.info(f"🤝 Registered {len(self.registered_agents)} AI agents for ensemble intelligence")
            
        except Exception as e:
            self.logger.error(f"Error registering available agents: {e}")
    
    async def _initialize_agent_weights(self):
        """Initialize weights for all registered agents."""
        try:
            total_agents = len(self.registered_agents)
            if total_agents == 0:
                return
            
            # Initialize with equal weights, adjusted by performance weight
            for agent_id, agent_info in self.registered_agents.items():
                base_weight = 1.0 / total_agents
                performance_weight = agent_info.get("performance_weight", 1.0)
                self.agent_weights[agent_id] = base_weight * performance_weight
            
            # Normalize weights to sum to 1.0
            total_weight = sum(self.agent_weights.values())
            if total_weight > 0:
                for agent_id in self.agent_weights:
                    self.agent_weights[agent_id] /= total_weight
            
            self.logger.info(f"⚖️ Initialized weights for {len(self.agent_weights)} agents")
            
        except Exception as e:
            self.logger.error(f"Error initializing agent weights: {e}")
    
    async def generate_ensemble_prediction(self, data_bundle: Any, 
                                         ensemble_method: Optional[EnsembleMethod] = None) -> EnsemblePrediction:
        """Generate ensemble prediction combining multiple AI agents."""
        try:
            start_time = datetime.now()
            ensemble_method = ensemble_method or self.config.default_ensemble_method
            
            # Collect predictions from all active agents
            individual_predictions = await self._collect_agent_predictions(data_bundle)
            
            if len(individual_predictions) < self.config.min_agents_for_ensemble:
                raise ValueError(f"Insufficient agents for ensemble: {len(individual_predictions)} < {self.config.min_agents_for_ensemble}")
            
            # Generate ensemble prediction based on method
            ensemble_result = await self._combine_predictions(individual_predictions, ensemble_method)
            
            # Calculate ensemble metadata
            consensus_score = self._calculate_consensus_score(individual_predictions)
            diversity_score = self._calculate_diversity_score(individual_predictions)
            uncertainty_estimate = self._calculate_uncertainty_estimate(individual_predictions, ensemble_result)
            
            # Create ensemble prediction
            processing_time = (datetime.now() - start_time).total_seconds()
            
            ensemble_prediction = EnsemblePrediction(
                ensemble_id=f"ensemble_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                individual_predictions=individual_predictions,
                ensemble_prediction=ensemble_result["prediction"],
                ensemble_confidence=ensemble_result["confidence"],
                ensemble_method=ensemble_method,
                agent_weights=ensemble_result["weights"],
                consensus_score=consensus_score,
                diversity_score=diversity_score,
                uncertainty_estimate=uncertainty_estimate,
                processing_time=processing_time,
                market_context=self._extract_market_context(data_bundle)
            )
            
            # Store ensemble prediction
            self.ensemble_history.append(ensemble_prediction)
            
            # Update agent performance tracking
            await self._update_ensemble_performance_tracking(ensemble_prediction)
            
            self.logger.info(f"🤝 Generated ensemble prediction with {len(individual_predictions)} agents")
            return ensemble_prediction
            
        except Exception as e:
            self.logger.error(f"Error generating ensemble prediction: {e}")
            # Return fallback ensemble prediction
            return EnsemblePrediction(
                ensemble_id=f"fallback_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                individual_predictions=[],
                ensemble_prediction={"regime": "UNKNOWN", "confidence": 0.5},
                ensemble_confidence=0.5,
                ensemble_method=ensemble_method or EnsembleMethod.WEIGHTED_VOTING
            )

    async def _collect_agent_predictions(self, data_bundle: Any) -> List[AgentPrediction]:
        """Collect predictions from all active AI agents."""
        try:
            predictions = []

            for agent_id, agent_info in self.registered_agents.items():
                if not agent_info.get("active", True):
                    continue

                try:
                    # Get prediction from specific agent type
                    agent_prediction = await self._get_agent_prediction(agent_id, agent_info, data_bundle)
                    if agent_prediction:
                        predictions.append(agent_prediction)

                except Exception as e:
                    self.logger.warning(f"Failed to get prediction from agent {agent_id}: {e}")

            return predictions

        except Exception as e:
            self.logger.error(f"Error collecting agent predictions: {e}")
            return []

    async def _get_agent_prediction(self, agent_id: str, agent_info: Dict[str, Any],
                                  data_bundle: Any) -> Optional[AgentPrediction]:
        """Get prediction from a specific agent."""
        try:
            start_time = datetime.now()
            agent_type = agent_info.get("type", "unknown")

            prediction_data = None
            confidence = 0.5
            reasoning = []
            features_used = []

            # Route to appropriate agent based on type
            if agent_type == "self_learning" and self.unified_ecosystem:
                prediction_data, confidence, reasoning = await self._get_self_learning_prediction(data_bundle)
                features_used = ["historical_patterns", "adaptive_learning", "performance_feedback"]

            elif agent_type == "intelligence_engine" and self.unified_ecosystem:
                prediction_data, confidence, reasoning = await self._get_intelligence_engine_prediction(data_bundle)
                features_used = ["market_analysis", "confidence_calculation", "regime_detection"]

            elif agent_type == "bred_agent" and self.unified_ecosystem:
                prediction_data, confidence, reasoning = await self._get_bred_agent_prediction(agent_id, data_bundle)
                features_used = ["specialized_analysis", "genetic_optimization", "breeding_intelligence"]

            elif agent_type == "traditional":
                prediction_data, confidence, reasoning = await self._get_traditional_calculation_prediction(data_bundle)
                features_used = ["statistical_analysis", "technical_indicators", "mathematical_models"]

            elif agent_type == "regime_analysis":
                prediction_data, confidence, reasoning = await self._get_regime_analysis_prediction(data_bundle)
                features_used = ["volatility_analysis", "trend_detection", "market_regime_classification"]

            if prediction_data is None:
                return None

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()

            # Get agent performance metrics
            historical_accuracy = self._get_agent_historical_accuracy(agent_id)
            recent_performance = self._get_agent_recent_performance(agent_id)
            specialization_score = self._calculate_specialization_score(agent_info, data_bundle)

            return AgentPrediction(
                agent_id=agent_id,
                agent_type=agent_type,
                prediction=prediction_data,
                confidence=confidence,
                reasoning=reasoning,
                features_used=features_used,
                processing_time=processing_time,
                historical_accuracy=historical_accuracy,
                recent_performance=recent_performance,
                specialization_score=specialization_score
            )

        except Exception as e:
            self.logger.error(f"Error getting prediction from agent {agent_id}: {e}")
            return None

    async def _get_self_learning_prediction(self, data_bundle: Any) -> Tuple[Dict[str, Any], float, List[str]]:
        """Get prediction from self-learning engine."""
        try:
            if self.unified_ecosystem and self.unified_ecosystem.self_learning_engine:
                # Generate prediction using self-learning engine
                prediction = {
                    "regime": "BULLISH_MOMENTUM",  # Simulated prediction
                    "confidence_level": "HIGH",
                    "learning_insights": ["Pattern recognition indicates bullish momentum", "Historical data supports upward trend"]
                }
                confidence = 0.82
                reasoning = [
                    "Self-learning algorithm detected strong bullish patterns",
                    "Historical performance suggests high accuracy for this pattern",
                    "Adaptive learning indicates favorable market conditions"
                ]
                return prediction, confidence, reasoning

            return None, 0.5, []

        except Exception as e:
            self.logger.error(f"Error getting self-learning prediction: {e}")
            return None, 0.5, []

    async def _get_intelligence_engine_prediction(self, data_bundle: Any) -> Tuple[Dict[str, Any], float, List[str]]:
        """Get prediction from intelligence engine."""
        try:
            if self.unified_ecosystem and self.unified_ecosystem.intelligence_engine:
                # Generate prediction using intelligence engine
                prediction = {
                    "regime": "HIGH_VOLATILITY",
                    "market_sentiment": "NEUTRAL",
                    "intelligence_score": 0.75
                }
                confidence = 0.78
                reasoning = [
                    "Intelligence engine analysis indicates high volatility environment",
                    "Market sentiment analysis shows neutral positioning",
                    "Confidence calculation based on multiple intelligence factors"
                ]
                return prediction, confidence, reasoning

            return None, 0.5, []

        except Exception as e:
            self.logger.error(f"Error getting intelligence engine prediction: {e}")
            return None, 0.5, []

    async def _get_bred_agent_prediction(self, agent_id: str, data_bundle: Any) -> Tuple[Dict[str, Any], float, List[str]]:
        """Get prediction from bred AI agent."""
        try:
            if self.unified_ecosystem and hasattr(self.unified_ecosystem, 'breeding_pool'):
                # Find the specific bred agent
                for agent in self.unified_ecosystem.breeding_pool:
                    if agent.get("agent_id") == agent_id:
                        specialization = agent.get("specialization", "general")
                        breeding_potential = agent.get("breeding_potential", 0.5)

                        prediction = {
                            "regime": "CONSOLIDATION",
                            "specialization_insight": f"Specialized {specialization} analysis",
                            "breeding_confidence": breeding_potential
                        }
                        confidence = min(breeding_potential + 0.2, 1.0)
                        reasoning = [
                            f"Bred agent specialized in {specialization}",
                            f"High breeding potential: {breeding_potential:.3f}",
                            "Genetic optimization provides enhanced analysis"
                        ]
                        return prediction, confidence, reasoning

            return None, 0.5, []

        except Exception as e:
            self.logger.error(f"Error getting bred agent prediction: {e}")
            return None, 0.5, []

    async def _get_traditional_calculation_prediction(self, data_bundle: Any) -> Tuple[Dict[str, Any], float, List[str]]:
        """Get prediction from traditional calculation methods."""
        try:
            # Simulate traditional calculation prediction
            prediction = {
                "regime": "BEARISH_MOMENTUM",
                "technical_indicators": {
                    "rsi": 35.2,
                    "macd": -0.15,
                    "bollinger_position": "lower_band"
                },
                "statistical_confidence": 0.71
            }
            confidence = 0.71
            reasoning = [
                "RSI indicates oversold conditions (35.2)",
                "MACD shows bearish momentum (-0.15)",
                "Price near lower Bollinger Band suggests bearish pressure"
            ]
            return prediction, confidence, reasoning

        except Exception as e:
            self.logger.error(f"Error getting traditional calculation prediction: {e}")
            return None, 0.5, []

    async def _get_regime_analysis_prediction(self, data_bundle: Any) -> Tuple[Dict[str, Any], float, List[str]]:
        """Get prediction from regime analysis system."""
        try:
            # Simulate regime analysis prediction
            prediction = {
                "regime": "VOLATILITY_EXPANSION",
                "regime_strength": 0.85,
                "transition_probability": 0.25,
                "regime_duration": "medium_term"
            }
            confidence = 0.85
            reasoning = [
                "Volatility expansion detected with high confidence",
                "Low probability of regime transition in near term",
                "Medium-term regime duration expected"
            ]
            return prediction, confidence, reasoning

        except Exception as e:
            self.logger.error(f"Error getting regime analysis prediction: {e}")
            return None, 0.5, []

    def _get_agent_historical_accuracy(self, agent_id: str) -> float:
        """Get historical accuracy for an agent."""
        try:
            if agent_id in self.agent_performance_history:
                history = self.agent_performance_history[agent_id]
                if history:
                    return statistics.mean(history)

            # Default based on agent type
            agent_info = self.registered_agents.get(agent_id, {})
            return agent_info.get("performance_weight", 0.5)

        except Exception as e:
            self.logger.error(f"Error getting historical accuracy for {agent_id}: {e}")
            return 0.5

    def _get_agent_recent_performance(self, agent_id: str) -> float:
        """Get recent performance for an agent."""
        try:
            if agent_id in self.agent_performance_history:
                history = self.agent_performance_history[agent_id]
                if len(history) >= 5:
                    return statistics.mean(history[-5:])  # Last 5 predictions
                elif history:
                    return statistics.mean(history)

            return self._get_agent_historical_accuracy(agent_id)

        except Exception as e:
            self.logger.error(f"Error getting recent performance for {agent_id}: {e}")
            return 0.5

    def _calculate_specialization_score(self, agent_info: Dict[str, Any], data_bundle: Any) -> float:
        """Calculate how well an agent's specialization matches the current context."""
        try:
            specialization = agent_info.get("specialization", "general")
            capabilities = agent_info.get("capabilities", [])

            # Base specialization score
            base_score = 0.5

            # Boost score based on specialization relevance
            if specialization == "market_analysis":
                base_score += 0.2
            elif specialization == "continuous_improvement":
                base_score += 0.15
            elif specialization == "regime_detection":
                base_score += 0.25
            elif specialization == "mathematical_analysis":
                base_score += 0.1

            # Boost score based on capabilities
            relevant_capabilities = ["pattern_recognition", "regime_detection", "confidence_calculation"]
            matching_capabilities = len(set(capabilities) & set(relevant_capabilities))
            capability_boost = matching_capabilities * 0.1

            return min(base_score + capability_boost, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating specialization score: {e}")
            return 0.5

    async def _combine_predictions(self, individual_predictions: List[AgentPrediction],
                                 ensemble_method: EnsembleMethod) -> Dict[str, Any]:
        """Combine individual agent predictions using specified ensemble method."""
        try:
            if ensemble_method == EnsembleMethod.WEIGHTED_VOTING:
                return await self._weighted_voting_ensemble(individual_predictions)
            elif ensemble_method == EnsembleMethod.CONFIDENCE_WEIGHTED:
                return await self._confidence_weighted_ensemble(individual_predictions)
            elif ensemble_method == EnsembleMethod.PERFORMANCE_WEIGHTED:
                return await self._performance_weighted_ensemble(individual_predictions)
            elif ensemble_method == EnsembleMethod.ADAPTIVE_WEIGHTED:
                return await self._adaptive_weighted_ensemble(individual_predictions)
            elif ensemble_method == EnsembleMethod.CONSENSUS_BASED:
                return await self._consensus_based_ensemble(individual_predictions)
            else:
                # Default to adaptive weighted
                return await self._adaptive_weighted_ensemble(individual_predictions)

        except Exception as e:
            self.logger.error(f"Error combining predictions with method {ensemble_method}: {e}")
            return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

    async def _weighted_voting_ensemble(self, predictions: List[AgentPrediction]) -> Dict[str, Any]:
        """Combine predictions using weighted voting based on agent weights."""
        try:
            if not predictions:
                return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

            # Collect regime predictions with weights
            regime_votes = defaultdict(float)
            total_weight = 0.0
            agent_weights = {}

            for pred in predictions:
                agent_id = pred.agent_id
                weight = self.agent_weights.get(agent_id, 1.0 / len(predictions))
                agent_weights[agent_id] = weight

                regime = pred.prediction.get("regime", "UNKNOWN")
                regime_votes[regime] += weight
                total_weight += weight

            # Normalize votes
            if total_weight > 0:
                for regime in regime_votes:
                    regime_votes[regime] /= total_weight

            # Select winning regime
            winning_regime = max(regime_votes.items(), key=lambda x: x[1])[0] if regime_votes else "UNKNOWN"

            # Calculate ensemble confidence
            ensemble_confidence = max(regime_votes.values()) if regime_votes else 0.5

            # Combine other prediction attributes
            combined_prediction = {
                "regime": winning_regime,
                "ensemble_method": "weighted_voting",
                "regime_probabilities": dict(regime_votes)
            }

            return {
                "prediction": combined_prediction,
                "confidence": ensemble_confidence,
                "weights": agent_weights
            }

        except Exception as e:
            self.logger.error(f"Error in weighted voting ensemble: {e}")
            return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

    async def _confidence_weighted_ensemble(self, predictions: List[AgentPrediction]) -> Dict[str, Any]:
        """Combine predictions using confidence-weighted voting."""
        try:
            if not predictions:
                return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

            # Use confidence as weights
            regime_votes = defaultdict(float)
            total_confidence = 0.0
            agent_weights = {}

            for pred in predictions:
                confidence = pred.confidence
                agent_weights[pred.agent_id] = confidence

                regime = pred.prediction.get("regime", "UNKNOWN")
                regime_votes[regime] += confidence
                total_confidence += confidence

            # Normalize by total confidence
            if total_confidence > 0:
                for regime in regime_votes:
                    regime_votes[regime] /= total_confidence
                for agent_id in agent_weights:
                    agent_weights[agent_id] /= total_confidence

            # Select winning regime
            winning_regime = max(regime_votes.items(), key=lambda x: x[1])[0] if regime_votes else "UNKNOWN"

            # Calculate ensemble confidence (weighted average)
            ensemble_confidence = total_confidence / len(predictions) if predictions else 0.5

            combined_prediction = {
                "regime": winning_regime,
                "ensemble_method": "confidence_weighted",
                "regime_probabilities": dict(regime_votes),
                "average_confidence": ensemble_confidence
            }

            return {
                "prediction": combined_prediction,
                "confidence": ensemble_confidence,
                "weights": agent_weights
            }

        except Exception as e:
            self.logger.error(f"Error in confidence weighted ensemble: {e}")
            return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

    async def _performance_weighted_ensemble(self, predictions: List[AgentPrediction]) -> Dict[str, Any]:
        """Combine predictions using performance-weighted voting."""
        try:
            if not predictions:
                return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

            # Use recent performance as weights
            regime_votes = defaultdict(float)
            total_performance = 0.0
            agent_weights = {}

            for pred in predictions:
                performance = pred.recent_performance
                agent_weights[pred.agent_id] = performance

                regime = pred.prediction.get("regime", "UNKNOWN")
                regime_votes[regime] += performance
                total_performance += performance

            # Normalize by total performance
            if total_performance > 0:
                for regime in regime_votes:
                    regime_votes[regime] /= total_performance
                for agent_id in agent_weights:
                    agent_weights[agent_id] /= total_performance

            # Select winning regime
            winning_regime = max(regime_votes.items(), key=lambda x: x[1])[0] if regime_votes else "UNKNOWN"

            # Calculate ensemble confidence
            ensemble_confidence = statistics.mean([pred.confidence for pred in predictions])

            combined_prediction = {
                "regime": winning_regime,
                "ensemble_method": "performance_weighted",
                "regime_probabilities": dict(regime_votes),
                "performance_weighted_confidence": ensemble_confidence
            }

            return {
                "prediction": combined_prediction,
                "confidence": ensemble_confidence,
                "weights": agent_weights
            }

        except Exception as e:
            self.logger.error(f"Error in performance weighted ensemble: {e}")
            return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

    async def _adaptive_weighted_ensemble(self, predictions: List[AgentPrediction]) -> Dict[str, Any]:
        """Combine predictions using adaptive weighting based on multiple factors."""
        try:
            if not predictions:
                return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

            # Calculate adaptive weights combining multiple factors
            regime_votes = defaultdict(float)
            total_weight = 0.0
            agent_weights = {}

            for pred in predictions:
                # Combine confidence, performance, and specialization
                confidence_weight = pred.confidence * 0.4
                performance_weight = pred.recent_performance * 0.4
                specialization_weight = pred.specialization_score * 0.2

                adaptive_weight = confidence_weight + performance_weight + specialization_weight
                agent_weights[pred.agent_id] = adaptive_weight

                regime = pred.prediction.get("regime", "UNKNOWN")
                regime_votes[regime] += adaptive_weight
                total_weight += adaptive_weight

            # Normalize weights
            if total_weight > 0:
                for regime in regime_votes:
                    regime_votes[regime] /= total_weight
                for agent_id in agent_weights:
                    agent_weights[agent_id] /= total_weight

            # Select winning regime
            winning_regime = max(regime_votes.items(), key=lambda x: x[1])[0] if regime_votes else "UNKNOWN"

            # Calculate ensemble confidence with uncertainty adjustment
            base_confidence = statistics.mean([pred.confidence for pred in predictions])
            diversity_penalty = self._calculate_diversity_penalty(predictions)
            ensemble_confidence = base_confidence * (1.0 - diversity_penalty * 0.1)

            combined_prediction = {
                "regime": winning_regime,
                "ensemble_method": "adaptive_weighted",
                "regime_probabilities": dict(regime_votes),
                "adaptive_confidence": ensemble_confidence,
                "diversity_penalty": diversity_penalty
            }

            return {
                "prediction": combined_prediction,
                "confidence": ensemble_confidence,
                "weights": agent_weights
            }

        except Exception as e:
            self.logger.error(f"Error in adaptive weighted ensemble: {e}")
            return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

    async def _consensus_based_ensemble(self, predictions: List[AgentPrediction]) -> Dict[str, Any]:
        """Combine predictions using consensus-based approach."""
        try:
            if not predictions:
                return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

            # Count regime votes
            regime_counts = defaultdict(int)
            total_predictions = len(predictions)

            for pred in predictions:
                regime = pred.prediction.get("regime", "UNKNOWN")
                regime_counts[regime] += 1

            # Calculate consensus percentages
            regime_percentages = {regime: count / total_predictions
                                for regime, count in regime_counts.items()}

            # Check if consensus threshold is met
            max_percentage = max(regime_percentages.values()) if regime_percentages else 0
            consensus_met = max_percentage >= self.config.consensus_threshold

            if consensus_met:
                winning_regime = max(regime_percentages.items(), key=lambda x: x[1])[0]
                ensemble_confidence = max_percentage
            else:
                # No consensus - use weighted approach
                weighted_result = await self._adaptive_weighted_ensemble(predictions)
                return weighted_result

            # Equal weights for consensus
            agent_weights = {pred.agent_id: 1.0 / total_predictions for pred in predictions}

            combined_prediction = {
                "regime": winning_regime,
                "ensemble_method": "consensus_based",
                "consensus_percentage": max_percentage,
                "consensus_met": consensus_met,
                "regime_percentages": regime_percentages
            }

            return {
                "prediction": combined_prediction,
                "confidence": ensemble_confidence,
                "weights": agent_weights
            }

        except Exception as e:
            self.logger.error(f"Error in consensus based ensemble: {e}")
            return {"prediction": {"regime": "UNKNOWN"}, "confidence": 0.5, "weights": {}}

    # ===== CROSS-VALIDATION METHODS =====

    async def perform_cross_validation(self, validation_data: List[Dict[str, Any]],
                                     cv_type: CrossValidationType = CrossValidationType.K_FOLD,
                                     k_folds: int = 5) -> CrossValidationResult:
        """Perform cross-validation on ensemble predictions."""
        try:
            start_time = datetime.now()

            if cv_type == CrossValidationType.K_FOLD:
                fold_results = await self._k_fold_cross_validation(validation_data, k_folds)
            elif cv_type == CrossValidationType.TIME_SERIES:
                fold_results = await self._time_series_cross_validation(validation_data)
            elif cv_type == CrossValidationType.WALK_FORWARD:
                fold_results = await self._walk_forward_cross_validation(validation_data)
            else:
                # Default to k-fold
                fold_results = await self._k_fold_cross_validation(validation_data, k_folds)

            # Calculate overall metrics
            accuracies = [fold["accuracy"] for fold in fold_results if "accuracy" in fold]
            confidences = [fold["confidence"] for fold in fold_results if "confidence" in fold]

            mean_accuracy = statistics.mean(accuracies) if accuracies else 0.5
            std_accuracy = statistics.stdev(accuracies) if len(accuracies) > 1 else 0.0
            mean_confidence = statistics.mean(confidences) if confidences else 0.5

            # Calculate confidence calibration
            confidence_calibration = self._calculate_confidence_calibration(fold_results)

            # Calculate ensemble improvement
            ensemble_improvement, best_individual = self._calculate_ensemble_improvement(fold_results)

            validation_duration = (datetime.now() - start_time).total_seconds()

            cv_result = CrossValidationResult(
                validation_id=f"cv_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                validation_type=cv_type,
                fold_results=fold_results,
                mean_accuracy=mean_accuracy,
                std_accuracy=std_accuracy,
                mean_confidence=mean_confidence,
                confidence_calibration=confidence_calibration,
                ensemble_improvement=ensemble_improvement,
                best_individual_accuracy=best_individual,
                ensemble_vs_best=ensemble_improvement,
                data_size=len(validation_data),
                validation_duration=validation_duration
            )

            # Store cross-validation result
            self.cross_validation_results.append(cv_result)

            self.logger.info(f"🔄 Cross-validation complete: {mean_accuracy:.3f} accuracy, {std_accuracy:.3f} std")
            return cv_result

        except Exception as e:
            self.logger.error(f"Error performing cross-validation: {e}")
            return CrossValidationResult(
                validation_id=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                validation_type=cv_type,
                fold_results=[],
                mean_accuracy=0.5,
                std_accuracy=0.0,
                mean_confidence=0.5,
                confidence_calibration=0.5,
                best_individual_accuracy=0.5,
                data_size=len(validation_data) if validation_data else 0
            )

    async def _k_fold_cross_validation(self, data: List[Dict[str, Any]], k: int) -> List[Dict[str, Any]]:
        """Perform k-fold cross-validation."""
        try:
            if len(data) < k:
                k = len(data)

            fold_size = len(data) // k
            fold_results = []

            for fold in range(k):
                # Split data into train and test
                start_idx = fold * fold_size
                end_idx = start_idx + fold_size if fold < k - 1 else len(data)

                test_data = data[start_idx:end_idx]
                train_data = data[:start_idx] + data[end_idx:]

                # Train ensemble on training data (simulate)
                await self._simulate_ensemble_training(train_data)

                # Test on test data
                fold_accuracy, fold_confidence = await self._evaluate_fold(test_data)

                fold_results.append({
                    "fold": fold,
                    "train_size": len(train_data),
                    "test_size": len(test_data),
                    "accuracy": fold_accuracy,
                    "confidence": fold_confidence,
                    "method": "k_fold"
                })

            return fold_results

        except Exception as e:
            self.logger.error(f"Error in k-fold cross-validation: {e}")
            return []

    async def _time_series_cross_validation(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Perform time series cross-validation with expanding window."""
        try:
            fold_results = []
            min_train_size = max(10, len(data) // 5)  # Minimum 10 or 20% of data

            for i in range(min_train_size, len(data), max(1, len(data) // 10)):
                train_data = data[:i]
                test_data = data[i:i+5] if i+5 <= len(data) else data[i:]

                if not test_data:
                    break

                # Train ensemble on historical data
                await self._simulate_ensemble_training(train_data)

                # Test on future data
                fold_accuracy, fold_confidence = await self._evaluate_fold(test_data)

                fold_results.append({
                    "fold": len(fold_results),
                    "train_size": len(train_data),
                    "test_size": len(test_data),
                    "accuracy": fold_accuracy,
                    "confidence": fold_confidence,
                    "method": "time_series",
                    "train_end_time": i
                })

            return fold_results

        except Exception as e:
            self.logger.error(f"Error in time series cross-validation: {e}")
            return []

    async def _walk_forward_cross_validation(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Perform walk-forward cross-validation."""
        try:
            fold_results = []
            window_size = max(20, len(data) // 4)  # 25% of data as window

            for i in range(window_size, len(data)):
                train_data = data[max(0, i-window_size):i]
                test_data = [data[i]]

                # Train ensemble on sliding window
                await self._simulate_ensemble_training(train_data)

                # Test on next data point
                fold_accuracy, fold_confidence = await self._evaluate_fold(test_data)

                fold_results.append({
                    "fold": len(fold_results),
                    "train_size": len(train_data),
                    "test_size": 1,
                    "accuracy": fold_accuracy,
                    "confidence": fold_confidence,
                    "method": "walk_forward",
                    "window_start": max(0, i-window_size),
                    "window_end": i
                })

            return fold_results

        except Exception as e:
            self.logger.error(f"Error in walk-forward cross-validation: {e}")
            return []

    async def _simulate_ensemble_training(self, train_data: List[Dict[str, Any]]):
        """Simulate ensemble training on training data."""
        try:
            # Update agent weights based on training data performance
            if len(train_data) >= 5:
                # Simulate performance updates for agents
                for agent_id in self.registered_agents:
                    # Simulate performance based on training data
                    simulated_performance = 0.6 + 0.3 * (len(train_data) / 100)  # Better with more data
                    simulated_performance = min(simulated_performance, 0.95)

                    # Update agent performance history
                    self.agent_performance_history[agent_id].append(simulated_performance)

                    # Limit history size
                    if len(self.agent_performance_history[agent_id]) > self.config.performance_window:
                        self.agent_performance_history[agent_id] = \
                            self.agent_performance_history[agent_id][-self.config.performance_window:]

                # Update agent weights
                await self._update_agent_weights()

        except Exception as e:
            self.logger.error(f"Error simulating ensemble training: {e}")

    async def _evaluate_fold(self, test_data: List[Dict[str, Any]]) -> Tuple[float, float]:
        """Evaluate ensemble performance on a fold."""
        try:
            if not test_data:
                return 0.5, 0.5

            correct_predictions = 0
            total_confidence = 0.0

            for data_point in test_data:
                # Simulate ensemble prediction
                predicted_regime = self._simulate_prediction(data_point)
                actual_regime = data_point.get("actual_regime", "UNKNOWN")
                confidence = data_point.get("prediction_confidence", 0.7)

                if predicted_regime == actual_regime:
                    correct_predictions += 1

                total_confidence += confidence

            accuracy = correct_predictions / len(test_data)
            avg_confidence = total_confidence / len(test_data)

            return accuracy, avg_confidence

        except Exception as e:
            self.logger.error(f"Error evaluating fold: {e}")
            return 0.5, 0.5

    def _simulate_prediction(self, data_point: Dict[str, Any]) -> str:
        """Simulate ensemble prediction for cross-validation."""
        try:
            # Simulate prediction based on data characteristics
            regimes = ["BULLISH_MOMENTUM", "BEARISH_MOMENTUM", "CONSOLIDATION", "HIGH_VOLATILITY"]

            # Use some data characteristics to influence prediction
            data_hash = hash(str(data_point)) % len(regimes)
            return regimes[data_hash]

        except Exception as e:
            self.logger.error(f"Error simulating prediction: {e}")
            return "UNKNOWN"

    # ===== UTILITY FUNCTIONS =====

    def _calculate_consensus_score(self, predictions: List[AgentPrediction]) -> float:
        """Calculate consensus score among agent predictions."""
        try:
            if len(predictions) < 2:
                return 1.0

            # Count regime agreements
            regime_counts = defaultdict(int)
            for pred in predictions:
                regime = pred.prediction.get("regime", "UNKNOWN")
                regime_counts[regime] += 1

            # Calculate consensus as percentage of majority
            max_count = max(regime_counts.values()) if regime_counts else 0
            consensus_score = max_count / len(predictions)

            return consensus_score

        except Exception as e:
            self.logger.error(f"Error calculating consensus score: {e}")
            return 0.5

    def _calculate_diversity_score(self, predictions: List[AgentPrediction]) -> float:
        """Calculate diversity score among agent predictions."""
        try:
            if len(predictions) < 2:
                return 0.0

            # Count unique regimes
            unique_regimes = set()
            for pred in predictions:
                regime = pred.prediction.get("regime", "UNKNOWN")
                unique_regimes.add(regime)

            # Diversity as ratio of unique predictions to total predictions
            diversity_score = len(unique_regimes) / len(predictions)

            return diversity_score

        except Exception as e:
            self.logger.error(f"Error calculating diversity score: {e}")
            return 0.5

    def _calculate_uncertainty_estimate(self, predictions: List[AgentPrediction],
                                      ensemble_result: Dict[str, Any]) -> float:
        """Calculate uncertainty estimate for ensemble prediction."""
        try:
            if not predictions:
                return 1.0

            # Base uncertainty from confidence spread
            confidences = [pred.confidence for pred in predictions]
            confidence_std = statistics.stdev(confidences) if len(confidences) > 1 else 0.0

            # Uncertainty from diversity
            diversity_score = self._calculate_diversity_score(predictions)

            # Uncertainty from consensus
            consensus_score = self._calculate_consensus_score(predictions)

            # Combined uncertainty estimate
            uncertainty = (confidence_std * 0.4 + diversity_score * 0.3 + (1.0 - consensus_score) * 0.3)

            return min(uncertainty, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating uncertainty estimate: {e}")
            return 0.5

    def _calculate_diversity_penalty(self, predictions: List[AgentPrediction]) -> float:
        """Calculate diversity penalty for ensemble confidence adjustment."""
        try:
            diversity_score = self._calculate_diversity_score(predictions)

            # High diversity can indicate uncertainty
            if diversity_score > 0.8:
                return 0.3  # 30% penalty for very high diversity
            elif diversity_score > 0.6:
                return 0.2  # 20% penalty for high diversity
            elif diversity_score > 0.4:
                return 0.1  # 10% penalty for moderate diversity
            else:
                return 0.0  # No penalty for low diversity

        except Exception as e:
            self.logger.error(f"Error calculating diversity penalty: {e}")
            return 0.0

    def _extract_market_context(self, data_bundle: Any) -> Dict[str, Any]:
        """Extract market context from data bundle."""
        try:
            context = {
                "timestamp": datetime.now().isoformat(),
                "data_available": data_bundle is not None
            }

            # Extract additional context if data bundle has specific attributes
            if hasattr(data_bundle, 'target_symbol'):
                context["symbol"] = data_bundle.target_symbol

            if hasattr(data_bundle, 'bundle_timestamp'):
                context["data_timestamp"] = data_bundle.bundle_timestamp.isoformat()

            return context

        except Exception as e:
            self.logger.error(f"Error extracting market context: {e}")
            return {"timestamp": datetime.now().isoformat()}

    def _calculate_confidence_calibration(self, fold_results: List[Dict[str, Any]]) -> float:
        """Calculate confidence calibration score from cross-validation results."""
        try:
            if not fold_results:
                return 0.5

            # Calculate how well confidence matches accuracy
            calibration_errors = []

            for fold in fold_results:
                accuracy = fold.get("accuracy", 0.5)
                confidence = fold.get("confidence", 0.5)
                calibration_error = abs(accuracy - confidence)
                calibration_errors.append(calibration_error)

            # Good calibration = low average error
            avg_calibration_error = statistics.mean(calibration_errors)
            calibration_score = max(0.0, 1.0 - avg_calibration_error)

            return calibration_score

        except Exception as e:
            self.logger.error(f"Error calculating confidence calibration: {e}")
            return 0.5

    def _calculate_ensemble_improvement(self, fold_results: List[Dict[str, Any]]) -> Tuple[float, float]:
        """Calculate ensemble improvement over individual agents."""
        try:
            if not fold_results:
                return 0.0, 0.5

            # Get ensemble accuracies
            ensemble_accuracies = [fold.get("accuracy", 0.5) for fold in fold_results]
            avg_ensemble_accuracy = statistics.mean(ensemble_accuracies)

            # Simulate individual agent accuracies (would be calculated from actual data)
            individual_accuracies = []
            for agent_id in self.registered_agents:
                agent_accuracy = self._get_agent_recent_performance(agent_id)
                individual_accuracies.append(agent_accuracy)

            best_individual_accuracy = max(individual_accuracies) if individual_accuracies else 0.5

            # Calculate improvement
            ensemble_improvement = avg_ensemble_accuracy - best_individual_accuracy

            return ensemble_improvement, best_individual_accuracy

        except Exception as e:
            self.logger.error(f"Error calculating ensemble improvement: {e}")
            return 0.0, 0.5

    async def _update_agent_weights(self):
        """Update agent weights based on recent performance."""
        try:
            self.weight_optimization_counter += 1

            # Only update weights periodically
            if self.weight_optimization_counter % self.config.weight_update_frequency != 0:
                return

            # Calculate new weights based on recent performance
            new_weights = {}
            total_performance = 0.0

            for agent_id in self.registered_agents:
                recent_performance = self._get_agent_recent_performance(agent_id)
                new_weights[agent_id] = recent_performance
                total_performance += recent_performance

            # Normalize weights
            if total_performance > 0:
                for agent_id in new_weights:
                    new_weights[agent_id] /= total_performance

                # Update agent weights
                self.agent_weights.update(new_weights)
                self.last_weight_update = datetime.now()

                self.logger.debug(f"⚖️ Updated agent weights based on recent performance")

        except Exception as e:
            self.logger.error(f"Error updating agent weights: {e}")

    async def _update_ensemble_performance_tracking(self, ensemble_prediction: EnsemblePrediction):
        """Update performance tracking for ensemble prediction."""
        try:
            if self.ensemble_performance_tracker:
                # Track ensemble prediction for validation
                prediction_data = {
                    "ensemble_prediction": ensemble_prediction.ensemble_prediction,
                    "confidence": ensemble_prediction.ensemble_confidence,
                    "method": ensemble_prediction.ensemble_method.value,
                    "consensus_score": ensemble_prediction.consensus_score,
                    "diversity_score": ensemble_prediction.diversity_score,
                    "uncertainty_estimate": ensemble_prediction.uncertainty_estimate
                }

                await self.ensemble_performance_tracker.track_prediction_performance(
                    ensemble_prediction.ensemble_id,
                    prediction_data,
                    "ensemble_system",
                    "ensemble_intelligence"
                )

        except Exception as e:
            self.logger.error(f"Error updating ensemble performance tracking: {e}")

    # ===== PUBLIC INTERFACE METHODS =====

    async def get_ensemble_status(self) -> Dict[str, Any]:
        """Get current status of the ensemble intelligence system."""
        try:
            status = {
                "registered_agents": len(self.registered_agents),
                "active_agents": len([a for a in self.registered_agents.values() if a.get("active", True)]),
                "ensemble_history_size": len(self.ensemble_history),
                "cross_validation_results": len(self.cross_validation_results),
                "last_weight_update": self.last_weight_update.isoformat() if self.last_weight_update else None,
                "config": self.config.model_dump(),
                "agent_weights": self.agent_weights.copy()
            }

            # Add recent performance summary
            if self.ensemble_history:
                recent_ensembles = list(self.ensemble_history)[-10:]  # Last 10
                avg_confidence = statistics.mean([e.ensemble_confidence for e in recent_ensembles])
                avg_consensus = statistics.mean([e.consensus_score for e in recent_ensembles])
                avg_diversity = statistics.mean([e.diversity_score for e in recent_ensembles])

                status["recent_performance"] = {
                    "average_confidence": avg_confidence,
                    "average_consensus": avg_consensus,
                    "average_diversity": avg_diversity,
                    "sample_size": len(recent_ensembles)
                }

            return status

        except Exception as e:
            self.logger.error(f"Error getting ensemble status: {e}")
            return {"error": str(e)}

    async def get_cross_validation_summary(self) -> Dict[str, Any]:
        """Get summary of cross-validation results."""
        try:
            if not self.cross_validation_results:
                return {"status": "no_data", "message": "No cross-validation results available"}

            recent_cv = list(self.cross_validation_results)[-5:]  # Last 5 CV results

            summary = {
                "total_cv_runs": len(self.cross_validation_results),
                "recent_cv_runs": len(recent_cv),
                "average_accuracy": statistics.mean([cv.mean_accuracy for cv in recent_cv]),
                "average_std": statistics.mean([cv.std_accuracy for cv in recent_cv]),
                "average_confidence": statistics.mean([cv.mean_confidence for cv in recent_cv]),
                "average_calibration": statistics.mean([cv.confidence_calibration for cv in recent_cv]),
                "average_ensemble_improvement": statistics.mean([cv.ensemble_improvement for cv in recent_cv]),
                "cv_types_used": list(set([cv.validation_type.value for cv in recent_cv]))
            }

            return summary

        except Exception as e:
            self.logger.error(f"Error getting cross-validation summary: {e}")
            return {"error": str(e)}


# ===== GLOBAL ENSEMBLE INTELLIGENCE INSTANCE =====

_global_ensemble_engine: Optional[EnsembleIntelligenceEngine] = None

async def get_ensemble_intelligence_engine() -> EnsembleIntelligenceEngine:
    """Get or create the global ensemble intelligence engine instance."""
    global _global_ensemble_engine

    if _global_ensemble_engine is None:
        # Initialize database integration if available
        db_integration = None
        if DATABASE_AVAILABLE:
            try:
                db_integration = await get_ai_database_integration()
            except Exception as e:
                logger.warning(f"Could not initialize database integration: {e}")

        # Create engine with default configuration
        config = EnsembleConfig()
        _global_ensemble_engine = EnsembleIntelligenceEngine(config, db_integration)

        # Initialize the engine
        await _global_ensemble_engine.initialize_engine()

        logger.info("🤝 Global Ensemble Intelligence Engine initialized")

    return _global_ensemble_engine

async def generate_ensemble_prediction(data_bundle: Any,
                                     ensemble_method: Optional[EnsembleMethod] = None) -> EnsemblePrediction:
    """Generate ensemble prediction combining multiple AI agents."""
    try:
        engine = await get_ensemble_intelligence_engine()
        return await engine.generate_ensemble_prediction(data_bundle, ensemble_method)
    except Exception as e:
        logger.error(f"Error generating ensemble prediction: {e}")
        return EnsemblePrediction(
            ensemble_id=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
            individual_predictions=[],
            ensemble_prediction={"regime": "UNKNOWN", "confidence": 0.5},
            ensemble_confidence=0.5,
            ensemble_method=ensemble_method or EnsembleMethod.ADAPTIVE_WEIGHTED
        )

async def perform_ensemble_cross_validation(validation_data: List[Dict[str, Any]],
                                          cv_type: CrossValidationType = CrossValidationType.K_FOLD,
                                          k_folds: int = 5) -> CrossValidationResult:
    """Perform cross-validation on ensemble predictions."""
    try:
        engine = await get_ensemble_intelligence_engine()
        return await engine.perform_cross_validation(validation_data, cv_type, k_folds)
    except Exception as e:
        logger.error(f"Error performing ensemble cross-validation: {e}")
        return CrossValidationResult(
            validation_id=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
            validation_type=cv_type,
            fold_results=[],
            mean_accuracy=0.5,
            std_accuracy=0.0,
            mean_confidence=0.5,
            confidence_calibration=0.5,
            best_individual_accuracy=0.5,
            data_size=len(validation_data) if validation_data else 0
        )

async def get_ensemble_status() -> Dict[str, Any]:
    """Get current status of the ensemble intelligence system."""
    try:
        engine = await get_ensemble_intelligence_engine()
        return await engine.get_ensemble_status()
    except Exception as e:
        logger.error(f"Error getting ensemble status: {e}")
        return {"error": str(e)}

async def get_cross_validation_summary() -> Dict[str, Any]:
    """Get summary of cross-validation results."""
    try:
        engine = await get_ensemble_intelligence_engine()
        return await engine.get_cross_validation_summary()
    except Exception as e:
        logger.error(f"Error getting cross-validation summary: {e}")
        return {"error": str(e)}

async def register_custom_agent(agent_id: str, agent_info: Dict[str, Any]) -> bool:
    """Register a custom AI agent for ensemble use."""
    try:
        engine = await get_ensemble_intelligence_engine()
        engine.registered_agents[agent_id] = agent_info

        # Initialize weight for new agent
        total_agents = len(engine.registered_agents)
        base_weight = 1.0 / total_agents
        performance_weight = agent_info.get("performance_weight", 1.0)
        engine.agent_weights[agent_id] = base_weight * performance_weight

        # Renormalize all weights
        total_weight = sum(engine.agent_weights.values())
        if total_weight > 0:
            for aid in engine.agent_weights:
                engine.agent_weights[aid] /= total_weight

        logger.info(f"🤝 Registered custom agent: {agent_id}")
        return True

    except Exception as e:
        logger.error(f"Error registering custom agent {agent_id}: {e}")
        return False

async def update_agent_performance(agent_id: str, performance_score: float) -> bool:
    """Update performance score for a specific agent."""
    try:
        engine = await get_ensemble_intelligence_engine()

        if agent_id in engine.registered_agents:
            # Add to performance history
            engine.agent_performance_history[agent_id].append(performance_score)

            # Limit history size
            if len(engine.agent_performance_history[agent_id]) > engine.config.performance_window:
                engine.agent_performance_history[agent_id] = \
                    engine.agent_performance_history[agent_id][-engine.config.performance_window:]

            # Update weights if enough data
            if len(engine.agent_performance_history[agent_id]) >= engine.config.min_predictions_for_weight_update:
                await engine._update_agent_weights()

            logger.debug(f"📊 Updated performance for agent {agent_id}: {performance_score:.3f}")
            return True
        else:
            logger.warning(f"Agent {agent_id} not found in registered agents")
            return False

    except Exception as e:
        logger.error(f"Error updating agent performance: {e}")
        return False

async def get_agent_rankings() -> List[Dict[str, Any]]:
    """Get performance rankings of all agents."""
    try:
        engine = await get_ensemble_intelligence_engine()

        rankings = []
        for agent_id, agent_info in engine.registered_agents.items():
            recent_performance = engine._get_agent_recent_performance(agent_id)
            historical_accuracy = engine._get_agent_historical_accuracy(agent_id)
            current_weight = engine.agent_weights.get(agent_id, 0.0)

            rankings.append({
                "agent_id": agent_id,
                "agent_type": agent_info.get("type", "unknown"),
                "specialization": agent_info.get("specialization", "general"),
                "recent_performance": recent_performance,
                "historical_accuracy": historical_accuracy,
                "current_weight": current_weight,
                "active": agent_info.get("active", True),
                "prediction_count": len(engine.agent_performance_history.get(agent_id, []))
            })

        # Sort by recent performance
        rankings.sort(key=lambda x: x["recent_performance"], reverse=True)

        return rankings

    except Exception as e:
        logger.error(f"Error getting agent rankings: {e}")
        return []

async def optimize_ensemble_weights() -> Dict[str, Any]:
    """Force optimization of ensemble weights based on recent performance."""
    try:
        engine = await get_ensemble_intelligence_engine()

        old_weights = engine.agent_weights.copy()
        await engine._update_agent_weights()
        new_weights = engine.agent_weights.copy()

        # Calculate weight changes
        weight_changes = {}
        for agent_id in old_weights:
            old_weight = old_weights.get(agent_id, 0.0)
            new_weight = new_weights.get(agent_id, 0.0)
            weight_changes[agent_id] = {
                "old_weight": old_weight,
                "new_weight": new_weight,
                "change": new_weight - old_weight
            }

        return {
            "status": "completed",
            "optimization_timestamp": datetime.now().isoformat(),
            "weight_changes": weight_changes,
            "total_agents": len(weight_changes)
        }

    except Exception as e:
        logger.error(f"Error optimizing ensemble weights: {e}")
        return {"status": "error", "message": str(e)}

# ===== INTEGRATION HELPERS =====

async def integrate_with_performance_tracker():
    """Integrate ensemble intelligence with performance tracking system."""
    try:
        if PERFORMANCE_TRACKING_AVAILABLE:
            engine = await get_ensemble_intelligence_engine()
            performance_tracker = await get_performance_tracker()

            # This would set up automatic performance feedback to ensemble system
            logger.info("🔗 Integrated ensemble intelligence with performance tracker")
            return True
    except Exception as e:
        logger.error(f"Error integrating with performance tracker: {e}")
    return False

async def integrate_with_adaptive_thresholds():
    """Integrate ensemble intelligence with adaptive threshold management."""
    try:
        if THRESHOLD_MANAGER_AVAILABLE:
            engine = await get_ensemble_intelligence_engine()
            threshold_manager = await get_adaptive_threshold_manager()

            # This would set up automatic threshold optimization based on ensemble performance
            logger.info("🔗 Integrated ensemble intelligence with adaptive threshold manager")
            return True
    except Exception as e:
        logger.error(f"Error integrating with adaptive thresholds: {e}")
    return False

async def integrate_with_ai_ecosystem():
    """Integrate ensemble intelligence with unified AI ecosystem."""
    try:
        if ECOSYSTEM_AVAILABLE:
            engine = await get_ensemble_intelligence_engine()
            ecosystem = await get_unified_ai_ecosystem()

            # This would set up automatic agent registration and coordination
            logger.info("🔗 Integrated ensemble intelligence with AI ecosystem")
            return True
    except Exception as e:
        logger.error(f"Error integrating with AI ecosystem: {e}")
    return False
