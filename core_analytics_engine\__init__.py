"""Core Analytics Engine Package for EOTS v2.5 - CONSOLIDATED & PYDANTIC-FIRST"""

# Import core analytics modules (validated and active)
from .adaptive_trade_idea_framework_v2_5 import AdaptiveTradeIdeaFrameworkV2_5
from .its_orchestrator_v2_5 import ITSOrchestratorV2_5
from .key_level_identifier_v2_5 import KeyLevelIdentifierV2_5
from .market_regime_engine_v2_5 import MarketRegimeEngineV2_5
from .metrics_calculator_v2_5 import MetricsCalculatorV2_5
from .recommendation_logic_v2_5 import RecommendationGeneratorV2_5
from .signal_generator_v2_5 import SignalGeneratorV2_5
from .ticker_context_analyzer_v2_5 import TickerContextAnalyzerV2_5
from .trade_parameter_optimizer_v2_5 import TradeParameterOptimizerV2_5

# Import AI and intelligence modules (Pydantic-first)
from .unified_ai_intelligence_system_v2_5 import (
    UnifiedAIIntelligenceSystemV2_5,
    UnifiedIntelligenceAnalysis,
    UnifiedLearningResult,
    get_unified_ai_intelligence_system,
    generate_unified_intelligence_for_bundle,
    run_unified_learning_for_symbol
)
from .adaptive_learning_integration_v2_5 import (
    AdaptiveLearningIntegrationV2_5,
    get_adaptive_learning_integration,
    run_daily_unified_learning,
    run_weekly_unified_learning
)
from .news_intelligence_engine_v2_5 import NewsIntelligenceEngineV2_5
from .ai_predictions_manager_v2_5 import AIPredictionsManagerV2_5
from .mcp_unified_manager_v2_5 import MCPUnifiedManagerV2_5
from .pydantic_ai_atif_engine_v2_5 import PydanticAIATIFEngineV2_5

__all__ = [
    # Core Analytics Components
    'AdaptiveTradeIdeaFrameworkV2_5',
    'ITSOrchestratorV2_5',
    'KeyLevelIdentifierV2_5',
    'MarketRegimeEngineV2_5',
    'MetricsCalculatorV2_5',
    'RecommendationGeneratorV2_5',
    'SignalGeneratorV2_5',
    'TickerContextAnalyzerV2_5',
    'TradeParameterOptimizerV2_5',

    # Unified AI Intelligence System
    'UnifiedAIIntelligenceSystemV2_5',
    'UnifiedIntelligenceAnalysis',
    'UnifiedLearningResult',
    'get_unified_ai_intelligence_system',
    'generate_unified_intelligence_for_bundle',
    'run_unified_learning_for_symbol',

    # Adaptive Learning Integration
    'AdaptiveLearningIntegrationV2_5',
    'get_adaptive_learning_integration',
    'run_daily_unified_learning',
    'run_weekly_unified_learning',

    # Supporting AI Components
    'NewsIntelligenceEngineV2_5',
    'AIPredictionsManagerV2_5',
    'MCPUnifiedManagerV2_5',
    'PydanticAIATIFEngineV2_5'
]